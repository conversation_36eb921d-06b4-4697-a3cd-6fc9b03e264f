"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.eventRegistry = exports.EventHandlerRegistry = exports.BaseEventHandler = void 0;
const logger_1 = require("../core/logger");
class BaseEventHandler {
    constructor(app, eventName) {
        this.once = false;
        this.app = app;
        this.logger = (0, logger_1.createLogger)(`event:${eventName}`);
    }
    handleError(error, context) {
        this.logger.error(`[${this.name}] Event handler error`, {
            error,
            context,
            eventName: this.name,
        });
    }
    logExecution(message, meta) {
        this.logger.debug(`[${this.name}] ${message}`, meta);
    }
    isFeatureEnabled(featureName) {
        const { isFeatureActive } = require('../config');
        return isFeatureActive(featureName);
    }
}
exports.BaseEventHandler = BaseEventHandler;
class EventHandlerRegistry {
    constructor() {
        this.handlers = new Map();
        this.logger = (0, logger_1.createLogger)('event-registry');
    }
    register(handler) {
        if (this.handlers.has(handler.name)) {
            this.logger.warn(`[EventRegistry] Handler already registered: ${handler.name}`);
            return;
        }
        this.handlers.set(handler.name, handler);
        this.logger.debug(`[EventRegistry] Registered handler: ${handler.name}`);
    }
    getHandlers() {
        return Array.from(this.handlers.values());
    }
    getHandler(name) {
        return this.handlers.get(name);
    }
    unregister(name) {
        const removed = this.handlers.delete(name);
        if (removed) {
            this.logger.debug(`[EventRegistry] Unregistered handler: ${name}`);
        }
        return removed;
    }
    clear() {
        this.handlers.clear();
        this.logger.debug('[EventRegistry] Cleared all handlers');
    }
}
exports.EventHandlerRegistry = EventHandlerRegistry;
exports.eventRegistry = new EventHandlerRegistry();
