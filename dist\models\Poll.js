"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Poll = void 0;
const mongoose_1 = require("mongoose");
const pollVoteSchema = new mongoose_1.Schema({
    userId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'User ID must be a valid Discord snowflake'
        }
    },
    optionIndex: {
        type: Number,
        required: true,
        min: [0, 'Option index cannot be negative'],
        max: [19, 'Option index cannot exceed 19']
    },
    voteWeight: {
        type: Number,
        required: true,
        min: [0, 'Vote weight cannot be negative']
    },
    voterDisplayName: {
        type: String,
        required: true,
        maxlength: [100, 'Display name cannot exceed 100 characters']
    },
    voterUsername: {
        type: String,
        required: true,
        maxlength: [50, 'Username cannot exceed 50 characters']
    },
    timestamp: {
        type: Date,
        default: Date.now
    }
}, { _id: false });
const pollSchema = new mongoose_1.Schema({
    pollId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    guildId: {
        type: String,
        required: true,
        index: true,
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    channelId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Channel ID must be a valid Discord snowflake'
        }
    },
    messageId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Message ID must be a valid Discord snowflake'
        }
    },
    title: {
        type: String,
        required: true,
        maxlength: [256, 'Poll title cannot exceed 256 characters'],
        minlength: [1, 'Poll title cannot be empty']
    },
    description: {
        type: String,
        required: true,
        maxlength: [1024, 'Poll description cannot exceed 1024 characters'],
        minlength: [1, 'Poll description cannot be empty']
    },
    createdBy: {
        type: String,
        required: true,
        index: true,
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Creator ID must be a valid Discord snowflake'
        }
    },
    eligibleVoterRoles: [{
            type: String,
            required: true,
            validate: {
                validator: function (v) {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }],
    options: [{
            type: String,
            required: true,
            maxlength: [100, 'Poll option cannot exceed 100 characters'],
            minlength: [1, 'Poll option cannot be empty']
        }],
    votes: [pollVoteSchema],
    totalVotes: {
        type: Number,
        default: 0,
        min: [0, 'Total votes cannot be negative']
    },
    totalVoteWeight: {
        type: Number,
        default: 0,
        min: [0, 'Total vote weight cannot be negative']
    },
    status: {
        type: String,
        enum: ['ACTIVE', 'ENDED', 'CANCELLED'],
        default: 'ACTIVE',
        index: true
    },
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    endedAt: {
        type: Date,
        index: true
    },
    endedBy: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Ended by ID must be a valid Discord snowflake'
        }
    },
    expiresAt: {
        type: Date,
        default: () => new Date(Date.now() + 24 * 60 * 60 * 1000),
        index: { expireAfterSeconds: 0 }
    }
}, {
    timestamps: false
});
pollSchema.index({ guildId: 1, status: 1 });
pollSchema.index({ guildId: 1, createdBy: 1 });
pollSchema.index({ guildId: 1, createdAt: -1 });
pollSchema.index({ status: 1, expiresAt: 1 });
pollSchema.pre('validate', function () {
    if (this.eligibleVoterRoles && this.eligibleVoterRoles.length === 0) {
        this.invalidate('eligibleVoterRoles', 'At least one eligible voter role is required');
    }
    if (this.options && (this.options.length < 2 || this.options.length > 20)) {
        this.invalidate('options', 'Poll must have between 2 and 20 options');
    }
});
pollSchema.methods.addVote = function (vote) {
    this.votes = this.votes.filter((v) => v.userId !== vote.userId);
    this.votes.push(vote);
    this.totalVotes = this.votes.length;
    this.totalVoteWeight = this.votes.reduce((sum, v) => sum + v.voteWeight, 0);
};
pollSchema.methods.removeVote = function (userId) {
    const initialLength = this.votes.length;
    this.votes = this.votes.filter((v) => v.userId !== userId);
    if (this.votes.length < initialLength) {
        this.totalVotes = this.votes.length;
        this.totalVoteWeight = this.votes.reduce((sum, v) => sum + v.voteWeight, 0);
        return true;
    }
    return false;
};
pollSchema.methods.endPoll = function (endedBy) {
    this.status = 'ENDED';
    this.endedAt = new Date();
    this.endedBy = endedBy;
};
pollSchema.methods.getVoteResults = function () {
    const results = new Array(this.options.length).fill(0).map(() => ({
        votes: 0,
        weight: 0,
        percentage: 0,
        weightPercentage: 0
    }));
    this.votes.forEach((vote) => {
        if (vote.optionIndex >= 0 && vote.optionIndex < results.length) {
            results[vote.optionIndex].votes++;
            results[vote.optionIndex].weight += vote.voteWeight;
        }
    });
    results.forEach(result => {
        result.percentage = this.totalVotes > 0 ? (result.votes / this.totalVotes) * 100 : 0;
        result.weightPercentage = this.totalVoteWeight > 0 ? (result.weight / this.totalVoteWeight) * 100 : 0;
    });
    return results;
};
exports.Poll = (0, mongoose_1.model)('Poll', pollSchema);
exports.default = exports.Poll;
