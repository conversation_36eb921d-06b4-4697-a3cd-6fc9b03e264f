"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getHelpState = getHelpState;
exports.setHelpState = setHelpState;
exports.updateHelpState = updateHelpState;
exports.removeHelpState = removeHelpState;
exports.cleanupHelpStates = cleanupHelpStates;
exports.getActiveStateCount = getActiveStateCount;
const helpStates = new Map();
function getHelpState(userId, guildId) {
    const stateKey = `${userId}_${guildId}`;
    return helpStates.get(stateKey);
}
function setHelpState(userId, guildId, state) {
    const stateKey = `${userId}_${guildId}`;
    const fullState = {
        ...state,
        timestamp: Date.now()
    };
    helpStates.set(stateKey, fullState);
}
function updateHelpState(userId, guildId, updates) {
    const stateKey = `${userId}_${guildId}`;
    const existingState = helpStates.get(stateKey);
    if (existingState) {
        const updatedState = {
            ...existingState,
            ...updates,
            timestamp: Date.now()
        };
        helpStates.set(stateKey, updatedState);
    }
}
function removeHelpState(userId, guildId) {
    const stateKey = `${userId}_${guildId}`;
    helpStates.delete(stateKey);
}
function cleanupHelpStates() {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    for (const [key, state] of helpStates.entries()) {
        if (state.timestamp < oneHourAgo) {
            helpStates.delete(key);
        }
    }
    if (helpStates.size > 1000) {
        const sortedStates = Array.from(helpStates.entries())
            .sort((a, b) => a[1].timestamp - b[1].timestamp);
        for (let i = 0; i < 100 && i < sortedStates.length; i++) {
            helpStates.delete(sortedStates[i][0]);
        }
    }
}
function getActiveStateCount() {
    return helpStates.size;
}
setInterval(cleanupHelpStates, 30 * 60 * 1000);
