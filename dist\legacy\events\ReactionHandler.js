"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyReactionHandler = void 0;
const reactionRewardsService_1 = require("../../services/reactionRewardsService");
const milestoneService_1 = require("../../services/milestoneService");
class LegacyReactionHandler {
    constructor(client) {
        this.client = client;
    }
    async handleReactionAdd(reaction, user) {
        try {
            const fullReaction = await this.ensureFullReaction(reaction);
            const fullUser = await this.ensureFullUser(user);
            if (!fullReaction || !fullUser) {
                return;
            }
            if (fullUser.bot)
                return;
            await this.processReactionReward(fullReaction, fullUser);
            await this.trackReactionActivity(fullReaction, fullUser);
        }
        catch (error) {
            console.error('[Reaction Handler] Error in messageReactionAdd handler:', error);
        }
    }
    async ensureFullReaction(reaction) {
        if (reaction.partial) {
            try {
                return await reaction.fetch();
            }
            catch (error) {
                console.error('[Reaction Handler] Failed to fetch reaction:', error);
                return null;
            }
        }
        return reaction;
    }
    async ensureFullUser(user) {
        if (user.partial) {
            try {
                return await user.fetch();
            }
            catch (error) {
                console.error('[Reaction Handler] Failed to fetch user:', error);
                return null;
            }
        }
        return user;
    }
    async processReactionReward(reaction, user) {
        try {
            await (0, reactionRewardsService_1.processReactionReward)(reaction, user);
        }
        catch (error) {
            console.error('[Reaction Handler] Error processing reaction reward:', error);
        }
    }
    async trackReactionActivity(reaction, user) {
        try {
            const milestoneResults = await (0, milestoneService_1.checkAndProcessMilestones)(this.client, user.id, reaction.message.guild?.id || '', 'reaction', {
                channelId: reaction.message.channel.id,
                emoji: reaction.emoji,
                timestamp: new Date()
            });
            if (milestoneResults.length > 0) {
                console.log(`[Milestone] User ${user.tag} achieved ${milestoneResults.length} milestone(s) from reaction activity`);
            }
        }
        catch (error) {
            console.error('[Milestone] Error processing reaction milestones:', error);
        }
    }
    getStats() {
        return {
            handlerType: 'LegacyReactionHandler',
            clientReady: this.client.isReady(),
            guildCount: this.client.guilds.cache.size,
        };
    }
}
exports.LegacyReactionHandler = LegacyReactionHandler;
exports.default = LegacyReactionHandler;
