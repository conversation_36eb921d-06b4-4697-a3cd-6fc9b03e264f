"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const escrowTransactionSchema = new mongoose_1.Schema({
    escrowId: {
        type: String,
        required: [true, 'Escrow ID is required'],
        unique: true,
        index: true
    },
    tradeId: {
        type: String,
        required: [true, 'Trade ID is required'],
        validate: {
            validator: function (v) {
                return v.trim().length > 0;
            },
            message: 'Trade ID cannot be empty'
        },
        index: true
    },
    discordId: {
        type: String,
        required: [true, 'Discord ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Discord ID must be a valid Discord snowflake'
        },
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    amount: {
        type: Number,
        required: [true, 'Amount is required'],
        validate: {
            validator: function (v) {
                return v !== 0 && Number.isInteger(v);
            },
            message: 'Amount must be a non-zero integer'
        }
    },
    transactionType: {
        type: String,
        enum: ['LOCK', 'RELEASE', 'REFUND', 'DISPUTE_HOLD'],
        required: [true, 'Transaction type is required'],
        index: true
    },
    status: {
        type: String,
        enum: ['PENDING', 'COMPLETED', 'FAILED', 'REVERSED'],
        required: [true, 'Status is required'],
        default: 'PENDING',
        index: true
    },
    timestamp: {
        type: Date,
        default: Date.now,
        index: true
    },
    completedAt: {
        type: Date,
        index: true
    },
    relatedTransactionId: {
        type: String,
        index: true
    },
    reversalTransactionId: {
        type: String,
        index: true
    },
    details: {
        type: String,
        maxlength: [500, 'Details cannot exceed 500 characters']
    },
    adminId: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Admin ID must be a valid Discord snowflake'
        },
        index: true
    },
    reason: {
        type: String,
        maxlength: [200, 'Reason cannot exceed 200 characters']
    }
}, {
    timestamps: false
});
escrowTransactionSchema.index({ tradeId: 1, transactionType: 1 });
escrowTransactionSchema.index({ discordId: 1, timestamp: -1 });
escrowTransactionSchema.index({ guildId: 1, timestamp: -1 });
escrowTransactionSchema.index({ status: 1, timestamp: -1 });
escrowTransactionSchema.methods.isCompleted = function () {
    return this.status === 'COMPLETED';
};
escrowTransactionSchema.methods.canBeReversed = function () {
    return this.status === 'COMPLETED' && !this.reversalTransactionId;
};
escrowTransactionSchema.statics.getEscrowBalance = async function (tradeId) {
    const transactions = await this.find({
        tradeId,
        status: 'COMPLETED'
    }).lean();
    let balance = 0;
    for (const tx of transactions) {
        switch (tx.transactionType) {
            case 'LOCK':
            case 'DISPUTE_HOLD':
                balance += Math.abs(tx.amount);
                break;
            case 'RELEASE':
            case 'REFUND':
                balance -= Math.abs(tx.amount);
                break;
        }
    }
    return Math.max(0, balance);
};
escrowTransactionSchema.statics.getUserEscrowedAmount = async function (discordId) {
    const transactions = await this.find({
        discordId,
        status: 'COMPLETED',
        transactionType: { $in: ['LOCK', 'DISPUTE_HOLD'] }
    }).lean();
    let totalEscrowed = 0;
    for (const tx of transactions) {
        const released = await this.findOne({
            tradeId: tx.tradeId,
            status: 'COMPLETED',
            transactionType: { $in: ['RELEASE', 'REFUND'] }
        }).lean();
        if (!released) {
            totalEscrowed += Math.abs(tx.amount);
        }
    }
    return totalEscrowed;
};
exports.default = (0, mongoose_1.model)('EscrowTransaction', escrowTransactionSchema);
