"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const economyService_1 = require("../services/economyService");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
const dynamicPricingService_1 = __importDefault(require("../services/dynamicPricingService"));
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('shop')
        .setDescription('Browse and purchase available roles with your coins'),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        const guildId = interaction.guild?.id;
        const discordId = interaction.user.id;
        if (!guildId) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('Guild information not available.');
        }
        try {
            const user = await (0, economyService_1.ensureUser)(discordId, guildId);
            const currentBalance = user.balance;
            const availableRoles = await User_1.RoleForSale.find({
                guildId,
                roleType: 'shop_purchase'
            }).sort({ price: 1 });
            if (availableRoles.length === 0) {
                const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Role Shop');
                embed.setDescription(`${embedBuilder_1.EMOJIS.MISC.EMPTY} **No roles available for purchase**\n\n` +
                    `The shop is currently empty. Contact an administrator to add purchasable roles.`);
                const navigationRow = (0, embedBuilder_1.createShopNavigationButtons)();
                await interaction.reply({
                    embeds: [embed],
                    components: [navigationRow],
                    ephemeral: false
                });
                return;
            }
            const member = await interaction.guild.members.fetch(discordId);
            const userRoleIds = member.roles.cache.map(role => role.id);
            const priceCalculations = await dynamicPricingService_1.default.calculatePricesForRoles(availableRoles, guildId);
            const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Role Shop');
            const coinName = await configurableConstants_1.default.getCoinName(guildId);
            const formattedBalance = await (0, embedBuilder_1.formatServerCoins)(guildId, currentBalance);
            embed.setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.SHOP} **Welcome to the Role Shop!**\n\n` +
                `Purchase roles using your ${coinName}. Click the "Buy" button next to any role you want to purchase.\n\n` +
                `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **Your Balance:** ${formattedBalance}`);
            const roleFields = [];
            const components = [];
            let buttonCount = 0;
            let currentRow = null;
            for (const roleData of availableRoles) {
                const priceInfo = priceCalculations.get(roleData.roleId);
                if (!priceInfo)
                    continue;
                const formattedPrice = await (0, embedBuilder_1.formatServerCoins)(guildId, priceInfo.displayPrice);
                const hasRole = userRoleIds.includes(roleData.roleId);
                const canAfford = currentBalance >= priceInfo.actualPrice;
                let statusEmoji;
                let statusText;
                let buttonDisabled = false;
                if (hasRole) {
                    statusEmoji = embedBuilder_1.EMOJIS.SUCCESS.CHECK;
                    statusText = '**Already Owned**';
                    buttonDisabled = true;
                }
                else if (canAfford) {
                    statusEmoji = embedBuilder_1.EMOJIS.ECONOMY.COINS;
                    statusText = '**Available**';
                }
                else {
                    statusEmoji = embedBuilder_1.EMOJIS.MISC.LOCK;
                    statusText = '**Insufficient Funds**';
                    buttonDisabled = true;
                }
                let priceDisplay = formattedPrice;
                if (priceInfo.isPercentageBased) {
                    priceDisplay += ` (${priceInfo.percentageValue}% of economy)`;
                }
                let fieldValue = `${statusEmoji} ${statusText} - ${priceDisplay}`;
                if (roleData.description) {
                    fieldValue += `\n*${roleData.description}*`;
                }
                roleFields.push(`**${roleData.name}**\n${fieldValue}`);
                if (buttonCount < 25) {
                    if (buttonCount % 5 === 0) {
                        if (currentRow)
                            components.push(currentRow);
                        currentRow = new discord_js_1.ActionRowBuilder();
                    }
                    const button = new discord_js_1.ButtonBuilder()
                        .setCustomId(`shop_buy_${roleData.roleId}`)
                        .setLabel(hasRole ? 'Owned' : 'Buy')
                        .setEmoji(hasRole ? embedBuilder_1.EMOJIS.SUCCESS.CHECK : embedBuilder_1.EMOJIS.ECONOMY.COINS)
                        .setStyle(hasRole ? discord_js_1.ButtonStyle.Success : (canAfford ? discord_js_1.ButtonStyle.Primary : discord_js_1.ButtonStyle.Secondary))
                        .setDisabled(buttonDisabled);
                    currentRow.addComponents(button);
                    buttonCount++;
                }
            }
            if (currentRow && currentRow.components.length > 0) {
                components.push(currentRow);
            }
            const maxFields = Math.min(roleFields.length, 25);
            for (let i = 0; i < maxFields; i++) {
                embed.addFields({
                    name: `${i + 1}. Role`,
                    value: roleFields[i],
                    inline: true
                });
            }
            if (roleFields.length > 25) {
                embed.addFields({
                    name: '⚠️ Display Limit Reached',
                    value: `Showing first 25 of ${roleFields.length} available roles.`,
                    inline: false
                });
            }
            const navigationRow = (0, embedBuilder_1.createShopNavigationButtons)();
            components.push(navigationRow);
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                components: components,
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(`Failed to load shop: ${error.message}`);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to load shop.');
            }
        }
    })
};
