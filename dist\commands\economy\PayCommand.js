"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const embedBuilder_1 = require("../../utils/embedBuilder");
const errorHandler_1 = require("../../utils/errorHandler");
const constants_1 = require("../../config/constants");
const ValidationUtils_1 = require("../../utils/validation/ValidationUtils");
const mongoose_1 = __importDefault(require("mongoose"));
const User_1 = __importDefault(require("../../models/User"));
const Transaction_1 = __importDefault(require("../../models/Transaction"));
const auditChannelService_1 = __importDefault(require("../../services/auditChannelService"));
class PayCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'pay',
            description: 'Transfer coins to another user',
            category: BaseCommand_1.CommandCategory.ECONOMY,
            requiredFeatures: ['ECONOMY_SYSTEM'],
            cooldown: 5,
        });
    }
    customizeCommand(command) {
        command
            .addUserOption(option => option.setName('user')
            .setDescription('The user to send coins to')
            .setRequired(true))
            .addIntegerOption(option => option.setName('amount')
            .setDescription('Amount of coins to send')
            .setRequired(true)
            .setMinValue(constants_1.VALIDATION.MIN_TRANSACTION_AMOUNT));
    }
    async executeCommand(context) {
        const { interaction } = context;
        const targetUser = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);
        const guildId = interaction.guild?.id;
        if (!guildId) {
            throw new Error('This command can only be used in a server');
        }
        this.validatePayment(interaction.user.id, targetUser.id, amount, targetUser);
        if (amount >= 10000) {
            try {
                ValidationUtils_1.RateLimitValidator.validateRateLimit(interaction.user.id, 'pay_high_value', 60);
            }
            catch (error) {
                if (error instanceof errorHandler_1.ValidationError) {
                    throw new errorHandler_1.RateLimitError(60);
                }
                throw error;
            }
        }
        if (mongoose_1.default.connection.readyState !== 1) {
            throw new errorHandler_1.DatabaseError('Database is not connected. Please try again in a moment.');
        }
        try {
            const guild = interaction.guild;
            if (guild) {
                const targetMember = await guild.members.fetch(targetUser.id).catch(() => null);
                if (!targetMember) {
                    throw new errorHandler_1.ValidationError('The target user is not a member of this server.');
                }
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            this.logger.warn('Could not verify target user membership', {
                targetUserId: targetUser.id,
                guildId: guildId,
                error: error instanceof Error ? error.message : String(error)
            });
        }
        const session = await mongoose_1.default.startSession();
        this.logger.debug('Starting payment transaction', {
            senderId: interaction.user.id,
            recipientId: targetUser.id,
            amount
        });
        try {
            let senderFinalBalance = 0;
            await session.withTransaction(async () => {
                const sender = await User_1.default.findOneAndUpdate({ discordId: interaction.user.id, guildId: guildId }, { $setOnInsert: { discordId: interaction.user.id, guildId: guildId, balance: 0 } }, { upsert: true, new: true, session });
                if (sender.balance < amount) {
                    const formattedBalance = await (0, embedBuilder_1.formatServerCoins)(guildId, sender.balance);
                    const formattedAmount = await (0, embedBuilder_1.formatServerCoins)(guildId, amount);
                    throw new errorHandler_1.ValidationError(`Insufficient balance. You have ${formattedBalance} but need ${formattedAmount}.`);
                }
                if (sender.balance - amount < 0) {
                    throw new errorHandler_1.ValidationError('Transaction would result in negative balance. Please try again.');
                }
                if (amount >= 50000) {
                    this.logger.warn('High-value payment transaction', {
                        senderId: interaction.user.id,
                        senderUsername: interaction.user.username,
                        recipientId: targetUser.id,
                        recipientUsername: targetUser.username,
                        amount,
                        senderBalance: sender.balance,
                        guildId
                    });
                }
                const updatedSender = await User_1.default.findOneAndUpdate({ discordId: interaction.user.id, guildId: guildId }, { $inc: { balance: -amount } }, { session, new: true });
                senderFinalBalance = updatedSender.balance;
                await User_1.default.findOneAndUpdate({ discordId: targetUser.id, guildId: guildId }, {
                    $inc: { balance: amount },
                    $setOnInsert: { discordId: targetUser.id, guildId: guildId }
                }, { upsert: true, session });
                await Transaction_1.default.create([
                    {
                        discordId: interaction.user.id,
                        guildId: guildId,
                        type: 'pay',
                        amount: -amount,
                        details: `Payment to ${targetUser.tag || targetUser.username}`,
                        timestamp: new Date()
                    },
                    {
                        discordId: targetUser.id,
                        guildId: guildId,
                        type: 'pay',
                        amount: amount,
                        details: `Payment from ${interaction.user.tag || interaction.user.username}`,
                        timestamp: new Date()
                    }
                ], { session, ordered: true });
                this.logger.debug('Payment transaction completed successfully', {
                    senderId: interaction.user.id,
                    recipientId: targetUser.id,
                    amount,
                    senderFinalBalance
                });
            });
            const formattedAmount = await (0, embedBuilder_1.formatServerCoins)(guildId, amount);
            const formattedBalance = await (0, embedBuilder_1.formatServerCoins)(guildId, senderFinalBalance);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Payment Sent Successfully!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.TRANSFER} **Payment Completed**\n\n` +
                `${formattedAmount} has been sent to **${targetUser.displayName}**!\n\n` +
                `${embedBuilder_1.EMOJIS.ECONOMY.MONEY} **Your New Balance:** ${formattedBalance}`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ACTIONS.SENDER} From`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ACTIONS.TARGET} To`,
                value: `**${targetUser.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
                value: formattedAmount,
                inline: true
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            this.logger.info(`Payment completed: ${interaction.user.username} sent ${amount} PLC to ${targetUser.username}`);
            try {
                await auditChannelService_1.default.logTransaction(interaction.client, {
                    type: 'pay',
                    executor: interaction.user,
                    target: targetUser,
                    amount: amount,
                    reason: undefined,
                    guildId: guildId,
                    timestamp: new Date()
                });
            }
            catch (auditError) {
                this.logger.warn('Failed to send audit log for pay command', { auditError });
            }
            try {
                const recipientEmbed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Payment Received!');
                recipientEmbed.setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.COINS} You received ${formattedAmount} from **${interaction.user.displayName}**!`);
                await targetUser.send({ embeds: [recipientEmbed] });
            }
            catch (error) {
                this.logger.debug(`Failed to send payment notification DM to ${targetUser.username}`, { error });
            }
        }
        catch (error) {
            this.logger.error('Error executing pay command', {
                error,
                senderId: interaction.user.id,
                recipientId: targetUser.id,
                amount
            });
            throw error;
        }
        finally {
            await session.endSession();
        }
    }
    validatePayment(senderId, recipientId, amount, targetUser) {
        if (!senderId || typeof senderId !== 'string' || senderId.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Invalid sender ID.');
        }
        if (!recipientId || typeof recipientId !== 'string' || recipientId.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Invalid recipient ID.');
        }
        if (senderId === recipientId) {
            throw new errorHandler_1.ValidationError('You cannot send coins to yourself.');
        }
        if (typeof amount !== 'number' || isNaN(amount)) {
            throw new errorHandler_1.ValidationError('Invalid amount. Please enter a valid number.');
        }
        if (amount <= 0) {
            throw new errorHandler_1.ValidationError('Amount must be greater than 0.');
        }
        if (amount < constants_1.VALIDATION.MIN_TRANSACTION_AMOUNT) {
            throw new errorHandler_1.ValidationError(`Amount must be at least ${constants_1.VALIDATION.MIN_TRANSACTION_AMOUNT} coin${constants_1.VALIDATION.MIN_TRANSACTION_AMOUNT === 1 ? '' : 's'}.`);
        }
        if (!Number.isInteger(amount)) {
            throw new errorHandler_1.ValidationError('Amount must be a whole number (no decimals).');
        }
        if (targetUser.bot) {
            throw new errorHandler_1.ValidationError('You cannot send coins to bots.');
        }
        if (!constants_1.VALIDATION.DISCORD_ID_REGEX.test(senderId)) {
            throw new errorHandler_1.ValidationError('Invalid sender Discord ID format.');
        }
        if (!constants_1.VALIDATION.DISCORD_ID_REGEX.test(recipientId)) {
            throw new errorHandler_1.ValidationError('Invalid recipient Discord ID format.');
        }
    }
}
exports.PayCommand = PayCommand;
