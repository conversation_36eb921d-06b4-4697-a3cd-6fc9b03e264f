0 verbose cli /usr/local/bin/node /usr/local/bin/npm
1 info using npm@10.9.2
2 info using node@v22.17.1
3 silly config load:file:/usr/local/lib/node_modules/npm/npmrc
4 silly config load:file:/home/<USER>/.npmrc
5 silly config load:file:/home/<USER>/.npm-global/etc/npmrc
6 verbose title npm run start:prod
7 verbose argv "run" "start:prod"
8 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-08-11T22_57_53_863Z-
9 verbose logfile /home/<USER>/.npm/_logs/2025-08-11T22_57_53_863Z-debug-0.log
10 silly logfile done cleaning log files
11 http fetch GET 200 https://registry.npmjs.org/npm 460ms
