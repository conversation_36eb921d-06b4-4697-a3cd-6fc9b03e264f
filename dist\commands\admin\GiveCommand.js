"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GiveCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const embedBuilder_1 = require("../../utils/embedBuilder");
const economyService_1 = require("../../services/economyService");
const errorHandler_1 = require("../../utils/errorHandler");
const constants_1 = require("../../config/constants");
const BankerPermissions_1 = require("../../utils/permissions/BankerPermissions");
const auditChannelService_1 = __importDefault(require("../../services/auditChannelService"));
class GiveCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'give',
            description: 'Give coins to a user (admin or banker role)',
            category: BaseCommand_1.CommandCategory.ADMIN,
            adminOnly: false,
            requiredFeatures: ['ECONOMY_SYSTEM'],
            requiredPermissions: [],
        });
    }
    customizeCommand(command) {
        command
            .addUserOption(option => option.setName('user')
            .setDescription('The user to give coins to')
            .setRequired(true))
            .addIntegerOption(option => option.setName('amount')
            .setDescription('Amount of coins to give')
            .setRequired(true)
            .setMinValue(constants_1.VALIDATION.MIN_TRANSACTION_AMOUNT)
            .setMaxValue(constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT))
            .addStringOption(option => option.setName('reason')
            .setDescription('Reason for giving coins (optional)')
            .setRequired(false)
            .setMaxLength(constants_1.VALIDATION.MAX_REASON_LENGTH));
    }
    async executeCommand(context) {
        const { interaction } = context;
        await (0, BankerPermissions_1.validateBankerPermissions)(interaction.member);
        const targetUser = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);
        const reason = interaction.options.getString('reason') || 'Administrative action';
        this.validateGive(targetUser.id, amount);
        try {
            const guildId = interaction.guild?.id;
            if (!guildId) {
                throw new Error('This command can only be used in a server');
            }
            await (0, economyService_1.adjustBalance)(targetUser.id, guildId, amount, 'give', `Given by admin ${interaction.user.username} (${interaction.user.id}): ${reason}`, interaction.client);
            const formattedAmount = await (0, embedBuilder_1.formatServerCoins)(guildId, amount);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Coins Awarded Successfully!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.HAMMER} **Administrative Action Completed**\n\n` +
                `${formattedAmount} has been awarded to **${targetUser.displayName}**!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.KEY} Administrator`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ACTIONS.TARGET} Recipient`,
                value: `**${targetUser.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
                value: formattedAmount,
                inline: true
            });
            if (reason !== 'Administrative action') {
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Reason`,
                    value: reason,
                    inline: false
                });
            }
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            this.logger.info(`Admin ${interaction.user.username} gave ${amount} PLC to ${targetUser.username}`, {
                adminId: interaction.user.id,
                recipientId: targetUser.id,
                amount,
                reason,
                guildId: interaction.guild?.id,
            });
            try {
                await auditChannelService_1.default.logTransaction(interaction.client, {
                    type: 'give',
                    executor: interaction.user,
                    target: targetUser,
                    amount: amount,
                    reason: reason !== 'Administrative action' ? reason : undefined,
                    guildId: guildId,
                    timestamp: new Date()
                });
            }
            catch (auditError) {
                this.logger.warn('Failed to send audit log for give command', { auditError });
            }
            try {
                const recipientEmbed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Coins Received!');
                recipientEmbed.setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.COINS} You received ${formattedAmount} from an administrator!\n\n` +
                    `**Reason:** ${reason}`);
                await targetUser.send({ embeds: [recipientEmbed] });
            }
            catch (error) {
                this.logger.debug(`Failed to send give notification DM to ${targetUser.username}`, { error });
            }
        }
        catch (error) {
            this.logger.error('Error executing give command', {
                error,
                adminId: interaction.user.id,
                recipientId: targetUser.id,
                amount,
                reason
            });
            throw error;
        }
    }
    validateGive(recipientId, amount) {
        if (amount <= 0) {
            throw new errorHandler_1.ValidationError('Amount must be greater than 0.');
        }
        if (amount > constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT) {
            throw new errorHandler_1.ValidationError(`Amount cannot exceed ${constants_1.VALIDATION.MAX_TRANSACTION_AMOUNT.toLocaleString()} coins.`);
        }
    }
}
exports.GiveCommand = GiveCommand;
