0 verbose cli /usr/local/bin/node /usr/local/bin/npm
1 info using npm@10.9.2
2 info using node@v22.17.1
3 silly config load:file:/usr/local/lib/node_modules/npm/npmrc
4 silly config load:file:/home/<USER>/.npmrc
5 silly config load:file:/home/<USER>/.npm-global/etc/npmrc
6 verbose title npm run start:prod
7 verbose argv "run" "start:prod"
8 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-08-02T23_45_59_954Z-
9 verbose logfile /home/<USER>/.npm/_logs/2025-08-02T23_45_59_954Z-debug-0.log
10 silly logfile done cleaning log files
11 http fetch GET 200 https://registry.npmjs.org/npm 588ms
12 verbose stack Error: command failed
12 verbose stack     at promiseSpawn (/usr/local/lib/node_modules/npm/node_modules/@npmcli/promise-spawn/lib/index.js:22:22)
12 verbose stack     at spawnWithShell (/usr/local/lib/node_modules/npm/node_modules/@npmcli/promise-spawn/lib/index.js:124:10)
12 verbose stack     at promiseSpawn (/usr/local/lib/node_modules/npm/node_modules/@npmcli/promise-spawn/lib/index.js:12:12)
12 verbose stack     at runScriptPkg (/usr/local/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/run-script-pkg.js:77:13)
12 verbose stack     at runScript (/usr/local/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/run-script.js:9:12)
12 verbose stack     at #run (/usr/local/lib/node_modules/npm/lib/commands/run-script.js:131:13)
12 verbose stack     at async RunScript.exec (/usr/local/lib/node_modules/npm/lib/commands/run-script.js:40:7)
12 verbose stack     at async Npm.exec (/usr/local/lib/node_modules/npm/lib/npm.js:207:9)
12 verbose stack     at async module.exports (/usr/local/lib/node_modules/npm/lib/cli/entry.js:74:5)
13 verbose pkgid economy-bot@2.0.0
14 error path /home/<USER>
15 error command failed
16 error signal SIGTERM
17 error command sh -c NODE_ENV=production DISCLOUD=true node dist/main.js
18 verbose cwd /home/<USER>
19 verbose os Linux 5.15.0-130-generic
20 verbose node v22.17.1
21 verbose npm  v10.9.2
22 notice
22 notice New [31mmajor[39m version of npm available! [31m10.9.2[39m -> [34m11.5.2[39m
22 notice Changelog: [34mhttps://github.com/npm/cli/releases/tag/v11.5.2[39m
22 notice To update run: [4mnpm install -g npm@11.5.2[24m
22 notice  { force: true, [Symbol(proc-log.meta)]: true }
23 verbose exit 1
24 verbose code 1
25 error A complete log of this run can be found in: /home/<USER>/.npm/_logs/2025-08-02T23_45_59_954Z-debug-0.log
