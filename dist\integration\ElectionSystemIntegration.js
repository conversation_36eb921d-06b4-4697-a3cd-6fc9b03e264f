"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElectionSystemIntegration = void 0;
const WealthElectionService_1 = require("../services/election/WealthElectionService");
const electionButtonHandler_1 = require("../handlers/electionButtonHandler");
const ElectionMonitoring_1 = require("../monitoring/ElectionMonitoring");
const ElectionErrorRecovery_1 = require("../resilience/ElectionErrorRecovery");
const ElectionRateLimiter_1 = require("../security/ElectionRateLimiter");
const ElectionIndexes_1 = require("../database/ElectionIndexes");
const ElectionQueryOptimizer_1 = require("../utils/ElectionQueryOptimizer");
class ElectionSystemIntegration {
    constructor(app, config = {}) {
        this.app = app;
        this.initialized = false;
        this.config = {
            enableMonitoring: true,
            enableRateLimiting: true,
            enableErrorRecovery: true,
            enableQueryOptimization: true,
            createIndexes: true,
            monitoringInterval: 30000,
            ...config
        };
    }
    async initialize() {
        if (this.initialized) {
            this.app.logger.warn('[ElectionSystemIntegration] System already initialized');
            return;
        }
        this.app.logger.info('[ElectionSystemIntegration] Initializing election system...');
        try {
            await this.initializeMonitoring();
            await this.initializeErrorRecovery();
            await this.initializeRateLimiting();
            await this.initializeQueryOptimization();
            await this.initializeDatabaseOptimizations();
            await this.initializeCoreServices();
            await this.initializeHandlers();
            this.registerServices();
            this.setupEventHandlers();
            await this.validateSystemHealth();
            this.initialized = true;
            this.app.logger.info('[ElectionSystemIntegration] Election system initialization complete');
        }
        catch (error) {
            this.app.logger.error('[ElectionSystemIntegration] Failed to initialize election system', { error });
            await this.cleanup();
            throw error;
        }
    }
    async initializeMonitoring() {
        if (!this.config.enableMonitoring) {
            this.app.logger.info('[ElectionSystemIntegration] Monitoring disabled');
            return;
        }
        this.app.logger.info('[ElectionSystemIntegration] Initializing monitoring service...');
        this.monitoring = new ElectionMonitoring_1.ElectionMonitoringService(this.app.logger);
        this.monitoring.on('critical_alert', (alert) => {
            this.app.logger.error('[ElectionSystemIntegration] Critical alert received', alert);
        });
        this.monitoring.on('alert', (alert) => {
            this.app.logger.warn('[ElectionSystemIntegration] Alert received', alert);
        });
    }
    async initializeErrorRecovery() {
        if (!this.config.enableErrorRecovery) {
            this.app.logger.info('[ElectionSystemIntegration] Error recovery disabled');
            return;
        }
        this.app.logger.info('[ElectionSystemIntegration] Initializing error recovery service...');
        this.errorRecovery = new ElectionErrorRecovery_1.ElectionErrorRecoveryService(this.app.logger, this.monitoring);
        this.errorRecovery.on('emergency_mode_enabled', (data) => {
            this.app.logger.error('[ElectionSystemIntegration] Emergency mode enabled', data);
        });
        this.errorRecovery.on('emergency_mode_disabled', (data) => {
            this.app.logger.info('[ElectionSystemIntegration] Emergency mode disabled', data);
        });
        this.errorRecovery.on('circuit_breaker_opened', (data) => {
            this.app.logger.warn('[ElectionSystemIntegration] Circuit breaker opened', data);
        });
    }
    async initializeRateLimiting() {
        if (!this.config.enableRateLimiting) {
            this.app.logger.info('[ElectionSystemIntegration] Rate limiting disabled');
            return;
        }
        this.app.logger.info('[ElectionSystemIntegration] Initializing rate limiting service...');
        this.rateLimiter = new ElectionRateLimiter_1.ElectionRateLimiterService(this.app.logger, this.monitoring);
        this.rateLimiter.on('rate_limit_exceeded', (data) => {
            this.app.logger.warn('[ElectionSystemIntegration] Rate limit exceeded', data);
        });
        this.rateLimiter.on('abuse_detected', (data) => {
            this.app.logger.error('[ElectionSystemIntegration] Abuse pattern detected', data);
        });
        this.rateLimiter.on('abuse_report', (data) => {
            this.app.logger.error('[ElectionSystemIntegration] Abuse report generated', data);
        });
    }
    async initializeQueryOptimization() {
        if (!this.config.enableQueryOptimization) {
            this.app.logger.info('[ElectionSystemIntegration] Query optimization disabled');
            return;
        }
        this.app.logger.info('[ElectionSystemIntegration] Initializing query optimization...');
        this.queryOptimizer = new ElectionQueryOptimizer_1.ElectionQueryOptimizer(this.app.logger);
    }
    async initializeDatabaseOptimizations() {
        if (!this.config.createIndexes) {
            this.app.logger.info('[ElectionSystemIntegration] Database index creation disabled');
            return;
        }
        this.app.logger.info('[ElectionSystemIntegration] Initializing database optimizations...');
        this.indexManager = new ElectionIndexes_1.ElectionIndexManager(this.app.logger);
        try {
            await this.indexManager.createElectionIndexes();
            this.app.logger.info('[ElectionSystemIntegration] Database indexes created successfully');
        }
        catch (error) {
            this.app.logger.warn('[ElectionSystemIntegration] Failed to create some indexes', { error });
        }
    }
    async initializeCoreServices() {
        this.app.logger.info('[ElectionSystemIntegration] Initializing core election services...');
        this.wealthElectionService = new WealthElectionService_1.WealthElectionService(this.app);
        await this.wealthElectionService.onInitialize();
    }
    async initializeHandlers() {
        this.app.logger.info('[ElectionSystemIntegration] Initializing election handlers...');
        this.buttonHandler = new electionButtonHandler_1.ElectionButtonHandler(this.app);
    }
    registerServices() {
        this.app.logger.info('[ElectionSystemIntegration] Registering services with application context...');
        if (this.wealthElectionService) {
            this.app.setService?.('WealthElectionService', this.wealthElectionService);
        }
        if (this.buttonHandler) {
            this.app.setService?.('ElectionButtonHandler', this.buttonHandler);
        }
        if (this.monitoring) {
            this.app.setService?.('ElectionMonitoringService', this.monitoring);
        }
        if (this.errorRecovery) {
            this.app.setService?.('ElectionErrorRecoveryService', this.errorRecovery);
        }
        if (this.rateLimiter) {
            this.app.setService?.('ElectionRateLimiterService', this.rateLimiter);
        }
        if (this.queryOptimizer) {
            this.app.setService?.('ElectionQueryOptimizer', this.queryOptimizer);
        }
        if (this.indexManager) {
            this.app.setService?.('ElectionIndexManager', this.indexManager);
        }
    }
    setupEventHandlers() {
        this.app.logger.info('[ElectionSystemIntegration] Setting up inter-service event handlers...');
        if (this.monitoring && this.errorRecovery) {
            this.monitoring.on('critical_alert', (alert) => {
            });
        }
        if (this.rateLimiter && this.monitoring) {
            this.rateLimiter.on('rate_limit_exceeded', (data) => {
                this.monitoring?.recordMetric('rate_limit_violations', 1, data);
            });
            this.rateLimiter.on('abuse_detected', (data) => {
                this.monitoring?.recordMetric('abuse_incidents', 1, data);
            });
        }
        if (this.monitoring) {
            this.monitoring.recordElectionEvent('system_startup', 'system', {
                config: this.config,
                timestamp: Date.now()
            });
        }
    }
    async validateSystemHealth() {
        this.app.logger.info('[ElectionSystemIntegration] Validating system health...');
        const healthChecks = [
            {
                name: 'WealthElectionService',
                check: async () => !!this.wealthElectionService
            },
            {
                name: 'ElectionButtonHandler',
                check: async () => !!this.buttonHandler
            }
        ];
        if (this.config.enableMonitoring) {
            healthChecks.push({
                name: 'MonitoringService',
                check: async () => {
                    const health = this.monitoring?.getHealthSummary();
                    return !!health && health.status !== 'critical';
                }
            });
        }
        if (this.config.enableRateLimiting) {
            healthChecks.push({
                name: 'RateLimiterService',
                check: async () => {
                    const stats = this.rateLimiter?.getSystemStats();
                    return !!stats;
                }
            });
        }
        if (this.config.enableErrorRecovery) {
            healthChecks.push({
                name: 'ErrorRecoveryService',
                check: async () => {
                    const stats = this.errorRecovery?.getRecoveryStats();
                    return !!stats && !this.errorRecovery?.isEmergencyModeEnabled();
                }
            });
        }
        const results = await Promise.allSettled(healthChecks.map(async ({ name, check }) => {
            try {
                const result = await check();
                return { name, healthy: result, error: null };
            }
            catch (error) {
                return { name, healthy: false, error };
            }
        }));
        const healthStatus = {};
        let allHealthy = true;
        results.forEach((result, index) => {
            const { name } = healthChecks[index];
            if (result.status === 'fulfilled') {
                healthStatus[name] = result.value;
                if (!result.value.healthy) {
                    allHealthy = false;
                }
            }
            else {
                healthStatus[name] = { healthy: false, error: result.reason };
                allHealthy = false;
            }
        });
        if (!allHealthy) {
            this.app.logger.error('[ElectionSystemIntegration] System health check failed', healthStatus);
            throw new Error('Election system health validation failed');
        }
        this.app.logger.info('[ElectionSystemIntegration] System health validation passed', healthStatus);
    }
    getSystemStatus() {
        if (!this.initialized) {
            return {
                initialized: false,
                status: 'not_initialized'
            };
        }
        return {
            initialized: true,
            status: 'healthy',
            config: this.config,
            services: {
                wealthElectionService: !!this.wealthElectionService,
                buttonHandler: !!this.buttonHandler,
                monitoring: !!this.monitoring,
                errorRecovery: !!this.errorRecovery,
                rateLimiter: !!this.rateLimiter,
                queryOptimizer: !!this.queryOptimizer,
                indexManager: !!this.indexManager
            },
            health: this.monitoring?.getHealthSummary() || null,
            recoveryStats: this.errorRecovery?.getRecoveryStats() || null,
            rateLimitStats: this.rateLimiter?.getSystemStats() || null,
            cacheStats: this.queryOptimizer?.getCacheStats() || null
        };
    }
    getSystemMetrics() {
        if (!this.initialized || !this.monitoring) {
            return null;
        }
        return {
            health: this.monitoring.getHealthSummary(),
            report: this.monitoring.generateReport(3600000),
            alerts: this.monitoring.getRecentAlerts(50),
            recovery: this.errorRecovery?.getRecoveryHistory(50) || [],
            rateLimiting: this.rateLimiter?.getRecentIncidents(50) || [],
            circuitBreakers: this.errorRecovery?.getCircuitBreakerStatus() || new Map(),
            cachePerformance: this.queryOptimizer?.getCacheStats() || null
        };
    }
    async performMaintenance() {
        this.app.logger.info('[ElectionSystemIntegration] Performing system maintenance...');
        const maintenanceTasks = [];
        if (this.queryOptimizer) {
            maintenanceTasks.push({
                name: 'Cache Cleanup',
                task: async () => {
                    this.queryOptimizer.clearCache();
                    this.app.logger.info('[ElectionSystemIntegration] Cache cleared during maintenance');
                }
            });
        }
        if (this.indexManager) {
            maintenanceTasks.push({
                name: 'Index Analysis',
                task: async () => {
                    await this.indexManager.analyzeIndexPerformance();
                    this.app.logger.info('[ElectionSystemIntegration] Database indexes analyzed');
                }
            });
        }
        if (this.monitoring) {
            maintenanceTasks.push({
                name: 'Health Report',
                task: async () => {
                    const report = this.monitoring.generateReport(86400000);
                    this.app.logger.info('[ElectionSystemIntegration] System health report generated', {
                        summary: {
                            totalMetrics: Object.keys(report.metrics).length,
                            totalAlerts: report.alerts.length,
                            healthStatus: report.health.status
                        }
                    });
                }
            });
        }
        const results = await Promise.allSettled(maintenanceTasks.map(async ({ name, task }) => {
            try {
                await task();
                return { name, success: true, error: null };
            }
            catch (error) {
                return { name, success: false, error };
            }
        }));
        const maintenanceResults = {};
        results.forEach((result, index) => {
            const { name } = maintenanceTasks[index];
            if (result.status === 'fulfilled') {
                maintenanceResults[name] = result.value;
            }
            else {
                maintenanceResults[name] = { success: false, error: result.reason };
            }
        });
        this.app.logger.info('[ElectionSystemIntegration] Maintenance completed', maintenanceResults);
    }
    async shutdown() {
        this.app.logger.info('[ElectionSystemIntegration] Shutting down election system...');
        if (this.monitoring) {
            this.monitoring.stopMonitoring();
        }
        if (this.rateLimiter) {
            this.rateLimiter.shutdown();
        }
        if (this.queryOptimizer) {
            this.queryOptimizer.clearCache();
        }
        this.initialized = false;
        this.app.logger.info('[ElectionSystemIntegration] Election system shutdown complete');
    }
    async cleanup() {
        this.app.logger.info('[ElectionSystemIntegration] Cleaning up after initialization failure...');
        try {
            if (this.monitoring) {
                this.monitoring.stopMonitoring();
            }
            if (this.rateLimiter) {
                this.rateLimiter.shutdown();
            }
            if (this.queryOptimizer) {
                this.queryOptimizer.clearCache();
            }
        }
        catch (error) {
            this.app.logger.error('[ElectionSystemIntegration] Error during cleanup', { error });
        }
    }
    async setEmergencyMode(enabled, reason) {
        if (!this.errorRecovery) {
            throw new Error('Error recovery service not available');
        }
        if (enabled) {
            await this.errorRecovery.enableEmergencyMode(reason);
        }
        else {
            await this.errorRecovery.disableEmergencyMode(reason);
        }
        this.app.logger.info(`[ElectionSystemIntegration] Emergency mode ${enabled ? 'enabled' : 'disabled'}`, { reason });
    }
    getButtonHandler() {
        return this.buttonHandler;
    }
    getWealthElectionService() {
        return this.wealthElectionService;
    }
    isHealthy() {
        if (!this.initialized) {
            return false;
        }
        if (this.errorRecovery?.isEmergencyModeEnabled()) {
            return false;
        }
        if (this.monitoring) {
            const health = this.monitoring.getHealthSummary();
            return health.status !== 'critical';
        }
        return true;
    }
}
exports.ElectionSystemIntegration = ElectionSystemIntegration;
