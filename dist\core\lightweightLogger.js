"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsoleOnlyLogger = exports.LightweightLogger = void 0;
exports.getLogger = getLogger;
exports.createLogger = createLogger;
exports.createWinstonCompatibleLogger = createWinstonCompatibleLogger;
exports.getOptimalLogger = getOptimalLogger;
class LightweightLogger {
    constructor(options = {}) {
        this.logHistory = [];
        this.levelPriority = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
        this.colors = {
            error: '\x1b[31m',
            warn: '\x1b[33m',
            info: '\x1b[36m',
            debug: '\x1b[37m'
        };
        this.reset = '\x1b[0m';
        this.options = {
            level: process.env.LOG_LEVEL || 'info',
            enableColors: process.env.DISABLE_COLORS !== 'true',
            enableTimestamps: true,
            maxLogHistory: process.env.MEMORY_CONSTRAINED_MODE === 'true' ? 50 : 100,
            ...options
        };
    }
    shouldLog(level) {
        return this.levelPriority[level] <= this.levelPriority[this.options.level];
    }
    formatMessage(level, message, context) {
        let formatted = '';
        if (this.options.enableTimestamps) {
            const timestamp = new Date().toISOString();
            formatted += `[${timestamp}] `;
        }
        const levelStr = level.toUpperCase().padEnd(5);
        if (this.options.enableColors) {
            formatted += `${this.colors[level]}${levelStr}${this.reset} `;
        }
        else {
            formatted += `${levelStr} `;
        }
        formatted += message;
        if (context) {
            if (typeof context === 'object') {
                try {
                    formatted += ` ${JSON.stringify(context)}`;
                }
                catch (error) {
                    formatted += ` [Context serialization failed]`;
                }
            }
            else {
                formatted += ` ${context}`;
            }
        }
        return formatted;
    }
    log(level, message, context) {
        if (!this.shouldLog(level)) {
            return;
        }
        const logEntry = {
            level,
            message,
            timestamp: new Date(),
            context
        };
        this.logHistory.push(logEntry);
        if (this.logHistory.length > this.options.maxLogHistory) {
            this.logHistory = this.logHistory.slice(-this.options.maxLogHistory);
        }
        const formatted = this.formatMessage(level, message, context);
        switch (level) {
            case 'error':
                console.error(formatted);
                break;
            case 'warn':
                console.warn(formatted);
                break;
            case 'info':
                console.info(formatted);
                break;
            case 'debug':
                console.debug(formatted);
                break;
        }
    }
    error(message, context) {
        this.log('error', message, context);
    }
    warn(message, context) {
        this.log('warn', message, context);
    }
    info(message, context) {
        this.log('info', message, context);
    }
    debug(message, context) {
        this.log('debug', message, context);
    }
    getHistory() {
        return [...this.logHistory];
    }
    clearHistory() {
        this.logHistory = [];
    }
    getMemoryUsage() {
        const entries = this.logHistory.length;
        const estimatedSizeMB = (entries * 200) / 1024 / 1024;
        return { entries, estimatedSizeMB };
    }
    setLevel(level) {
        this.options.level = level;
    }
    getLevel() {
        return this.options.level;
    }
}
exports.LightweightLogger = LightweightLogger;
let globalLogger = null;
function getLogger() {
    if (!globalLogger) {
        globalLogger = new LightweightLogger();
    }
    return globalLogger;
}
function createLogger(options) {
    return new LightweightLogger(options);
}
function createWinstonCompatibleLogger(name) {
    const logger = new LightweightLogger();
    return {
        error: (message, context) => {
            const prefixedMessage = name ? `[${name}] ${message}` : message;
            logger.error(prefixedMessage, context);
        },
        warn: (message, context) => {
            const prefixedMessage = name ? `[${name}] ${message}` : message;
            logger.warn(prefixedMessage, context);
        },
        info: (message, context) => {
            const prefixedMessage = name ? `[${name}] ${message}` : message;
            logger.info(prefixedMessage, context);
        },
        debug: (message, context) => {
            const prefixedMessage = name ? `[${name}] ${message}` : message;
            logger.debug(prefixedMessage, context);
        }
    };
}
class ConsoleOnlyLogger {
    shouldLog(level) {
        const currentLevel = process.env.LOG_LEVEL || 'info';
        const levelPriority = {
            error: 0, warn: 1, info: 2, debug: 3
        };
        return levelPriority[level] <= levelPriority[currentLevel];
    }
    error(message, context) {
        if (this.shouldLog('error')) {
            console.error(`[ERROR] ${message}`, context || '');
        }
    }
    warn(message, context) {
        if (this.shouldLog('warn')) {
            console.warn(`[WARN] ${message}`, context || '');
        }
    }
    info(message, context) {
        if (this.shouldLog('info')) {
            console.info(`[INFO] ${message}`, context || '');
        }
    }
    debug(message, context) {
        if (this.shouldLog('debug')) {
            console.debug(`[DEBUG] ${message}`, context || '');
        }
    }
}
exports.ConsoleOnlyLogger = ConsoleOnlyLogger;
function getOptimalLogger() {
    if (process.env.MEMORY_CONSTRAINED_MODE === 'true' && process.env.MEMORY_MODE === 'ultra-minimal') {
        return new ConsoleOnlyLogger();
    }
    return getLogger();
}
