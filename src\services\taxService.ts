import { Client, Guild, GuildMember, Role } from 'discord.js';
import { TaxConfiguration } from '../models/TaxConfiguration';
import { RoleForSale } from '../models/User';
import { adjustBalance } from './economyService';
import User from '../models/User';
import { DatabaseError } from '../utils/errorHandler';
import { createErrorEmbed, createServerErrorEmbed, EMOJIS, COLORS, formatServerCoinsWithSymbol } from '../utils/embedBuilder';
import ConfigurableConstants from '../config/configurableConstants';

interface TaxCollectionResult {
    totalProcessed: number;
    totalTaxed: number;
    totalRolesRemoved: number;
    errors: string[];
}

/**
 * Processes tax collection for a specific guild
 */
export async function processTaxCollection(client: Client, guildId: string): Promise<TaxCollectionResult> {
    const result: TaxCollectionResult = {
        totalProcessed: 0,
        totalTaxed: 0,
        totalRolesRemoved: 0,
        errors: []
    };

    try {
        // Get tax configuration for this guild
        const taxConfig = await TaxConfiguration.findOne({ guildId, enabled: true });
        if (!taxConfig) {
            return result; // No tax configuration or disabled
        }

        // Check if it's time to collect taxes
        const now = new Date();
        if (!taxConfig.nextTaxDate || now < taxConfig.nextTaxDate) {
            return result; // Not time yet
        }

        // Get the guild
        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            result.errors.push(`Guild ${guildId} not found`);
            return result;
        }

        // Get the taxed role
        const taxedRole = guild.roles.cache.get(taxConfig.roleId);
        if (!taxedRole) {
            result.errors.push(`Taxed role ${taxConfig.roleId} not found in guild ${guild.name}`);
            return result;
        }

        // Get all members with the taxed role
        await guild.members.fetch(); // Ensure all members are cached
        const membersWithRole = taxedRole.members;

        console.log(`[Tax Collection] Processing ${membersWithRole.size} members with role ${taxedRole.name} in guild ${guild.name}`);

        // Process each member
        for (const [memberId, member] of membersWithRole) {
            try {
                result.totalProcessed++;
                
                // Get user's current balance (guild-scoped)
                const user = await User.findOne({ discordId: memberId, guildId: guild.id });
                const currentBalance = user?.balance || 0;

                // Calculate tax amount based on configuration type
                let taxAmount: number;
                if (taxConfig.taxType === 'percentage') {
                    // Calculate percentage of user's balance
                    taxAmount = Math.floor((currentBalance * (taxConfig.percentageValue || 0)) / 100);
                    // Ensure minimum tax of 1 if user has any balance and percentage > 0
                    if (currentBalance > 0 && (taxConfig.percentageValue || 0) > 0 && taxAmount === 0) {
                        taxAmount = 1;
                    }
                } else {
                    // Use fixed amount
                    taxAmount = taxConfig.amount;
                }

                if (currentBalance >= taxAmount && taxAmount > 0) {
                    // User can afford the tax - deduct it
                    const coinSymbol = await ConfigurableConstants.getCoinSymbol(guild.id);
                    const taxDescription = taxConfig.taxType === 'percentage'
                        ? `Tax collection: ${taxConfig.percentageValue}% (${taxAmount} ${coinSymbol}) for role ${taxedRole.name}`
                        : `Tax collection: ${taxAmount} ${coinSymbol} for role ${taxedRole.name}`;

                    await adjustBalance(
                        memberId,
                        guild.id,
                        -taxAmount,
                        'tax',
                        taxDescription,
                        client
                    );
                    result.totalTaxed++;
                    console.log(`[Tax Collection] Taxed ${taxAmount} ${coinSymbol} from ${member.displayName}`);
                } else {
                    // User cannot afford the tax - remove all purchasable roles and send DM
                    await handleInsufficientFundsForTax(member, taxAmount, currentBalance, taxConfig);
                    result.totalRolesRemoved++;
                    console.log(`[Tax Collection] Removed roles from ${member.displayName} due to insufficient funds`);
                }
            } catch (error) {
                const errorMsg = `Failed to process tax for member ${member.displayName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                result.errors.push(errorMsg);
                console.error(`[Tax Collection] ${errorMsg}`);
            }
        }

        // Update tax configuration with new dates
        taxConfig.lastTaxDate = now;
        taxConfig.nextTaxDate = new Date(now.getTime() + (taxConfig.frequency * 7 * 24 * 60 * 60 * 1000));
        await taxConfig.save();

        console.log(`[Tax Collection] Completed for guild ${guild.name}. Processed: ${result.totalProcessed}, Taxed: ${result.totalTaxed}, Roles Removed: ${result.totalRolesRemoved}`);

    } catch (error) {
        const errorMsg = `Tax collection failed for guild ${guildId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMsg);
        console.error(`[Tax Collection] ${errorMsg}`);
    }

    return result;
}

/**
 * Handles the case where a user cannot afford the tax payment
 */
async function handleInsufficientFundsForTax(member: GuildMember, taxAmount: number, currentBalance: number, taxConfig: any): Promise<void> {
    try {
        // Get all purchasable roles for this guild
        const purchasableRoles = await RoleForSale.find({ guildId: member.guild.id });
        const purchasableRoleIds = new Set(purchasableRoles.map(role => role.roleId));

        // Find roles to remove (intersection of user's roles and purchasable roles)
        const rolesToRemove = member.roles.cache.filter(role => purchasableRoleIds.has(role.id));

        if (rolesToRemove.size > 0) {
            // Remove all purchasable roles
            await member.roles.remove(rolesToRemove);
            
            // Send DM to user explaining what happened
            try {
                const coinSymbol = await ConfigurableConstants.getCoinSymbol(member.guild.id);
                const taxDescription = taxConfig.taxType === 'percentage'
                    ? `**${taxConfig.percentageValue}%** of your balance (**${taxAmount} ${coinSymbol}**)`
                    : `**${taxAmount} ${coinSymbol}**`;

                const embed = await createServerErrorEmbed(member.guild.id, 'Tax Payment Failed - Roles Removed')
                    .then(embed => embed.setDescription(
                        `${EMOJIS.ADMIN.WARNING} **Insufficient Funds for Tax Payment**\n\n` +
                        `You were unable to pay the required tax of ${taxDescription} ` +
                        `(you only had **${currentBalance} ${coinSymbol}**).\n\n` +
                        `As a result, all your purchasable roles have been removed:\n` +
                        `${rolesToRemove.map(role => `• ${role.name}`).join('\n')}\n\n` +
                        `${EMOJIS.ECONOMY.COINS} You can earn more ${coinSymbol} and repurchase these roles when you have sufficient funds.`
                    ));

                await member.send({ embeds: [embed] });
            } catch (dmError) {
                console.error(`[Tax Collection] Failed to send DM to ${member.displayName}:`, dmError);
            }
        }

        // Set balance to 0 if it was negative due to other transactions (guild-scoped)
        if (currentBalance < 0) {
            await adjustBalance(
                member.id,
                member.guild.id,
                -currentBalance,
                'tax',
                'Balance reset to 0 after tax enforcement',
                member.client
            );
        }

    } catch (error) {
        throw new DatabaseError(`Failed to handle insufficient funds for tax: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

/**
 * Gets the next scheduled tax collection date for a guild
 */
export async function getNextTaxDate(guildId: string): Promise<Date | null> {
    try {
        const taxConfig = await TaxConfiguration.findOne({ guildId, enabled: true });
        return taxConfig?.nextTaxDate || null;
    } catch (error) {
        console.error(`[Tax Service] Failed to get next tax date for guild ${guildId}:`, error);
        return null;
    }
}

/**
 * Checks if tax collection is enabled for a guild
 */
export async function isTaxEnabled(guildId: string): Promise<boolean> {
    try {
        const taxConfig = await TaxConfiguration.findOne({ guildId, enabled: true });
        return !!taxConfig;
    } catch (error) {
        console.error(`[Tax Service] Failed to check tax status for guild ${guildId}:`, error);
        return false;
    }
}
