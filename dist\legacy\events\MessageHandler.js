"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyMessageHandler = void 0;
const milestoneService_1 = require("../../services/milestoneService");
class LegacyMessageHandler {
    constructor(client) {
        this.client = client;
    }
    async handleMessageCreate(message) {
        try {
            if (message.author.bot || !message.guild)
                return;
            await this.handleBotMention(message);
            await this.trackMessageActivity(message);
        }
        catch (error) {
            console.error('[Message Handler] Error in messageCreate handler:', error);
        }
    }
    async handleBotMention(message) {
        try {
            const botMentioned = message.mentions.has(this.client.user);
            if (botMentioned) {
                await message.react('✅');
                console.log(`[Message Handler] Bot mentioned in ${message.guild?.name}, reacted with checkmark`);
            }
        }
        catch (error) {
            console.error('[Message Handler] Failed to react to bot mention:', error);
        }
    }
    async trackMessageActivity(message) {
        try {
            const milestoneResults = await (0, milestoneService_1.checkAndProcessMilestones)(this.client, message.author.id, message.guild.id, 'message', {
                channelId: message.channel.id,
                messageContent: message.content,
                messageLength: message.content.length,
                timestamp: message.createdAt
            });
            if (milestoneResults.length > 0) {
                console.log(`[Milestone] User ${message.author.tag} achieved ${milestoneResults.length} milestone(s) from message activity`);
            }
        }
        catch (error) {
            console.error('[Milestone] Error processing message milestones:', error);
        }
    }
    getStats() {
        return {
            handlerType: 'LegacyMessageHandler',
            clientReady: this.client.isReady(),
            guildCount: this.client.guilds.cache.size,
        };
    }
}
exports.LegacyMessageHandler = LegacyMessageHandler;
exports.default = LegacyMessageHandler;
