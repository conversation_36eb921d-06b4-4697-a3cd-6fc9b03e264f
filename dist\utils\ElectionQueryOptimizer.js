"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElectionQueryOptimizer = void 0;
const models_1 = require("../models");
class ElectionQueryOptimizer {
    constructor(logger) {
        this.logger = logger;
        this.cache = {};
        this.DEFAULT_TTL = 30000;
    }
    async getElectionOptimized(electionId) {
        const cacheKey = `election:${electionId}`;
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return cached;
        }
        try {
            const election = await models_1.Election.findOne({ electionId }, {
                electionId: 1,
                guildId: 1,
                channelId: 1,
                title: 1,
                description: 1,
                seats: 1,
                candidateCap: 1,
                status: 1,
                eligibleVoterRoles: 1,
                eligibleCandidateRoles: 1,
                nominationStart: 1,
                nominationEnd: 1,
                votingStart: 1,
                votingEnd: 1,
                snapshotHash: 1,
                snapshotTime: 1,
                createdAt: 1,
                endedAt: 1
            }).lean();
            if (election) {
                this.setCache(cacheKey, election, this.DEFAULT_TTL);
            }
            return election;
        }
        catch (error) {
            this.logger.error('[ElectionQueryOptimizer] Failed to get election', { error, electionId });
            throw error;
        }
    }
    async getActiveCandidatesOptimized(electionId) {
        const cacheKey = `candidates:${electionId}`;
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return cached;
        }
        try {
            const candidates = await models_1.ElectionCandidate.find({
                electionId,
                withdrawn: false,
                disqualified: false
            }, {
                candidateId: 1,
                userId: 1,
                displayName: 1,
                username: 1,
                bio: 1,
                campaignMessage: 1,
                nominationTime: 1
            })
                .sort({ nominationTime: 1 })
                .lean()
                .exec();
            this.setCache(cacheKey, candidates, this.DEFAULT_TTL);
            return candidates;
        }
        catch (error) {
            this.logger.error('[ElectionQueryOptimizer] Failed to get active candidates', { error, electionId });
            throw error;
        }
    }
    async getUserVoteOptimized(electionId, userId) {
        const cacheKey = `vote:${electionId}:${userId}`;
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return cached;
        }
        try {
            const vote = await models_1.ElectionBallot.findOne({
                electionId,
                voterUserId: userId,
                replaced: false
            }, {
                ballotId: 1,
                candidateId: 1,
                weight: 1,
                timestamp: 1
            }).lean();
            if (vote) {
                this.setCache(cacheKey, vote, 15000);
            }
            return vote;
        }
        catch (error) {
            this.logger.error('[ElectionQueryOptimizer] Failed to get user vote', { error, electionId, userId });
            throw error;
        }
    }
    async getElectionResultsOptimized(electionId) {
        const cacheKey = `results:${electionId}`;
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return cached;
        }
        try {
            const resultsAggregation = await models_1.ElectionBallot.aggregate([
                {
                    $match: {
                        electionId,
                        replaced: false
                    }
                },
                {
                    $group: {
                        _id: '$candidateId',
                        voteCount: { $sum: 1 },
                        totalVoteWeight: { $sum: '$weight' }
                    }
                },
                {
                    $lookup: {
                        from: 'electioncandidates',
                        localField: '_id',
                        foreignField: 'candidateId',
                        as: 'candidate',
                        pipeline: [
                            {
                                $match: {
                                    withdrawn: false,
                                    disqualified: false
                                }
                            },
                            {
                                $project: {
                                    userId: 1,
                                    displayName: 1,
                                    username: 1
                                }
                            }
                        ]
                    }
                },
                {
                    $unwind: '$candidate'
                },
                {
                    $project: {
                        userId: '$candidate.userId',
                        displayName: '$candidate.displayName',
                        username: '$candidate.username',
                        voteCount: 1,
                        totalVoteWeight: 1
                    }
                },
                {
                    $sort: {
                        totalVoteWeight: -1,
                        voteCount: -1
                    }
                }
            ]);
            const totalVotes = resultsAggregation.reduce((sum, candidate) => sum + candidate.voteCount, 0);
            const totalVoteWeight = resultsAggregation.reduce((sum, candidate) => sum + candidate.totalVoteWeight, 0);
            const candidates = resultsAggregation.map((candidate, index) => ({
                ...candidate,
                rank: index + 1
            }));
            const results = {
                candidates,
                totalVotes,
                totalVoteWeight
            };
            this.setCache(cacheKey, results, 60000);
            return results;
        }
        catch (error) {
            this.logger.error('[ElectionQueryOptimizer] Failed to get election results', { error, electionId });
            throw error;
        }
    }
    async getVoterSnapshotOptimized(electionId, userId) {
        const cacheKey = `snapshot:${electionId}:${userId}`;
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return cached;
        }
        try {
            const snapshot = await models_1.SnapshotBalance.findOne({ electionId, userId }, { balance: 1, recordedAt: 1 }).lean();
            if (snapshot) {
                this.setCache(cacheKey, snapshot, 300000);
            }
            return snapshot;
        }
        catch (error) {
            this.logger.error('[ElectionQueryOptimizer] Failed to get voter snapshot', { error, electionId, userId });
            throw error;
        }
    }
    async getAuditTrailOptimized(electionId, limit = 50, offset = 0) {
        try {
            const auditLogs = await models_1.ElectionAuditLog.find({ electionId }, {
                actionType: 1,
                actorId: 1,
                targetId: 1,
                details: 1,
                timestamp: 1
            })
                .sort({ timestamp: -1 })
                .limit(limit)
                .skip(offset)
                .lean()
                .exec();
            return auditLogs;
        }
        catch (error) {
            this.logger.error('[ElectionQueryOptimizer] Failed to get audit trail', { error, electionId });
            throw error;
        }
    }
    async getCandidatesBulkOptimized(candidateIds) {
        try {
            const candidateMap = new Map();
            const candidates = await models_1.ElectionCandidate.find({
                candidateId: { $in: candidateIds },
                withdrawn: false,
                disqualified: false
            }, {
                candidateId: 1,
                userId: 1,
                displayName: 1,
                username: 1
            }).lean();
            candidates.forEach(candidate => {
                candidateMap.set(candidate.candidateId, candidate);
            });
            return candidateMap;
        }
        catch (error) {
            this.logger.error('[ElectionQueryOptimizer] Failed to get candidates bulk', { error, candidateIds });
            throw error;
        }
    }
    getFromCache(key) {
        const cached = this.cache[key];
        if (cached && Date.now() < cached.timestamp + cached.ttl) {
            return cached.data;
        }
        if (cached) {
            delete this.cache[key];
        }
        return null;
    }
    setCache(key, data, ttl) {
        this.cache[key] = {
            data,
            timestamp: Date.now(),
            ttl
        };
    }
    clearCache() {
        this.cache = {};
        this.logger.info('[ElectionQueryOptimizer] Cache cleared');
    }
    clearElectionCache(electionId) {
        const keysToDelete = Object.keys(this.cache).filter(key => key.includes(electionId));
        keysToDelete.forEach(key => {
            delete this.cache[key];
        });
        this.logger.info('[ElectionQueryOptimizer] Election cache cleared', { electionId, keysCleared: keysToDelete.length });
    }
    getCacheStats() {
        const now = Date.now();
        const keys = Object.keys(this.cache);
        const activeKeys = keys.filter(key => {
            const cached = this.cache[key];
            return now < cached.timestamp + cached.ttl;
        });
        return {
            totalKeys: keys.length,
            activeKeys: activeKeys.length,
            expiredKeys: keys.length - activeKeys.length,
            memoryUsage: JSON.stringify(this.cache).length
        };
    }
}
exports.ElectionQueryOptimizer = ElectionQueryOptimizer;
