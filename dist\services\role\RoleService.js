"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserAchievementRoles = exports.checkAndAssignRoles = exports.RoleService = void 0;
exports.sendRoleAchievementNotifications = sendRoleAchievementNotifications;
const discord_js_1 = require("discord.js");
const BaseService_1 = require("../base/BaseService");
const features_1 = require("../../config/features");
const User_1 = require("../../models/User");
const embedBuilder_1 = require("../../utils/embedBuilder");
const errorHandler_1 = require("../../utils/errorHandler");
class RoleService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'RoleService';
    }
    async onInitialize() {
        if (!this.isFeatureEnabled('ROLE_AUTOMATION')) {
            throw new Error('Role automation is not enabled');
        }
        this.logger.info('[RoleService] Role automation system initialized');
    }
    async checkAndAssignRoles(client, discordId, guildId, balance) {
        try {
            this.logOperation('Checking role assignments', { discordId, guildId, balance });
            const guild = await client.guilds.fetch(guildId);
            if (!guild) {
                throw new errorHandler_1.DatabaseError(`Guild not found: ${guildId}`);
            }
            const member = await guild.members.fetch(discordId);
            if (!member) {
                throw new errorHandler_1.DatabaseError(`Member not found: ${discordId}`);
            }
            const rolesForSale = await User_1.RoleForSale.find({
                guildId,
                roleType: 'balance_threshold'
            }).sort({ price: 1 });
            if (rolesForSale.length === 0) {
                return null;
            }
            const rolesToAssign = [];
            for (const roleInfo of rolesForSale) {
                if (balance >= roleInfo.price) {
                    if (!member.roles.cache.has(roleInfo.roleId)) {
                        const discordRole = await guild.roles.fetch(roleInfo.roleId);
                        if (discordRole) {
                            rolesToAssign.push({
                                ...roleInfo.toObject(),
                                discordRole
                            });
                        }
                    }
                }
            }
            if (rolesToAssign.length === 0) {
                return null;
            }
            const assignedRoles = [];
            for (const roleInfo of rolesToAssign) {
                try {
                    await member.roles.add(roleInfo.discordRole);
                    const economyService = this.getService('EconomyService');
                    assignedRoles.push({
                        roleId: roleInfo.roleId,
                        roleName: roleInfo.name,
                        price: roleInfo.price,
                        description: roleInfo.description
                    });
                    this.logger.info(`Assigned role ${roleInfo.name} to user ${discordId} in guild ${guildId}`);
                }
                catch (error) {
                    this.handleError(error, {
                        operation: 'role_assignment',
                        roleId: roleInfo.roleId,
                        discordId,
                        guildId
                    });
                }
            }
            if (assignedRoles.length === 0) {
                return null;
            }
            return {
                rolesAssigned: assignedRoles,
                member,
                newBalance: balance
            };
        }
        catch (error) {
            this.handleError(error, { discordId, guildId, balance });
            throw new errorHandler_1.DatabaseError(`Failed to check and assign roles: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getUserAchievementRoles(member) {
        try {
            this.logOperation('Getting user achievement roles', {
                userId: member.user.id,
                guildId: member.guild.id
            });
            const economyService = this.getService('EconomyService');
            const userBalance = 0;
            const rolesForSale = await User_1.RoleForSale.find({
                guildId: member.guild.id,
                roleType: 'balance_threshold'
            }).sort({ price: 1 });
            const currentRoles = [];
            const availableRoles = [];
            for (const roleInfo of rolesForSale) {
                const discordRole = await member.guild.roles.fetch(roleInfo.roleId).catch(() => null);
                if (!discordRole) {
                    continue;
                }
                const roleData = {
                    roleId: roleInfo.roleId,
                    roleName: roleInfo.name,
                    price: roleInfo.price,
                    description: roleInfo.description
                };
                if (member.roles.cache.has(roleInfo.roleId)) {
                    currentRoles.push(roleData);
                }
                else {
                    availableRoles.push({
                        ...roleData,
                        canAfford: userBalance >= roleInfo.price
                    });
                }
            }
            return {
                currentRoles,
                availableRoles,
                userBalance
            };
        }
        catch (error) {
            this.handleError(error, {
                userId: member.user.id,
                guildId: member.guild.id
            });
            throw new errorHandler_1.DatabaseError(`Failed to get user achievement roles: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
exports.RoleService = RoleService;
__decorate([
    (0, features_1.requireFeature)('ROLE_AUTOMATION'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [discord_js_1.Client, String, String, Number]),
    __metadata("design:returntype", Promise)
], RoleService.prototype, "checkAndAssignRoles", null);
__decorate([
    (0, features_1.requireFeature)('ROLE_AUTOMATION'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [discord_js_1.GuildMember]),
    __metadata("design:returntype", Promise)
], RoleService.prototype, "getUserAchievementRoles", null);
async function sendRoleAchievementNotifications(result, client, guildId) {
    try {
        if (result.rolesAssigned.length === 0) {
            return;
        }
        const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, '🎉 Achievement Unlocked!');
        embed.setDescription(`Congratulations! You've automatically unlocked ${result.rolesAssigned.length} new role achievement${result.rolesAssigned.length > 1 ? 's' : ''}!`);
        for (const role of result.rolesAssigned) {
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.SUCCESS.STAR} ${role.roleName}`,
                value: `Required: ${await (0, embedBuilder_1.formatServerCoins)(guildId, role.price)}\n${role.description || 'No description available'}`,
                inline: true
            });
        }
        (0, embedBuilder_1.addUserInfo)(embed, result.member.user);
        try {
            await result.member.send({ embeds: [embed] });
        }
        catch (error) {
            console.log(`Failed to send role achievement DM to ${result.member.displayName}: ${error}`);
        }
    }
    catch (error) {
        console.error('Error sending role achievement notifications:', error);
    }
}
var roleAssignmentService_1 = require("../roleAssignmentService");
Object.defineProperty(exports, "checkAndAssignRoles", { enumerable: true, get: function () { return roleAssignmentService_1.checkAndAssignRoles; } });
Object.defineProperty(exports, "getUserAchievementRoles", { enumerable: true, get: function () { return roleAssignmentService_1.getUserAchievementRoles; } });
