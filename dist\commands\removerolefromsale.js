"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const BankerPermissions_1 = require("../utils/permissions/BankerPermissions");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('removerolefromsale')
        .setDescription('Remove a role from the purchasable shop inventory (Admin/Banker only)')
        .addRoleOption(option => option
        .setName('role')
        .setDescription('The role to remove from the shop')
        .setRequired(true)),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        const role = interaction.options.getRole('role', true);
        const guildId = interaction.guild?.id;
        if (!guildId) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('Guild information not available.');
        }
        if (!interaction.member || !(interaction.member instanceof discord_js_1.GuildMember)) {
            throw new errorHandler_1.ValidationError('Member information not available.');
        }
        const hasPermissions = await (0, BankerPermissions_1.hasBankerPermissions)(interaction.member);
        if (!hasPermissions) {
            throw new errorHandler_1.PermissionError('This command requires administrator permissions or the designated banker role.');
        }
        try {
            const fullRole = interaction.guild.roles.cache.get(role.id) || await interaction.guild.roles.fetch(role.id);
            if (!fullRole) {
                throw new errorHandler_1.ValidationError('Role not found in guild');
            }
            const roleForSale = await User_1.RoleForSale.findOne({
                roleId: fullRole.id,
                guildId: guildId,
                roleType: 'shop_purchase'
            });
            if (!roleForSale) {
                const embed = await (0, embedBuilder_1.createServerErrorEmbed)(guildId, 'Role Not Found in Shop');
                embed.setDescription(`${embedBuilder_1.EMOJIS.ERROR.X} **${fullRole.name}** is not currently available for purchase in the role shop.\n\n` +
                    `Use \`/shop\` to see which roles are currently for sale.`);
                (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            const roleName = roleForSale.name;
            const rolePrice = roleForSale.price;
            const roleDescription = roleForSale.description;
            await User_1.RoleForSale.findOneAndDelete({
                roleId: fullRole.id,
                guildId: guildId,
                roleType: 'shop_purchase'
            });
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Role Removed from Shop!');
            const coinName = await configurableConstants_1.default.getCoinName(guildId);
            const formattedPrice = await (0, embedBuilder_1.formatServerCoins)(guildId, rolePrice);
            embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **${roleName}** has been removed from the role shop!\n\n` +
                `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **Previous Price:** ${formattedPrice}\n` +
                `${embedBuilder_1.EMOJIS.MISC.SCROLL} **Description:** ${roleDescription || 'None'}`);
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} What's Next?`,
                value: [
                    `• Members can no longer purchase this role from the shop`,
                    `• Use \`/setroleforsale\` to add it back to the shop`,
                    `• Use \`/shop\` to view the updated role shop`,
                    `• Existing role holders keep their roles`
                ].join('\n'),
                inline: false
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError || error instanceof errorHandler_1.PermissionError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(`Failed to remove role from shop: ${error.message}`);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to remove role from shop.');
            }
        }
    })
};
