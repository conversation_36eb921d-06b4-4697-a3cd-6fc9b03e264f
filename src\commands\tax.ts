import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import { TaxConfiguration } from '../models/TaxConfiguration';
import { withErrorHandler, ValidationError, DatabaseError, PermissionError } from '../utils/errorHandler';
import { createServerAdminEmbed, createServerSuccessEmbed, createServerErrorEmbed, EMOJIS, COLORS } from '../utils/embedBuilder';
import { resolveRole, validateRolePermissions } from '../utils/roleResolver';

interface TaxAmountConfig {
    taxType: 'fixed' | 'percentage';
    amount: number;
    percentageValue?: number;
}

/**
 * Parses tax amount input to determine if it's a fixed amount or percentage
 */
function parseTaxAmount(input: string): TaxAmountConfig {
    if (!input || input.trim().length === 0) {
        throw new ValidationError('Tax amount cannot be empty');
    }

    const trimmedInput = input.trim();

    // Check if it's a percentage (ends with %)
    if (trimmedInput.endsWith('%')) {
        const percentageStr = trimmedInput.slice(0, -1);
        const percentageValue = parseFloat(percentageStr);

        if (isNaN(percentageValue)) {
            throw new ValidationError('Invalid percentage format. Use a number followed by % (e.g., 20%)');
        }

        if (percentageValue <= 0 || percentageValue > 100) {
            throw new ValidationError('Percentage must be between 0.1% and 100%');
        }

        return {
            taxType: 'percentage',
            amount: 1, // Placeholder amount for database compatibility
            percentageValue
        };
    } else {
        // Parse as fixed amount
        const amount = parseInt(trimmedInput, 10);

        if (isNaN(amount)) {
            throw new ValidationError('Invalid amount format. Use a whole number (e.g., 2000) or percentage (e.g., 20%)');
        }

        if (amount <= 0 || amount > 10000) {
            throw new ValidationError('Fixed amount must be between 1 and 10000 PLC');
        }

        return {
            taxType: 'fixed',
            amount,
            percentageValue: undefined
        };
    }
}

module.exports = {
    data: new SlashCommandBuilder()
        .setName('tax')
        .setDescription('Configure the automatic taxation system (admin only)')
        .addStringOption(option =>
            option.setName('status')
                .setDescription('Enable or disable the tax system')
                .setRequired(true)
                .addChoices(
                    { name: 'Enable', value: 'on' },
                    { name: 'Disable', value: 'off' }
                ))
        .addIntegerOption(option =>
            option.setName('frequency')
                .setDescription('Number of weeks between tax collection (1-52)')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(52))
        .addStringOption(option =>
            option.setName('amount')
                .setDescription('Tax amount: fixed number (e.g., 2000) or percentage (e.g., 20%)')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('role')
                .setDescription('Discord role that will be subject to taxation')
                .setRequired(false))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        // Check permissions
        if (!interaction.guild) {
            throw new ValidationError('This command can only be used in a server.');
        }

        if (!interaction.memberPermissions?.has(PermissionFlagsBits.Administrator)) {
            throw new PermissionError('You need Administrator permissions to use this command.');
        }

        const status = interaction.options.getString('status', true);
        const frequency = interaction.options.getInteger('frequency');
        const amountInput = interaction.options.getString('amount');
        const roleInput = interaction.options.getString('role');

        try {
            if (status === 'off') {
                // Disable tax system
                await TaxConfiguration.findOneAndUpdate(
                    { guildId: interaction.guild.id },
                    {
                        enabled: false,
                        nextTaxDate: undefined
                    },
                    { upsert: true, new: true }
                );

                const guildId = interaction.guild!.id;
                const embed = await createServerSuccessEmbed(guildId, 'Tax System Disabled');
                embed.setDescription(
                        `${EMOJIS.SUCCESS.CHECK} **Tax Collection Disabled**\n\n` +
                        `The automatic taxation system has been disabled for this server.\n\n` +
                        `${EMOJIS.ADMIN.INFO} No further tax collections will occur until re-enabled.`
                    );

                await interaction.reply({ embeds: [embed], ephemeral: false });
                return;
            }

            // Enable tax system - validate required parameters
            if (!frequency) {
                throw new ValidationError('Frequency is required when enabling the tax system. Please specify how many weeks between tax collections (1-52).');
            }

            if (!amountInput) {
                throw new ValidationError('Amount is required when enabling the tax system. Please specify a fixed amount (e.g., 2000) or percentage (e.g., 20%).');
            }

            // Parse and validate the tax amount
            const taxConfig = parseTaxAmount(amountInput);

            if (!roleInput) {
                throw new ValidationError('Role is required when enabling the tax system. Please specify which role will be subject to taxation.');
            }

            // Resolve and validate the role
            const roleResolution = await resolveRole(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;

            // Validate role permissions
            await validateRolePermissions(interaction.guild, role);

            // Create or update tax configuration
            const savedTaxConfig = await TaxConfiguration.findOneAndUpdate(
                { guildId: interaction.guild.id },
                {
                    enabled: true,
                    frequency,
                    amount: taxConfig.amount,
                    taxType: taxConfig.taxType,
                    percentageValue: taxConfig.percentageValue,
                    roleId: role.id,
                    roleName: role.name,
                    lastTaxDate: undefined // Reset last tax date when configuration changes
                },
                { upsert: true, new: true, runValidators: true }
            );

            // Calculate next tax date (frequency weeks from now)
            const nextTaxDate = new Date(Date.now() + (frequency * 7 * 24 * 60 * 60 * 1000));
            const nextTaxDateString = nextTaxDate.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZoneName: 'short'
            });

            // Create success embed
            const guildId = interaction.guild!.id;
            const embed = await createServerAdminEmbed(guildId, 'Tax System Configured');
            embed.setDescription(
                    `${EMOJIS.ADMIN.HAMMER} **Tax Collection Enabled**\n\n` +
                    `The automatic taxation system has been successfully configured!`
                )
                .addFields(
                    {
                        name: `${EMOJIS.ADMIN.SCALES} Tax Configuration`,
                        value:
                            `**Frequency:** Every ${frequency} week${frequency > 1 ? 's' : ''}\n` +
                            `**Amount:** ${taxConfig.taxType === 'percentage'
                                ? `${taxConfig.percentageValue}% of user balance`
                                : `${taxConfig.amount} PLC per collection`}\n` +
                            `**Taxed Role:** ${role.name}`,
                        inline: false
                    },
                    {
                        name: `${EMOJIS.ADMIN.CLOCK} Next Collection`,
                        value: nextTaxDateString,
                        inline: false
                    },
                    {
                        name: `${EMOJIS.ADMIN.WARNING} Important Notes`,
                        value:
                            `• Users who cannot afford the tax will lose **ALL** purchasable roles\n` +
                            `• Users will be notified via DM when roles are removed\n` +
                            `• Tax collection runs automatically in the background`,
                        inline: false
                    }
                );

            // Add resolution info if role was resolved by fuzzy matching
            if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                embed.setFooter({ text: `Note: Resolved "${roleInput}" to "${role.name}"` });
            }

            await interaction.reply({ embeds: [embed], ephemeral: false });

        } catch (error: unknown) {
            if (error instanceof ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new DatabaseError('Failed to configure tax system. Please try again.', error);
            }
            throw new DatabaseError('An unexpected error occurred while configuring the tax system.');
        }
    })
};
