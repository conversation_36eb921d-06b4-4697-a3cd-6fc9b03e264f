"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElectionIndexManager = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
class ElectionIndexManager {
    constructor(logger) {
        this.logger = logger;
    }
    async createElectionIndexes() {
        try {
            this.logger.info('[ElectionIndexManager] Creating election indexes for performance optimization');
            const electionCollection = mongoose_1.default.connection.collection('elections');
            await Promise.all([
                electionCollection.createIndex({ electionId: 1 }, { unique: true, background: true }),
                electionCollection.createIndex({ guildId: 1 }, { background: true }),
                electionCollection.createIndex({ status: 1 }, { background: true }),
                electionCollection.createIndex({ channelId: 1 }, { background: true }),
                electionCollection.createIndex({ createdAt: 1 }, { background: true }),
                electionCollection.createIndex({ nominationStart: 1, nominationEnd: 1 }, { background: true }),
                electionCollection.createIndex({ votingStart: 1, votingEnd: 1 }, { background: true }),
                electionCollection.createIndex({ guildId: 1, status: 1 }, { background: true }),
                electionCollection.createIndex({ guildId: 1, createdAt: -1 }, { background: true }),
                electionCollection.createIndex({ status: 1, votingEnd: 1 }, { background: true }),
            ]);
            const candidateCollection = mongoose_1.default.connection.collection('electioncandidates');
            await Promise.all([
                candidateCollection.createIndex({ candidateId: 1 }, { unique: true, background: true }),
                candidateCollection.createIndex({ electionId: 1 }, { background: true }),
                candidateCollection.createIndex({ userId: 1 }, { background: true }),
                candidateCollection.createIndex({ guildId: 1 }, { background: true }),
                candidateCollection.createIndex({ withdrawn: 1 }, { background: true }),
                candidateCollection.createIndex({ disqualified: 1 }, { background: true }),
                candidateCollection.createIndex({ nominationTime: 1 }, { background: true }),
                candidateCollection.createIndex({ electionId: 1, withdrawn: 1, disqualified: 1 }, { background: true }),
                candidateCollection.createIndex({ electionId: 1, nominationTime: 1 }, { background: true }),
                candidateCollection.createIndex({ guildId: 1, userId: 1 }, { background: true }),
                candidateCollection.createIndex({ electionId: 1, userId: 1 }, { unique: true, background: true }),
            ]);
            const snapshotCollection = mongoose_1.default.connection.collection('snapshotbalances');
            await Promise.all([
                snapshotCollection.createIndex({ snapshotId: 1 }, { unique: true, background: true }),
                snapshotCollection.createIndex({ electionId: 1 }, { background: true }),
                snapshotCollection.createIndex({ userId: 1 }, { background: true }),
                snapshotCollection.createIndex({ guildId: 1 }, { background: true }),
                snapshotCollection.createIndex({ balance: 1 }, { background: true }),
                snapshotCollection.createIndex({ recordedAt: 1 }, { background: true }),
                snapshotCollection.createIndex({ electionId: 1, userId: 1 }, { unique: true, background: true }),
                snapshotCollection.createIndex({ electionId: 1, balance: -1 }, { background: true }),
                snapshotCollection.createIndex({ guildId: 1, recordedAt: -1 }, { background: true }),
            ]);
            const ballotCollection = mongoose_1.default.connection.collection('electionballots');
            await Promise.all([
                ballotCollection.createIndex({ ballotId: 1 }, { unique: true, background: true }),
                ballotCollection.createIndex({ electionId: 1 }, { background: true }),
                ballotCollection.createIndex({ voterUserId: 1 }, { background: true }),
                ballotCollection.createIndex({ candidateId: 1 }, { background: true }),
                ballotCollection.createIndex({ guildId: 1 }, { background: true }),
                ballotCollection.createIndex({ replaced: 1 }, { background: true }),
                ballotCollection.createIndex({ timestamp: 1 }, { background: true }),
                ballotCollection.createIndex({ weight: 1 }, { background: true }),
                ballotCollection.createIndex({ electionId: 1, replaced: 1 }, { background: true }),
                ballotCollection.createIndex({ electionId: 1, candidateId: 1, replaced: 1 }, { background: true }),
                ballotCollection.createIndex({ electionId: 1, voterUserId: 1, replaced: 1 }, { background: true }),
                ballotCollection.createIndex({ guildId: 1, timestamp: -1 }, { background: true }),
            ]);
            const auditCollection = mongoose_1.default.connection.collection('electionauditlogs');
            await Promise.all([
                auditCollection.createIndex({ logId: 1 }, { unique: true, background: true }),
                auditCollection.createIndex({ electionId: 1 }, { background: true }),
                auditCollection.createIndex({ actorId: 1 }, { background: true }),
                auditCollection.createIndex({ targetId: 1 }, { background: true }),
                auditCollection.createIndex({ guildId: 1 }, { background: true }),
                auditCollection.createIndex({ actionType: 1 }, { background: true }),
                auditCollection.createIndex({ timestamp: 1 }, { background: true }),
                auditCollection.createIndex({ electionId: 1, timestamp: -1 }, { background: true }),
                auditCollection.createIndex({ electionId: 1, actionType: 1 }, { background: true }),
                auditCollection.createIndex({ guildId: 1, timestamp: -1 }, { background: true }),
                auditCollection.createIndex({ actorId: 1, timestamp: -1 }, { background: true }),
            ]);
            this.logger.info('[ElectionIndexManager] All election indexes created successfully');
        }
        catch (error) {
            this.logger.error('[ElectionIndexManager] Failed to create election indexes', { error });
            throw error;
        }
    }
    async dropElectionIndexes() {
        try {
            this.logger.info('[ElectionIndexManager] Dropping election indexes');
            const collections = [
                'elections',
                'electioncandidates',
                'snapshotbalances',
                'electionballots',
                'electionauditlogs'
            ];
            for (const collectionName of collections) {
                try {
                    const collection = mongoose_1.default.connection.collection(collectionName);
                    await collection.dropIndexes();
                    this.logger.info(`[ElectionIndexManager] Dropped indexes for ${collectionName}`);
                }
                catch (error) {
                    this.logger.warn(`[ElectionIndexManager] Could not drop indexes for ${collectionName}`, { error });
                }
            }
        }
        catch (error) {
            this.logger.error('[ElectionIndexManager] Failed to drop election indexes', { error });
            throw error;
        }
    }
    async analyzeIndexPerformance() {
        try {
            this.logger.info('[ElectionIndexManager] Analyzing index performance');
            const collections = [
                'elections',
                'electioncandidates',
                'snapshotbalances',
                'electionballots',
                'electionauditlogs'
            ];
            for (const collectionName of collections) {
                try {
                    const collection = mongoose_1.default.connection.collection(collectionName);
                    const indexes = await collection.listIndexes().toArray();
                    const stats = await collection.stats();
                    this.logger.info(`[ElectionIndexManager] ${collectionName} analysis`, {
                        indexCount: indexes.length,
                        documentCount: stats.count,
                        totalSize: stats.size,
                        avgDocumentSize: stats.avgObjSize,
                        indexes: indexes.map(idx => ({
                            name: idx.name,
                            keys: idx.key,
                            unique: idx.unique || false,
                            background: idx.background || false
                        }))
                    });
                }
                catch (error) {
                    this.logger.warn(`[ElectionIndexManager] Could not analyze ${collectionName}`, { error });
                }
            }
        }
        catch (error) {
            this.logger.error('[ElectionIndexManager] Failed to analyze index performance', { error });
        }
    }
    async getIndexStats() {
        try {
            const stats = {};
            const collections = [
                'elections',
                'electioncandidates',
                'snapshotbalances',
                'electionballots',
                'electionauditlogs'
            ];
            for (const collectionName of collections) {
                try {
                    const collection = mongoose_1.default.connection.collection(collectionName);
                    const indexStats = await collection.aggregate([
                        { $indexStats: {} }
                    ]).toArray();
                    stats[collectionName] = indexStats;
                }
                catch (error) {
                    this.logger.warn(`[ElectionIndexManager] Could not get stats for ${collectionName}`, { error });
                    stats[collectionName] = null;
                }
            }
            return stats;
        }
        catch (error) {
            this.logger.error('[ElectionIndexManager] Failed to get index stats', { error });
            return null;
        }
    }
}
exports.ElectionIndexManager = ElectionIndexManager;
