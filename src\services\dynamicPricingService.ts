/**
 * Dynamic Pricing Service
 * Handles percentage-based role pricing calculations with caching
 */

import { IRoleForSale } from '../models/User';
import { LeaderboardManager } from './economy/managers/LeaderboardManager';
import { createLogger } from '../core/logger';
import { DatabaseError } from '../utils/errorHandler';

interface EconomyCache {
  [guildId: string]: {
    totalEconomyValue: number;
    timestamp: number;
    ttl: number;
  };
}

interface CalculatedPrice {
  displayPrice: number;
  actualPrice: number;
  isPercentageBased: boolean;
  percentageValue?: number;
  economyTotal?: number;
}

export class DynamicPricingService {
  private static instance: DynamicPricingService;
  private economyCache: EconomyCache = {};
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MIN_ECONOMY_VALUE = 1000; // Minimum economy value for percentage calculations
  private readonly MIN_PERCENTAGE_PRICE = 10; // Minimum price for percentage-based roles

  private constructor() {}

  public static getInstance(): DynamicPricingService {
    if (!DynamicPricingService.instance) {
      DynamicPricingService.instance = new DynamicPricingService();
    }
    return DynamicPricingService.instance;
  }

  /**
   * Parse price input to determine if it's fixed or percentage-based
   */
  public parsePriceInput(priceInput: string | number): {
    priceType: 'fixed' | 'percentage';
    value: number;
    percentageValue?: number;
  } {
    if (typeof priceInput === 'number') {
      return {
        priceType: 'fixed',
        value: priceInput
      };
    }

    const stringInput = priceInput.toString().trim();
    
    // Check if it's a percentage (ends with %)
    if (stringInput.endsWith('%')) {
      const percentageStr = stringInput.slice(0, -1);
      const percentageValue = parseFloat(percentageStr);
      
      if (isNaN(percentageValue) || percentageValue < 0.1 || percentageValue > 50) {
        throw new Error('Percentage must be between 0.1% and 50%');
      }

      return {
        priceType: 'percentage',
        value: 0, // Will be calculated dynamically
        percentageValue
      };
    }

    // Try to parse as fixed number
    const fixedValue = parseInt(stringInput);
    if (isNaN(fixedValue) || fixedValue < 0) {
      throw new Error('Fixed price must be a non-negative integer');
    }

    return {
      priceType: 'fixed',
      value: fixedValue
    };
  }

  /**
   * Get total economy value for a guild with caching
   */
  private async getTotalEconomyValue(guildId: string): Promise<number> {
    const now = Date.now();
    const cached = this.economyCache[guildId];

    // Return cached value if still valid
    if (cached && (now - cached.timestamp) < cached.ttl) {
      return cached.totalEconomyValue;
    }

    try {
      // Get fresh data from LeaderboardManager
      const logger = createLogger('DynamicPricingService');
      const leaderboardManager = new LeaderboardManager(logger);
      const stats = await leaderboardManager.getLeaderboardStats(guildId);
      
      const totalEconomyValue = Math.max(stats.totalEconomyValue, this.MIN_ECONOMY_VALUE);

      // Cache the result
      this.economyCache[guildId] = {
        totalEconomyValue,
        timestamp: now,
        ttl: this.CACHE_TTL
      };

      return totalEconomyValue;
    } catch (error) {
      console.error(`Failed to get economy stats for guild ${guildId}:`, error);
      
      // Return cached value if available, otherwise use minimum
      if (cached) {
        return cached.totalEconomyValue;
      }
      
      return this.MIN_ECONOMY_VALUE;
    }
  }

  /**
   * Calculate the actual price for a role
   */
  public async calculatePrice(role: IRoleForSale, guildId: string): Promise<CalculatedPrice> {
    if (role.priceType === 'fixed') {
      return {
        displayPrice: role.price,
        actualPrice: role.price,
        isPercentageBased: false
      };
    }

    if (role.priceType === 'percentage' && role.percentageValue) {
      const economyTotal = await this.getTotalEconomyValue(guildId);
      const calculatedPrice = Math.floor((role.percentageValue / 100) * economyTotal);
      const actualPrice = Math.max(calculatedPrice, this.MIN_PERCENTAGE_PRICE);

      return {
        displayPrice: actualPrice,
        actualPrice,
        isPercentageBased: true,
        percentageValue: role.percentageValue,
        economyTotal
      };
    }

    // Fallback to fixed price
    return {
      displayPrice: role.price,
      actualPrice: role.price,
      isPercentageBased: false
    };
  }

  /**
   * Calculate prices for multiple roles efficiently
   */
  public async calculatePricesForRoles(roles: IRoleForSale[], guildId: string): Promise<Map<string, CalculatedPrice>> {
    const results = new Map<string, CalculatedPrice>();
    
    // Check if any roles need economy calculation
    const needsEconomyData = roles.some(role => role.priceType === 'percentage');
    let economyTotal = 0;

    if (needsEconomyData) {
      economyTotal = await this.getTotalEconomyValue(guildId);
    }

    for (const role of roles) {
      if (role.priceType === 'fixed') {
        results.set(role.roleId, {
          displayPrice: role.price,
          actualPrice: role.price,
          isPercentageBased: false
        });
      } else if (role.priceType === 'percentage' && role.percentageValue) {
        const calculatedPrice = Math.floor((role.percentageValue / 100) * economyTotal);
        const actualPrice = Math.max(calculatedPrice, this.MIN_PERCENTAGE_PRICE);

        results.set(role.roleId, {
          displayPrice: actualPrice,
          actualPrice,
          isPercentageBased: true,
          percentageValue: role.percentageValue,
          economyTotal
        });
      } else {
        // Fallback
        results.set(role.roleId, {
          displayPrice: role.price,
          actualPrice: role.price,
          isPercentageBased: false
        });
      }
    }

    return results;
  }

  /**
   * Clear cache for a specific guild
   */
  public clearGuildCache(guildId: string): void {
    delete this.economyCache[guildId];
  }

  /**
   * Clear all cache
   */
  public clearAllCache(): void {
    this.economyCache = {};
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): { totalCached: number; oldestEntry: number | null } {
    const guilds = Object.keys(this.economyCache);
    const now = Date.now();
    
    let oldestEntry: number | null = null;
    
    for (const guildId of guilds) {
      const entry = this.economyCache[guildId];
      if (oldestEntry === null || entry.timestamp < oldestEntry) {
        oldestEntry = entry.timestamp;
      }
    }

    return {
      totalCached: guilds.length,
      oldestEntry: oldestEntry ? now - oldestEntry : null
    };
  }
}

export default DynamicPricingService.getInstance();
