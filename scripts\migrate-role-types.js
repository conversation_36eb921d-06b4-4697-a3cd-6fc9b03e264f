/**
 * Migration script to update existing RoleForSale records with proper roleType
 * This script will set all existing roles to 'balance_threshold' since they were
 * originally created as automatic unlock roles via /addrole command.
 * 
 * Run this script after deploying the roleType changes to ensure database consistency.
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { RoleForSale } = require('../dist/models/User');

async function migrateRoleTypes() {
    try {
        console.log('🔄 Connecting to MongoDB...');
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Connected to MongoDB');

        console.log('🔍 Checking existing roles...');
        
        // Find all roles that don't have a roleType set (or have null/undefined)
        const rolesWithoutType = await RoleForSale.find({
            $or: [
                { roleType: { $exists: false } },
                { roleType: null },
                { roleType: undefined }
            ]
        });

        console.log(`📊 Found ${rolesWithoutType.length} roles without roleType`);

        if (rolesWithoutType.length === 0) {
            console.log('✅ All roles already have roleType set');
            return;
        }

        // Update all existing roles to be balance_threshold (since they were created via /addrole)
        const updateResult = await RoleForSale.updateMany(
            {
                $or: [
                    { roleType: { $exists: false } },
                    { roleType: null },
                    { roleType: undefined }
                ]
            },
            {
                $set: { roleType: 'balance_threshold' }
            }
        );

        console.log(`✅ Updated ${updateResult.modifiedCount} roles to 'balance_threshold' type`);

        // Verify the migration
        const verifyCount = await RoleForSale.countDocuments({ roleType: 'balance_threshold' });
        const totalCount = await RoleForSale.countDocuments();
        
        console.log(`📊 Migration Summary:`);
        console.log(`   • Total roles: ${totalCount}`);
        console.log(`   • Balance threshold roles: ${verifyCount}`);
        console.log(`   • Shop purchase roles: ${totalCount - verifyCount}`);

        console.log('✅ Migration completed successfully!');
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
        process.exit(0);
    }
}

// Run the migration
if (require.main === module) {
    migrateRoleTypes();
}

module.exports = { migrateRoleTypes };
