"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElectionErrorRecoveryService = void 0;
const errorHandler_1 = require("../utils/errorHandler");
const events_1 = require("events");
class ElectionErrorRecoveryService extends events_1.EventEmitter {
    constructor(logger, monitoring) {
        super();
        this.logger = logger;
        this.monitoring = monitoring;
        this.retryConfigs = new Map();
        this.circuitBreakers = new Map();
        this.recoveryHistory = [];
        this.emergencyMode = false;
        this.gracefulDegradationEnabled = false;
        this.setupDefaultConfigurations();
        this.setupEventHandlers();
    }
    setupDefaultConfigurations() {
        this.retryConfigs.set('database', {
            maxAttempts: 3,
            baseDelay: 1000,
            maxDelay: 10000,
            backoffMultiplier: 2,
            jitter: true
        });
        this.retryConfigs.set('discord_api', {
            maxAttempts: 5,
            baseDelay: 2000,
            maxDelay: 30000,
            backoffMultiplier: 1.5,
            jitter: true
        });
        this.retryConfigs.set('vote_cast', {
            maxAttempts: 2,
            baseDelay: 500,
            maxDelay: 5000,
            backoffMultiplier: 2,
            jitter: false
        });
        this.retryConfigs.set('candidate_register', {
            maxAttempts: 2,
            baseDelay: 1000,
            maxDelay: 5000,
            backoffMultiplier: 2,
            jitter: false
        });
        this.setupCircuitBreaker('database', {
            failureThreshold: 5,
            recoveryTimeout: 30000,
            monitoringPeriod: 60000
        });
        this.setupCircuitBreaker('discord_api', {
            failureThreshold: 10,
            recoveryTimeout: 60000,
            monitoringPeriod: 120000
        });
    }
    setupEventHandlers() {
        if (this.monitoring) {
            this.monitoring.on('critical_alert', (alert) => {
                this.handleCriticalAlert(alert);
            });
            this.monitoring.on('emergency_mode', (data) => {
                this.enableEmergencyMode(data.reason);
            });
            this.monitoring.on('database_performance_issue', (data) => {
                this.handleDatabasePerformanceIssue(data.alert);
            });
        }
    }
    async executeWithRecovery(operation, operationType, metadata) {
        const startTime = Date.now();
        let lastError;
        if (this.isCircuitBreakerOpen(operationType)) {
            const error = new Error(`Circuit breaker is open for ${operationType}`);
            this.recordRecoveryAction({
                id: this.generateActionId(),
                type: 'alert',
                operation: operationType,
                timestamp: Date.now(),
                success: false,
                error: error.message,
                metadata
            });
            throw error;
        }
        const retryConfig = this.retryConfigs.get(operationType) || this.retryConfigs.get('database');
        for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
            try {
                const result = await operation();
                this.recordCircuitBreakerSuccess(operationType);
                this.recordRecoveryAction({
                    id: this.generateActionId(),
                    type: 'retry',
                    operation: operationType,
                    timestamp: Date.now(),
                    success: true,
                    metadata: { ...metadata, attempt, duration: Date.now() - startTime }
                });
                if (this.monitoring) {
                    this.monitoring.recordOperationTime(operationType, Date.now() - startTime);
                }
                return result;
            }
            catch (error) {
                lastError = error;
                this.recordCircuitBreakerFailure(operationType);
                this.logger.warn(`[ElectionErrorRecovery] Operation failed, attempt ${attempt}/${retryConfig.maxAttempts}`, {
                    operation: operationType,
                    error: error instanceof Error ? error.message : 'Unknown error',
                    attempt,
                    metadata
                });
                if (this.shouldNotRetry(error)) {
                    this.logger.info(`[ElectionErrorRecovery] Not retrying due to error type`, {
                        operation: operationType,
                        errorType: error?.constructor?.name
                    });
                    break;
                }
                if (attempt < retryConfig.maxAttempts) {
                    const delay = this.calculateDelay(attempt, retryConfig);
                    await this.sleep(delay);
                }
            }
        }
        this.recordRecoveryAction({
            id: this.generateActionId(),
            type: 'retry',
            operation: operationType,
            timestamp: Date.now(),
            success: false,
            error: lastError instanceof Error ? lastError.message : 'Unknown error',
            metadata: { ...metadata, totalAttempts: retryConfig.maxAttempts, duration: Date.now() - startTime }
        });
        const fallbackResult = await this.tryFallback(operationType, lastError, metadata);
        if (fallbackResult.success) {
            return fallbackResult.data;
        }
        if (this.monitoring) {
            this.monitoring.recordMetric('failed_operations', 1, { operation: operationType });
        }
        throw lastError;
    }
    shouldNotRetry(error) {
        if (error instanceof errorHandler_1.ValidationError) {
            return true;
        }
        if (error?.code === 50013 || error?.code === 50001) {
            return true;
        }
        if (error?.code === 429 && error?.retry_after > 60000) {
            return true;
        }
        return false;
    }
    calculateDelay(attempt, config) {
        const exponentialDelay = Math.min(config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1), config.maxDelay);
        if (config.jitter) {
            const jitterAmount = exponentialDelay * 0.2;
            return exponentialDelay + (Math.random() - 0.5) * 2 * jitterAmount;
        }
        return exponentialDelay;
    }
    async tryFallback(operationType, originalError, metadata) {
        this.logger.info(`[ElectionErrorRecovery] Attempting fallback for ${operationType}`);
        try {
            switch (operationType) {
                case 'vote_cast':
                    return await this.fallbackVoteCast(originalError, metadata);
                case 'candidate_register':
                    return await this.fallbackCandidateRegister(originalError, metadata);
                case 'election_results':
                    return await this.fallbackElectionResults(originalError, metadata);
                case 'database':
                    return await this.fallbackDatabaseOperation(originalError, metadata);
                default:
                    return { success: false };
            }
        }
        catch (fallbackError) {
            this.logger.error(`[ElectionErrorRecovery] Fallback failed for ${operationType}`, {
                originalError: originalError instanceof Error ? originalError.message : 'Unknown',
                fallbackError: fallbackError instanceof Error ? fallbackError.message : 'Unknown'
            });
            this.recordRecoveryAction({
                id: this.generateActionId(),
                type: 'fallback',
                operation: operationType,
                timestamp: Date.now(),
                success: false,
                error: fallbackError instanceof Error ? fallbackError.message : 'Unknown error',
                metadata
            });
            return { success: false };
        }
    }
    async fallbackVoteCast(originalError, metadata) {
        if (originalError instanceof errorHandler_1.DatabaseError && originalError.message.includes('transaction')) {
            this.logger.info('[ElectionErrorRecovery] Queueing vote for delayed processing');
            this.recordRecoveryAction({
                id: this.generateActionId(),
                type: 'fallback',
                operation: 'vote_cast',
                timestamp: Date.now(),
                success: true,
                metadata: { ...metadata, fallbackType: 'queued' }
            });
            return {
                success: true,
                data: {
                    queued: true,
                    message: 'Vote queued for processing due to high load'
                }
            };
        }
        return { success: false };
    }
    async fallbackCandidateRegister(originalError, metadata) {
        if (originalError instanceof errorHandler_1.DatabaseError) {
            this.logger.info('[ElectionErrorRecovery] Adding candidate to manual review queue');
            this.recordRecoveryAction({
                id: this.generateActionId(),
                type: 'fallback',
                operation: 'candidate_register',
                timestamp: Date.now(),
                success: true,
                metadata: { ...metadata, fallbackType: 'manual_review' }
            });
            return {
                success: true,
                data: {
                    manualReview: true,
                    message: 'Candidacy submitted for manual review due to technical issues'
                }
            };
        }
        return { success: false };
    }
    async fallbackElectionResults(originalError, metadata) {
        this.logger.info('[ElectionErrorRecovery] Attempting to return cached election results');
        this.recordRecoveryAction({
            id: this.generateActionId(),
            type: 'fallback',
            operation: 'election_results',
            timestamp: Date.now(),
            success: true,
            metadata: { ...metadata, fallbackType: 'cached' }
        });
        return {
            success: true,
            data: {
                cached: true,
                message: 'Displaying cached results due to technical issues',
                candidates: [],
                totalVotes: 0,
                totalVoteWeight: 0
            }
        };
    }
    async fallbackDatabaseOperation(originalError, metadata) {
        if (this.gracefulDegradationEnabled) {
            this.logger.info('[ElectionErrorRecovery] Enabling read-only mode due to database issues');
            this.recordRecoveryAction({
                id: this.generateActionId(),
                type: 'fallback',
                operation: 'database',
                timestamp: Date.now(),
                success: true,
                metadata: { ...metadata, fallbackType: 'read_only' }
            });
            return {
                success: true,
                data: {
                    readOnly: true,
                    message: 'System in read-only mode due to database issues'
                }
            };
        }
        return { success: false };
    }
    setupCircuitBreaker(name, config) {
        this.circuitBreakers.set(name, {
            config,
            state: 'closed',
            failures: 0,
            lastFailureTime: 0,
            lastSuccessTime: Date.now()
        });
    }
    recordCircuitBreakerFailure(operationType) {
        const breaker = this.circuitBreakers.get(operationType);
        if (breaker) {
            breaker.failures++;
            breaker.lastFailureTime = Date.now();
            if (breaker.failures >= breaker.config.failureThreshold && breaker.state === 'closed') {
                breaker.state = 'open';
                this.logger.warn(`[ElectionErrorRecovery] Circuit breaker opened for ${operationType}`, {
                    failures: breaker.failures,
                    threshold: breaker.config.failureThreshold
                });
                this.emit('circuit_breaker_opened', { operationType, breaker });
            }
        }
    }
    recordCircuitBreakerSuccess(operationType) {
        const breaker = this.circuitBreakers.get(operationType);
        if (breaker) {
            breaker.failures = 0;
            breaker.lastSuccessTime = Date.now();
            if (breaker.state === 'half_open') {
                breaker.state = 'closed';
                this.logger.info(`[ElectionErrorRecovery] Circuit breaker closed for ${operationType}`);
                this.emit('circuit_breaker_closed', { operationType, breaker });
            }
        }
    }
    isCircuitBreakerOpen(operationType) {
        const breaker = this.circuitBreakers.get(operationType);
        if (!breaker)
            return false;
        const now = Date.now();
        if (breaker.state === 'open') {
            if (now - breaker.lastFailureTime >= breaker.config.recoveryTimeout) {
                breaker.state = 'half_open';
                this.logger.info(`[ElectionErrorRecovery] Circuit breaker half-open for ${operationType}`);
                return false;
            }
            return true;
        }
        return false;
    }
    async handleCriticalAlert(alert) {
        this.logger.error('[ElectionErrorRecovery] Handling critical alert', alert);
        if (alert.severity === 'critical') {
            await this.enableEmergencyMode(`Critical alert: ${alert.metric}`);
        }
    }
    async enableEmergencyMode(reason) {
        if (this.emergencyMode)
            return;
        this.emergencyMode = true;
        this.gracefulDegradationEnabled = true;
        this.logger.error('[ElectionErrorRecovery] EMERGENCY MODE ENABLED', { reason });
        this.recordRecoveryAction({
            id: this.generateActionId(),
            type: 'emergency',
            operation: 'emergency_mode',
            timestamp: Date.now(),
            success: true,
            metadata: { reason }
        });
        this.emit('emergency_mode_enabled', { reason });
        setTimeout(() => {
            this.disableEmergencyMode('automatic_timeout');
        }, 30 * 60 * 1000);
    }
    async disableEmergencyMode(reason) {
        if (!this.emergencyMode)
            return;
        this.emergencyMode = false;
        this.gracefulDegradationEnabled = false;
        this.logger.info('[ElectionErrorRecovery] Emergency mode disabled', { reason });
        this.recordRecoveryAction({
            id: this.generateActionId(),
            type: 'emergency',
            operation: 'emergency_mode_disable',
            timestamp: Date.now(),
            success: true,
            metadata: { reason }
        });
        this.emit('emergency_mode_disabled', { reason });
    }
    async handleDatabasePerformanceIssue(alert) {
        this.logger.warn('[ElectionErrorRecovery] Database performance issue detected', alert);
        this.gracefulDegradationEnabled = true;
        setTimeout(() => {
            this.gracefulDegradationEnabled = false;
            this.logger.info('[ElectionErrorRecovery] Graceful degradation disabled');
        }, 10 * 60 * 1000);
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    generateActionId() {
        return `recovery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    recordRecoveryAction(action) {
        this.recoveryHistory.push(action);
        if (this.recoveryHistory.length > 1000) {
            this.recoveryHistory.splice(0, this.recoveryHistory.length - 1000);
        }
        this.emit('recovery_action', action);
    }
    isEmergencyModeEnabled() {
        return this.emergencyMode;
    }
    isGracefulDegradationEnabled() {
        return this.gracefulDegradationEnabled;
    }
    getCircuitBreakerStatus() {
        const status = new Map();
        for (const [name, breaker] of this.circuitBreakers) {
            status.set(name, {
                state: breaker.state,
                failures: breaker.failures,
                lastFailureTime: breaker.lastFailureTime,
                lastSuccessTime: breaker.lastSuccessTime
            });
        }
        return status;
    }
    getRecoveryHistory(limit = 100) {
        return this.recoveryHistory
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, limit);
    }
    getRecoveryStats() {
        const now = Date.now();
        const oneHourAgo = now - 3600000;
        const recentActions = this.recoveryHistory.filter(a => a.timestamp >= oneHourAgo);
        return {
            totalActions: this.recoveryHistory.length,
            recentActions: recentActions.length,
            successRate: recentActions.length > 0 ?
                recentActions.filter(a => a.success).length / recentActions.length : 1,
            emergencyMode: this.emergencyMode,
            gracefulDegradation: this.gracefulDegradationEnabled,
            circuitBreakers: Object.fromEntries(this.getCircuitBreakerStatus())
        };
    }
}
exports.ElectionErrorRecoveryService = ElectionErrorRecoveryService;
