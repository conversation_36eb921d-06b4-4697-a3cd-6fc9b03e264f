"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const roleResolver_1 = require("../utils/roleResolver");
const User_1 = __importDefault(require("../models/User"));
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('richestrole')
        .setDescription('Assign a role to the user with the highest coin balance (admin only)')
        .addRoleOption(option => option.setName('role')
        .setDescription('The role to assign to the richest member')
        .setRequired(true))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError('You need Administrator permissions to use this command.');
        }
        const role = interaction.options.getRole('role', true);
        try {
            await (0, roleResolver_1.validateRolePermissions)(interaction.guild, role);
            const richestUser = await User_1.default.findOne({ guildId: interaction.guild.id }).sort({ balance: -1 }).limit(1);
            if (!richestUser || richestUser.balance <= 0) {
                const coinName = await configurableConstants_1.default.getCoinName(interaction.guild.id);
                const coinSymbol = await configurableConstants_1.default.getCoinSymbol(interaction.guild.id);
                const embed = (0, embedBuilder_1.createErrorEmbed)('No Eligible Users')
                    .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **No Users Found**\n\nNo users with ${coinName} found in the database.`)
                    .addFields({
                    name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} Suggestion`,
                    value: `Users need to have earned at least 1 ${coinSymbol} to be eligible for this role.`,
                    inline: false
                });
                await interaction.reply({ embeds: [embed], ephemeral: false });
                return;
            }
            let richestMember;
            try {
                richestMember = await interaction.guild.members.fetch(richestUser.discordId);
            }
            catch (error) {
                const allUsers = await User_1.default.find({ balance: { $gt: 0 } }).sort({ balance: -1 });
                let foundMember = null;
                let foundUser = null;
                for (const user of allUsers) {
                    try {
                        foundMember = await interaction.guild.members.fetch(user.discordId);
                        foundUser = user;
                        break;
                    }
                    catch {
                        continue;
                    }
                }
                if (!foundMember || !foundUser) {
                    const coinSymbol = await configurableConstants_1.default.getCoinSymbol(interaction.guild.id);
                    const embed = (0, embedBuilder_1.createErrorEmbed)('No Eligible Members')
                        .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **No Eligible Members**\n\nNo users with ${coinSymbol} balances are currently in this server.`)
                        .addFields({
                        name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Database Status`,
                        value: `Found ${allUsers.length} user(s) with ${coinSymbol} balances, but none are in this server.`,
                        inline: false
                    });
                    await interaction.reply({ embeds: [embed], ephemeral: false });
                    return;
                }
                richestMember = foundMember;
                richestUser.discordId = foundUser.discordId;
                richestUser.balance = foundUser.balance;
            }
            const currentHolders = role.members;
            const previousHoldersList = currentHolders.map(member => member.displayName).slice(0, 5);
            const removePromises = currentHolders.map(member => member.roles.remove(role));
            await Promise.all(removePromises);
            await richestMember.roles.add(role);
            const guildId = interaction.guild.id;
            const formattedBalance = await (0, embedBuilder_1.formatServerCoins)(guildId, richestUser.balance);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Richest Role Assigned');
            embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Role Updated!**\n\nThe "${role.name}" role has been assigned to the richest member.`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} New Role Holder`,
                value: `**${richestMember.displayName}**\nBalance: ${formattedBalance}`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} Role Information`,
                value: `**Role:** ${role.name}\n**Previous Holders:** ${currentHolders.size}\n**Action:** Role reassigned`,
                inline: true
            });
            if (previousHoldersList.length > 0) {
                const previousHoldersText = previousHoldersList.length > 5
                    ? `${previousHoldersList.join(', ')} and ${currentHolders.size - 5} more`
                    : previousHoldersList.join(', ');
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Previous Holders`,
                    value: previousHoldersText || 'None',
                    inline: false
                });
            }
            embed.setThumbnail(richestMember.displayAvatarURL({ size: 128 }));
            await interaction.reply({ embeds: [embed], ephemeral: false });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError || error instanceof errorHandler_1.PermissionError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(`Failed to assign richest role: ${error.message}`);
            }
            throw new errorHandler_1.DatabaseError('An unexpected error occurred while assigning the richest role.');
        }
    })
};
