"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormatUtils = exports.ProgressFormatter = exports.TextFormatter = exports.ListFormatter = exports.UserFormatter = exports.TimeFormatter = exports.CurrencyFormatter = void 0;
const constants_1 = require("../../config/constants");
class CurrencyFormatter {
    static formatCoins(amount) {
        if (typeof amount !== 'number' || isNaN(amount)) {
            return `0 ${constants_1.ECONOMY.CURRENCY_SYMBOL}`;
        }
        const formattedAmount = amount.toLocaleString();
        return `${formattedAmount} ${constants_1.ECONOMY.CURRENCY_SYMBOL}`;
    }
    static formatCoinsWithEmoji(amount) {
        return `${constants_1.ECONOMY.CURRENCY_EMOJI} ${this.formatCoins(amount)}`;
    }
    static formatPercentage(value, decimals = 1) {
        return `${value.toFixed(decimals)}%`;
    }
    static formatLargeNumber(num) {
        if (num < 1000)
            return num.toString();
        if (num < 1000000)
            return `${(num / 1000).toFixed(1)}K`;
        if (num < 1000000000)
            return `${(num / 1000000).toFixed(1)}M`;
        return `${(num / 1000000000).toFixed(1)}B`;
    }
}
exports.CurrencyFormatter = CurrencyFormatter;
class TimeFormatter {
    static formatDuration(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        if (days > 0)
            return `${days}d ${hours % 24}h`;
        if (hours > 0)
            return `${hours}h ${minutes % 60}m`;
        if (minutes > 0)
            return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    }
    static formatRelativeTime(date) {
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffSeconds = Math.floor(diffMs / 1000);
        const diffMinutes = Math.floor(diffSeconds / 60);
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);
        if (diffDays > 0)
            return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
        if (diffHours > 0)
            return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        if (diffMinutes > 0)
            return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
        return 'Just now';
    }
    static formatDiscordTimestamp(date, style = 'f') {
        const timestamp = Math.floor(date.getTime() / 1000);
        return `<t:${timestamp}:${style}>`;
    }
}
exports.TimeFormatter = TimeFormatter;
class UserFormatter {
    static formatDisplayName(user) {
        if ('displayName' in user && typeof user.displayName === 'string') {
            return user.displayName;
        }
        if ('username' in user && typeof user.username === 'string') {
            return user.username;
        }
        return '';
    }
    static formatUserTag(user) {
        return user.tag;
    }
    static formatUserMention(user) {
        return `<@${user.id}>`;
    }
    static formatUserInfo(user, includeId = false) {
        let info = `**${user.username}**`;
        if (includeId) {
            info += `\nID: \`${user.id}\``;
        }
        return info;
    }
}
exports.UserFormatter = UserFormatter;
class ListFormatter {
    static formatNumberedList(items, startIndex = 1) {
        return items.map((item, index) => `${startIndex + index}. ${item}`).join('\n');
    }
    static formatBulletedList(items, bullet = '•') {
        return items.map(item => `${bullet} ${item}`).join('\n');
    }
    static formatLeaderboard(entries) {
        return entries.map(entry => {
            const medal = this.getRankMedal(entry.rank);
            return `${medal} **${entry.rank}.** ${entry.name} - ${entry.value}`;
        }).join('\n');
    }
    static getRankMedal(rank) {
        switch (rank) {
            case 1: return '🥇';
            case 2: return '🥈';
            case 3: return '🥉';
            default: return '🏅';
        }
    }
    static formatKeyValuePairs(pairs, separator = ': ') {
        return Object.entries(pairs)
            .map(([key, value]) => `**${key}**${separator}${value}`)
            .join('\n');
    }
}
exports.ListFormatter = ListFormatter;
class TextFormatter {
    static truncate(text, maxLength, suffix = '...') {
        if (text.length <= maxLength)
            return text;
        return text.substring(0, maxLength - suffix.length) + suffix;
    }
    static capitalize(text) {
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
    }
    static toTitleCase(text) {
        return text.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
    }
    static formatCodeBlock(code, language = '') {
        return `\`\`\`${language}\n${code}\n\`\`\``;
    }
    static formatInlineCode(code) {
        return `\`${code}\``;
    }
    static formatBold(text) {
        return `**${text}**`;
    }
    static formatItalic(text) {
        return `*${text}*`;
    }
    static formatStrikethrough(text) {
        return `~~${text}~~`;
    }
    static formatSpoiler(text) {
        return `||${text}||`;
    }
}
exports.TextFormatter = TextFormatter;
class ProgressFormatter {
    static createProgressBar(current, max, length = 10, filledChar = '█', emptyChar = '░') {
        const percentage = Math.max(0, Math.min(1, current / max));
        const filledLength = Math.round(length * percentage);
        const emptyLength = length - filledLength;
        return filledChar.repeat(filledLength) + emptyChar.repeat(emptyLength);
    }
    static formatProgress(current, max) {
        const percentage = Math.max(0, Math.min(100, (current / max) * 100));
        const progressBar = this.createProgressBar(current, max);
        return `${progressBar} ${percentage.toFixed(1)}%`;
    }
}
exports.ProgressFormatter = ProgressFormatter;
class FormatUtils {
}
exports.FormatUtils = FormatUtils;
FormatUtils.currency = CurrencyFormatter;
FormatUtils.time = TimeFormatter;
FormatUtils.user = UserFormatter;
FormatUtils.list = ListFormatter;
FormatUtils.text = TextFormatter;
FormatUtils.progress = ProgressFormatter;
FormatUtils.formatCoins = CurrencyFormatter.formatCoins;
FormatUtils.formatDisplayName = UserFormatter.formatDisplayName;
FormatUtils.formatRelativeTime = TimeFormatter.formatRelativeTime;
exports.default = FormatUtils;
