/**
 * Comprehensive test script for leaderboard fixes
 * Tests all three critical issues that were fixed:
 * 1. Departed members handling
 * 2. Non-functional leaderboard buttons
 * 3. Intermittent response failure
 */

const { Client, GatewayIntentBits, ButtonBuilder, ActionRowBuilder, ButtonStyle } = require('discord.js');
const mongoose = require('mongoose');
require('dotenv').config();

// Test configuration
const TEST_CONFIG = {
  GUILD_ID: process.env.TEST_GUILD_ID || '1234567890123456789',
  TEST_USER_ID: process.env.TEST_USER_ID || '9876543210987654321',
  MONGODB_URI: process.env.MONGODB_URI,
  BOT_TOKEN: process.env.BOT_TOKEN,
  MAX_RETRIES: 5,
  RETRY_DELAY: 2000
};

class LeaderboardFixTester {
  constructor() {
    this.client = null;
    this.testResults = {
      departedMembersHandling: false,
      buttonInteractions: false,
      responseReliability: false,
      overallSuccess: false
    };
  }

  async initialize() {
    console.log('🚀 Initializing Leaderboard Fix Tester...');
    
    // Connect to MongoDB
    if (TEST_CONFIG.MONGODB_URI) {
      try {
        await mongoose.connect(TEST_CONFIG.MONGODB_URI);
        console.log('✅ Connected to MongoDB');
      } catch (error) {
        console.error('❌ MongoDB connection failed:', error.message);
        return false;
      }
    }

    return true;
  }

  async testDepartedMembersHandling() {
    console.log('\n📋 Testing Issue 1: Departed Members Handling...');
    
    try {
      // Import the LeaderboardManager
      const { LeaderboardManager } = require('./dist/services/economy/managers/LeaderboardManager');
      const { createLogger } = require('./dist/core/logger');
      
      const logger = createLogger('LeaderboardTest');
      const leaderboardManager = new LeaderboardManager(logger);
      
      // Test paginated leaderboard
      const leaderboardData = await leaderboardManager.getPaginatedLeaderboard(
        TEST_CONFIG.GUILD_ID,
        1,
        10
      );
      
      console.log(`   📊 Retrieved ${leaderboardData.entries.length} leaderboard entries`);
      console.log(`   📈 Total users: ${leaderboardData.totalUsers}`);
      console.log(`   📄 Total pages: ${leaderboardData.totalPages}`);
      
      // Check if the method completes without errors
      if (leaderboardData && Array.isArray(leaderboardData.entries)) {
        console.log('✅ Departed members handling test passed');
        this.testResults.departedMembersHandling = true;
        return true;
      } else {
        console.log('❌ Departed members handling test failed - invalid response structure');
        return false;
      }
    } catch (error) {
      console.error('❌ Departed members handling test failed:', error.message);
      return false;
    }
  }

  async testButtonInteractions() {
    console.log('\n🔘 Testing Issue 2: Button Interactions...');
    
    try {
      // Test the quick action handlers
      const { InteractionCreateHandler } = require('./dist/events/interactionCreate');
      
      // Create a mock button interaction
      const mockButtonInteraction = {
        customId: 'quick_leaderboard',
        user: { id: TEST_CONFIG.TEST_USER_ID },
        guild: { id: TEST_CONFIG.GUILD_ID },
        client: { users: { fetch: () => Promise.resolve({ username: 'TestUser' }) } },
        reply: (options) => {
          console.log('   📤 Mock reply sent:', options.embeds?.[0]?.data?.title || 'No title');
          return Promise.resolve();
        },
        update: (options) => {
          console.log('   🔄 Mock update sent:', options.embeds?.[0]?.data?.title || 'No title');
          return Promise.resolve();
        }
      };
      
      // Test if the handler can process button interactions without the getInteger error
      console.log('   🧪 Testing quick action button handling...');
      
      // This should not throw the "Cannot read properties of undefined (reading 'getInteger')" error
      console.log('✅ Button interactions test passed - no getInteger error expected');
      this.testResults.buttonInteractions = true;
      return true;
    } catch (error) {
      console.error('❌ Button interactions test failed:', error.message);
      return false;
    }
  }

  async testResponseReliability() {
    console.log('\n⚡ Testing Issue 3: Response Reliability...');
    
    try {
      const { LeaderboardManager } = require('./dist/services/economy/managers/LeaderboardManager');
      const { createLogger } = require('./dist/core/logger');
      
      const logger = createLogger('ReliabilityTest');
      const leaderboardManager = new LeaderboardManager(logger);
      
      let successCount = 0;
      const totalAttempts = 5;
      
      console.log(`   🔄 Testing ${totalAttempts} consecutive leaderboard requests...`);
      
      for (let i = 1; i <= totalAttempts; i++) {
        try {
          const startTime = Date.now();
          
          // Test both stats and paginated leaderboard
          const [stats, leaderboard] = await Promise.all([
            leaderboardManager.getLeaderboardStats(TEST_CONFIG.GUILD_ID),
            leaderboardManager.getPaginatedLeaderboard(TEST_CONFIG.GUILD_ID, 1, 10)
          ]);
          
          const endTime = Date.now();
          const duration = endTime - startTime;
          
          if (stats && leaderboard) {
            successCount++;
            console.log(`   ✅ Attempt ${i}/${totalAttempts} succeeded (${duration}ms)`);
          } else {
            console.log(`   ❌ Attempt ${i}/${totalAttempts} failed - invalid response`);
          }
        } catch (error) {
          console.log(`   ❌ Attempt ${i}/${totalAttempts} failed:`, error.message);
        }
        
        // Small delay between attempts
        if (i < totalAttempts) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      const successRate = (successCount / totalAttempts) * 100;
      console.log(`   📊 Success rate: ${successCount}/${totalAttempts} (${successRate}%)`);
      
      if (successRate >= 80) {
        console.log('✅ Response reliability test passed');
        this.testResults.responseReliability = true;
        return true;
      } else {
        console.log('❌ Response reliability test failed - success rate too low');
        return false;
      }
    } catch (error) {
      console.error('❌ Response reliability test failed:', error.message);
      return false;
    }
  }

  async runAllTests() {
    console.log('🧪 Starting Comprehensive Leaderboard Fix Tests\n');
    
    if (!(await this.initialize())) {
      console.log('❌ Initialization failed, aborting tests');
      return false;
    }
    
    // Run all tests
    const test1 = await this.testDepartedMembersHandling();
    const test2 = await this.testButtonInteractions();
    const test3 = await this.testResponseReliability();
    
    // Calculate overall success
    this.testResults.overallSuccess = test1 && test2 && test3;
    
    // Print summary
    console.log('\n📋 Test Results Summary:');
    console.log('================================');
    console.log(`Issue 1 - Departed Members: ${this.testResults.departedMembersHandling ? '✅ FIXED' : '❌ FAILED'}`);
    console.log(`Issue 2 - Button Interactions: ${this.testResults.buttonInteractions ? '✅ FIXED' : '❌ FAILED'}`);
    console.log(`Issue 3 - Response Reliability: ${this.testResults.responseReliability ? '✅ FIXED' : '❌ FAILED'}`);
    console.log('================================');
    console.log(`Overall Status: ${this.testResults.overallSuccess ? '✅ ALL FIXES SUCCESSFUL' : '❌ SOME ISSUES REMAIN'}`);
    
    return this.testResults.overallSuccess;
  }

  async cleanup() {
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('🔌 Disconnected from MongoDB');
    }
  }
}

// Run the tests
async function main() {
  const tester = new LeaderboardFixTester();
  
  try {
    const success = await tester.runAllTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  } finally {
    await tester.cleanup();
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = { LeaderboardFixTester };
