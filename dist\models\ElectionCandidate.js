"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const electionCandidateSchema = new mongoose_1.Schema({
    candidateId: {
        type: String,
        required: [true, 'Candidate ID is required'],
        unique: true,
        index: true
    },
    electionId: {
        type: String,
        required: [true, 'Election ID is required'],
        validate: {
            validator: function (v) {
                return v.trim().length > 0;
            },
            message: 'Election ID cannot be empty'
        },
        index: true
    },
    userId: {
        type: String,
        required: [true, 'User ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'User ID must be a valid Discord snowflake'
        },
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    displayName: {
        type: String,
        required: [true, 'Display name is required'],
        maxlength: [100, 'Display name cannot exceed 100 characters'],
        minlength: [1, 'Display name cannot be empty'],
        trim: true
    },
    username: {
        type: String,
        required: [true, 'Username is required'],
        maxlength: [50, 'Username cannot exceed 50 characters'],
        minlength: [1, 'Username cannot be empty'],
        trim: true
    },
    bio: {
        type: String,
        required: [true, 'Candidate bio is required'],
        maxlength: [1000, 'Bio cannot exceed 1000 characters'],
        minlength: [10, 'Bio must be at least 10 characters'],
        trim: true
    },
    campaignMessage: {
        type: String,
        maxlength: [500, 'Campaign message cannot exceed 500 characters'],
        trim: true
    },
    withdrawn: {
        type: Boolean,
        default: false,
        index: true
    },
    withdrawnAt: {
        type: Date,
        index: true
    },
    disqualified: {
        type: Boolean,
        default: false,
        index: true
    },
    disqualifiedBy: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Disqualified by must be a valid Discord snowflake'
        }
    },
    disqualificationReason: {
        type: String,
        maxlength: [500, 'Disqualification reason cannot exceed 500 characters'],
        trim: true
    },
    nominationTime: {
        type: Date,
        required: [true, 'Nomination time is required'],
        default: Date.now,
        index: true
    }
}, {
    timestamps: true
});
electionCandidateSchema.index({ electionId: 1, withdrawn: 1, disqualified: 1 });
electionCandidateSchema.index({ electionId: 1, nominationTime: 1 });
electionCandidateSchema.index({ guildId: 1, userId: 1 });
electionCandidateSchema.index({ electionId: 1, userId: 1 }, { unique: true });
electionCandidateSchema.methods.isActive = function () {
    return !this.withdrawn && !this.disqualified;
};
electionCandidateSchema.methods.withdraw = function () {
    if (this.withdrawn) {
        throw new Error('Candidate has already withdrawn');
    }
    if (this.disqualified) {
        throw new Error('Cannot withdraw - candidate is disqualified');
    }
    this.withdrawn = true;
    this.withdrawnAt = new Date();
};
electionCandidateSchema.methods.disqualify = function (adminId, reason) {
    if (this.disqualified) {
        throw new Error('Candidate is already disqualified');
    }
    this.disqualified = true;
    this.disqualifiedBy = adminId;
    this.disqualificationReason = reason;
};
electionCandidateSchema.pre('save', function () {
    if (this.withdrawn && !this.withdrawnAt) {
        this.withdrawnAt = new Date();
    }
    if (this.disqualified) {
        if (!this.disqualifiedBy) {
            this.invalidate('disqualifiedBy', 'Disqualified by admin ID is required when disqualified');
        }
        if (!this.disqualificationReason) {
            this.invalidate('disqualificationReason', 'Disqualification reason is required when disqualified');
        }
    }
});
exports.default = (0, mongoose_1.model)('ElectionCandidate', electionCandidateSchema);
