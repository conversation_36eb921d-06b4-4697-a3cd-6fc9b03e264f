"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactionReward = void 0;
const mongoose_1 = require("mongoose");
const reactionRewardSchema = new mongoose_1.Schema({
    userId: {
        type: String,
        required: [true, 'User ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'User ID cannot be empty'
        }
    },
    messageId: {
        type: String,
        required: [true, 'Message ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Message ID cannot be empty'
        }
    },
    channelId: {
        type: String,
        required: [true, 'Channel ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Channel ID cannot be empty'
        }
    },
    coinsAwarded: {
        type: Number,
        required: true,
        min: [0, 'Coins awarded cannot be negative'],
        default: 5
    },
    timestamp: {
        type: Date,
        default: Date.now,
        required: true
    }
});
reactionRewardSchema.index({ userId: 1, messageId: 1 }, { unique: true });
reactionRewardSchema.index({ userId: 1, timestamp: -1 });
reactionRewardSchema.index({ messageId: 1, timestamp: -1 });
reactionRewardSchema.index({ channelId: 1, timestamp: -1 });
exports.ReactionReward = (0, mongoose_1.model)('ReactionReward', reactionRewardSchema);
