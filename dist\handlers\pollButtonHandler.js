"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PollButtonHandler = void 0;
const errorHandler_1 = require("../utils/errorHandler");
class PollButtonHandler {
    constructor(app) {
        this.app = app;
        this.logger = app.logger;
        this.pollService = app.getService('PollService');
        if (!this.pollService) {
            throw new Error('PollService not available for PollButtonHandler');
        }
        this.logger.info('[PollButtonHandler] Poll button handler initialized with new architecture');
        console.log('🎯 REBUILT POLL BUTTON HANDLER ACTIVE - New member validation system loaded');
        console.log('📊 Button Handler Debug: Initialized with new architecture');
    }
    async handlePollButton(interaction) {
        try {
            const customId = interaction.customId;
            const parts = customId.split('_');
            if (parts.length < 3 || parts[0] !== 'poll') {
                throw new errorHandler_1.ValidationError('Invalid poll button interaction');
            }
            const action = parts[1];
            const pollId = parts[2];
            this.logger.debug('[PollButtonHandler] Processing poll button interaction', {
                customId,
                action,
                pollId,
                userId: interaction.user.id,
                guildId: interaction.guild?.id
            });
            if (!interaction.guild || !interaction.member) {
                throw new errorHandler_1.ValidationError('This action can only be performed in a server');
            }
            const member = interaction.member;
            if (!member.roles || !member.roles.cache) {
                this.logger.error('[PollButtonHandler] Invalid member object', {
                    userId: interaction.user.id,
                    pollId,
                    hasMember: !!member,
                    hasRoles: !!member.roles,
                    hasCache: !!(member.roles?.cache)
                });
                throw new errorHandler_1.ValidationError('Unable to verify your roles. Please try again.');
            }
            switch (action) {
                case 'vote':
                    await this.handleVote(interaction, pollId, member);
                    break;
                case 'end':
                    await this.handleEndPoll(interaction, pollId, member);
                    break;
                default:
                    throw new errorHandler_1.ValidationError(`Unknown poll action: ${action}`);
            }
        }
        catch (error) {
            this.logger.error('[PollButtonHandler] Error handling poll button', {
                error: error instanceof Error ? error.message : 'Unknown error',
                customId: interaction.customId,
                userId: interaction.user.id,
                guildId: interaction.guild?.id
            });
            const errorMessage = error instanceof errorHandler_1.ValidationError
                ? error.message
                : 'An error occurred while processing your request. Please try again.';
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({
                    content: `❌ **Error:** ${errorMessage}`,
                    ephemeral: true
                });
            }
            else {
                await interaction.reply({
                    content: `❌ **Error:** ${errorMessage}`,
                    ephemeral: true
                });
            }
        }
    }
    async handleVote(interaction, pollId, member) {
        try {
            const parts = interaction.customId.split('_');
            if (parts.length < 4) {
                throw new errorHandler_1.ValidationError('Invalid vote button format');
            }
            const optionIndex = parseInt(parts[3]);
            if (isNaN(optionIndex) || optionIndex < 0) {
                throw new errorHandler_1.ValidationError('Invalid poll option');
            }
            this.logger.info('[PollButtonHandler] Processing vote', {
                pollId,
                userId: interaction.user.id,
                optionIndex,
                guildId: member.guild.id
            });
            await interaction.deferReply({ ephemeral: true });
            await this.pollService.castVote(pollId, interaction.user.id, optionIndex, member);
            const poll = await this.pollService.getPoll(pollId);
            if (!poll) {
                throw new errorHandler_1.ValidationError('Poll not found');
            }
            const { embed, components } = this.pollService.createPollEmbed(poll);
            await interaction.message.edit({
                embeds: [embed],
                components
            });
            await interaction.editReply({
                content: `✅ Your vote has been recorded for option ${optionIndex + 1}: **${poll.options[optionIndex]}**`
            });
            this.logger.info('[PollButtonHandler] Vote processed successfully', {
                pollId,
                userId: interaction.user.id,
                optionIndex,
                totalVotes: poll.totalVotes
            });
        }
        catch (error) {
            this.logger.error('[PollButtonHandler] Error processing vote', {
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
                pollId,
                userId: interaction.user.id
            });
            let errorMessage;
            if (error instanceof errorHandler_1.ValidationError) {
                errorMessage = error.message;
            }
            else if (error instanceof Error) {
                this.logger.error('[PollButtonHandler] Unexpected error details', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                });
                errorMessage = 'Failed to process your vote. Please try again.';
            }
            else {
                errorMessage = 'An unexpected error occurred. Please try again.';
            }
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `❌ **Error:** ${errorMessage}`
                });
            }
            else {
                await interaction.reply({
                    content: `❌ **Error:** ${errorMessage}`,
                    ephemeral: true
                });
            }
        }
    }
    async handleEndPoll(interaction, pollId, member) {
        try {
            this.logger.info('[PollButtonHandler] Processing end poll request', {
                pollId,
                userId: interaction.user.id,
                guildId: member.guild.id
            });
            if (!member.permissions) {
                this.logger.error('[PollButtonHandler] Member permissions not available for end poll', {
                    userId: interaction.user.id,
                    pollId,
                    memberType: typeof member
                });
                throw new errorHandler_1.ValidationError('Unable to verify your permissions. Please try again.');
            }
            await interaction.deferReply({ ephemeral: true });
            const poll = await this.pollService.endPoll(pollId, interaction.user.id, member);
            const { embed, components } = this.pollService.createPollEmbed(poll);
            await interaction.message.edit({
                embeds: [embed],
                components
            });
            await interaction.editReply({
                content: `✅ Poll ended successfully. Final results are now displayed.`
            });
            this.logger.info('[PollButtonHandler] Poll ended successfully', {
                pollId,
                endedBy: interaction.user.id,
                totalVotes: poll.totalVotes,
                totalVoteWeight: poll.totalVoteWeight
            });
        }
        catch (error) {
            this.logger.error('[PollButtonHandler] Error ending poll', {
                error: error instanceof Error ? error.message : 'Unknown error',
                pollId,
                userId: interaction.user.id
            });
            const errorMessage = error instanceof errorHandler_1.ValidationError
                ? error.message
                : 'Failed to end poll. Please try again.';
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `❌ **Error:** ${errorMessage}`
                });
            }
            else {
                await interaction.reply({
                    content: `❌ **Error:** ${errorMessage}`,
                    ephemeral: true
                });
            }
        }
    }
    static isPollButton(customId) {
        return customId.startsWith('poll_');
    }
    static getPollIdFromCustomId(customId) {
        const parts = customId.split('_');
        return parts.length >= 3 ? parts[2] : null;
    }
}
exports.PollButtonHandler = PollButtonHandler;
