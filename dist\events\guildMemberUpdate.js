"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuildMemberUpdateEventHandler = void 0;
const base_1 = require("./base");
class GuildMemberUpdateEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'guildMemberUpdate');
        this.name = 'guildMemberUpdate';
    }
    async execute(oldMember, newMember) {
        try {
            const fullOldMember = await this.ensureFullMember(oldMember);
            if (!fullOldMember) {
                return;
            }
            const addedRoles = newMember.roles.cache.filter(role => !fullOldMember.roles.cache.has(role.id));
            const removedRoles = fullOldMember.roles.cache.filter(role => !newMember.roles.cache.has(role.id));
            if (addedRoles.size > 0) {
                await this.processAddedRoles(newMember, addedRoles);
            }
            if (removedRoles.size > 0) {
                await this.processRemovedRoles(newMember, removedRoles);
            }
        }
        catch (error) {
            this.handleError(error, {
                userId: newMember.user.id,
                guildId: newMember.guild.id,
                displayName: newMember.displayName,
            });
        }
    }
    async ensureFullMember(member) {
        if (member.partial) {
            try {
                return await member.fetch();
            }
            catch (error) {
                this.logger.error('[GuildMemberUpdate] Failed to fetch old member', { error });
                return null;
            }
        }
        return member;
    }
    async processAddedRoles(member, addedRoles) {
        for (const [roleId, role] of addedRoles) {
            try {
                this.logExecution(`Role added: ${role.name} to ${member.displayName}`, {
                    userId: member.user.id,
                    guildId: member.guild.id,
                    roleId: role.id,
                    roleName: role.name,
                });
                if (this.isFeatureEnabled('STARTER_BALANCE')) {
                    await this.processStarterBalance(member, role);
                }
                if (this.isFeatureEnabled('AUTO_MESSAGES')) {
                    await this.processRoleAddMessage(member, role);
                }
            }
            catch (error) {
                this.logger.error(`[GuildMemberUpdate] Failed to process role add for ${member.displayName} and role ${role.name}`, {
                    error,
                    userId: member.user.id,
                    roleId: role.id,
                });
            }
        }
    }
    async processRemovedRoles(member, removedRoles) {
        for (const [roleId, role] of removedRoles) {
            try {
                this.logExecution(`Role removed: ${role.name} from ${member.displayName}`, {
                    userId: member.user.id,
                    guildId: member.guild.id,
                    roleId: role.id,
                    roleName: role.name,
                });
                if (this.isFeatureEnabled('AUTO_MESSAGES')) {
                    await this.processRoleRemoveMessage(member, role);
                }
            }
            catch (error) {
                this.logger.error(`[GuildMemberUpdate] Failed to process role remove for ${member.displayName} and role ${role.name}`, {
                    error,
                    userId: member.user.id,
                    roleId: role.id,
                });
            }
        }
    }
    async processStarterBalance(member, role) {
        try {
            const { processStarterBalance } = await Promise.resolve().then(() => __importStar(require('../services/starterBalanceService')));
            const granted = await processStarterBalance(member, role);
            if (granted) {
                this.logger.info(`[GuildMemberUpdate] Granted starter balance to ${member.displayName} for role ${role.name}`);
            }
        }
        catch (error) {
            this.logger.error('[GuildMemberUpdate] Error processing starter balance', {
                error,
                userId: member.user.id,
                roleId: role.id,
            });
        }
    }
    async processRoleAddMessage(member, role) {
        try {
            const { processRoleChangeMessage } = await Promise.resolve().then(() => __importStar(require('../services/automessageService')));
            const roleAddResult = await processRoleChangeMessage(member, role, 'role_add');
            if (roleAddResult.sent) {
                this.logger.info(`[GuildMemberUpdate] Sent ${roleAddResult.templatesProcessed} role add message(s) to ${member.displayName} for role ${role.name}`);
            }
            if (roleAddResult.errors.length > 0) {
                this.logger.error(`[GuildMemberUpdate] Errors processing role add messages for ${member.displayName}`, {
                    errors: roleAddResult.errors,
                });
            }
        }
        catch (error) {
            this.logger.error('[GuildMemberUpdate] Error processing role add message', {
                error,
                userId: member.user.id,
                roleId: role.id,
            });
        }
    }
    async processRoleRemoveMessage(member, role) {
        try {
            const { processRoleChangeMessage } = await Promise.resolve().then(() => __importStar(require('../services/automessageService')));
            const roleRemoveResult = await processRoleChangeMessage(member, role, 'role_remove');
            if (roleRemoveResult.sent) {
                this.logger.info(`[GuildMemberUpdate] Sent ${roleRemoveResult.templatesProcessed} role remove message(s) to ${member.displayName} for role ${role.name}`);
            }
            if (roleRemoveResult.errors.length > 0) {
                this.logger.error(`[GuildMemberUpdate] Errors processing role remove messages for ${member.displayName}`, {
                    errors: roleRemoveResult.errors,
                });
            }
        }
        catch (error) {
            this.logger.error('[GuildMemberUpdate] Error processing role remove message', {
                error,
                userId: member.user.id,
                roleId: role.id,
            });
        }
    }
}
exports.GuildMemberUpdateEventHandler = GuildMemberUpdateEventHandler;
