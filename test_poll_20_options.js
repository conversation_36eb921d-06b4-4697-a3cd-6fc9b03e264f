/**
 * Test Script for 20-Option Poll System
 * 
 * This script tests the enhanced poll system that now supports up to 20 options
 * instead of the previous 8-option limit.
 */

const { PollService } = require('./dist/services/poll/PollService');
const { ValidationError } = require('./dist/utils/errorHandler');

// Mock application context for testing
const mockApp = {
  getService: (serviceName) => {
    if (serviceName === 'EconomyService') {
      return {
        getUserBalance: async (userId, guildId) => ({ balance: 1000 })
      };
    }
    return null;
  },
  logger: {
    info: console.log,
    warn: console.warn,
    error: console.error,
    debug: console.log
  }
};

// Test configuration validation with various option counts
async function testPollValidation() {
  console.log('\n🧪 Testing Poll Validation with Different Option Counts...\n');
  
  const pollService = new PollService(mockApp);
  
  // Test cases for different option counts
  const testCases = [
    {
      name: 'Single Option (Should Fail)',
      options: ['Option 1'],
      shouldPass: false,
      expectedError: 'At least 2 poll options are required'
    },
    {
      name: 'Two Options (Minimum Valid)',
      options: ['Option 1', 'Option 2'],
      shouldPass: true
    },
    {
      name: 'Eight Options (Old Maximum)',
      options: Array.from({length: 8}, (_, i) => `Option ${i + 1}`),
      shouldPass: true
    },
    {
      name: 'Fifteen Options (Mid-Range)',
      options: Array.from({length: 15}, (_, i) => `Option ${i + 1}`),
      shouldPass: true
    },
    {
      name: 'Twenty Options (New Maximum)',
      options: Array.from({length: 20}, (_, i) => `Option ${i + 1}`),
      shouldPass: true
    },
    {
      name: 'Twenty-One Options (Should Fail)',
      options: Array.from({length: 21}, (_, i) => `Option ${i + 1}`),
      shouldPass: false,
      expectedError: 'Maximum 20 poll options allowed'
    }
  ];

  for (const testCase of testCases) {
    try {
      const config = {
        title: 'Test Poll',
        description: 'Test Description',
        options: testCase.options,
        eligibleVoterRoles: ['123456789012345678'], // Valid Discord snowflake
        guildId: '123456789012345678',
        channelId: '123456789012345678',
        createdBy: '123456789012345678'
      };

      // Use the private method through reflection for testing
      pollService.validatePollConfig(config);
      
      if (testCase.shouldPass) {
        console.log(`✅ ${testCase.name}: PASSED (${testCase.options.length} options)`);
      } else {
        console.log(`❌ ${testCase.name}: FAILED - Should have thrown error but didn't`);
      }
    } catch (error) {
      if (!testCase.shouldPass && error.message.includes(testCase.expectedError)) {
        console.log(`✅ ${testCase.name}: PASSED - Correctly rejected (${error.message})`);
      } else if (testCase.shouldPass) {
        console.log(`❌ ${testCase.name}: FAILED - Unexpected error: ${error.message}`);
      } else {
        console.log(`❌ ${testCase.name}: FAILED - Wrong error message: ${error.message}`);
      }
    }
  }
}

// Test UI button layout for different option counts
function testButtonLayout() {
  console.log('\n🎨 Testing Button Layout for Different Option Counts...\n');
  
  const testCases = [
    { optionCount: 2, expectedRows: 2 }, // 1 row for buttons + 1 for end button
    { optionCount: 5, expectedRows: 2 }, // 1 row for buttons + 1 for end button
    { optionCount: 8, expectedRows: 3 }, // 2 rows for buttons + 1 for end button
    { optionCount: 15, expectedRows: 4 }, // 3 rows for buttons + 1 for end button
    { optionCount: 20, expectedRows: 5 }  // 4 rows for buttons + 1 for end button
  ];

  for (const testCase of testCases) {
    const options = Array.from({length: testCase.optionCount}, (_, i) => `Option ${i + 1}`);
    
    // Simulate the button layout logic
    const maxVotingButtons = Math.min(options.length, 20);
    const buttonsPerRow = 5;
    const maxVotingRows = 4;
    
    let componentRows = 0;
    
    // Count voting button rows
    for (let row = 0; row < maxVotingRows && row * buttonsPerRow < maxVotingButtons; row++) {
      componentRows++;
    }
    
    // Add end button row
    componentRows++;
    
    if (componentRows === testCase.expectedRows) {
      console.log(`✅ ${testCase.optionCount} options: ${componentRows} rows (Expected: ${testCase.expectedRows})`);
    } else {
      console.log(`❌ ${testCase.optionCount} options: ${componentRows} rows (Expected: ${testCase.expectedRows})`);
    }
  }
}

// Test Discord limits compliance
function testDiscordLimits() {
  console.log('\n📏 Testing Discord Limits Compliance...\n');
  
  const maxButtonsPerRow = 5;
  const maxRows = 5;
  const maxTotalButtons = maxButtonsPerRow * (maxRows - 1); // Reserve 1 row for end button
  
  console.log(`Discord Limits:`);
  console.log(`- Max buttons per row: ${maxButtonsPerRow}`);
  console.log(`- Max rows total: ${maxRows}`);
  console.log(`- Max voting buttons: ${maxTotalButtons} (${maxRows - 1} rows × ${maxButtonsPerRow} buttons)`);
  console.log(`- Our new limit: 20 options`);
  
  if (20 <= maxTotalButtons) {
    console.log(`✅ 20-option limit is within Discord's constraints`);
  } else {
    console.log(`❌ 20-option limit exceeds Discord's constraints`);
  }
}

// Main test execution
async function runTests() {
  console.log('🚀 Starting Poll System 20-Option Enhancement Tests\n');
  console.log('=' .repeat(60));
  
  try {
    await testPollValidation();
    testButtonLayout();
    testDiscordLimits();
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 All tests completed! Check results above.');
    console.log('\n📋 Summary:');
    console.log('- Poll validation now accepts 2-20 options (previously 2-8)');
    console.log('- UI button layout scales properly up to 20 options');
    console.log('- Discord limits are respected (5 buttons/row, 5 rows max)');
    console.log('- Error messages updated to reflect new limits');
    
  } catch (error) {
    console.error('\n❌ Test execution failed:', error.message);
    console.error(error.stack);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  testPollValidation,
  testButtonLayout,
  testDiscordLimits,
  runTests
};
