"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Suggestion = void 0;
const mongoose_1 = require("mongoose");
const suggestionVoteSchema = new mongoose_1.Schema({
    userId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'User ID must be a valid Discord snowflake'
        }
    },
    type: {
        type: String,
        enum: ['upvote', 'downvote'],
        required: true
    },
    timestamp: {
        type: Date,
        default: Date.now
    }
}, { _id: false });
const suggestionSchema = new mongoose_1.Schema({
    suggestionId: {
        type: String,
        required: true,
        unique: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Suggestion ID cannot be empty'
        }
    },
    guildId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    channelId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Channel ID must be a valid Discord snowflake'
        }
    },
    messageId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Message ID must be a valid Discord snowflake'
        }
    },
    authorId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Author ID must be a valid Discord snowflake'
        }
    },
    content: {
        type: String,
        required: true,
        maxlength: [2000, 'Suggestion content cannot exceed 2000 characters'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Suggestion content cannot be empty'
        }
    },
    votes: {
        type: [suggestionVoteSchema],
        default: []
    },
    upvoteCount: {
        type: Number,
        default: 0,
        min: 0
    },
    downvoteCount: {
        type: Number,
        default: 0,
        min: 0
    },
    isEdited: {
        type: Boolean,
        default: false
    },
    editHistory: {
        type: [{
                previousContent: {
                    type: String,
                    required: true
                },
                editedAt: {
                    type: Date,
                    default: Date.now
                }
            }],
        default: []
    },
    expiresAt: {
        type: Date,
        default: function () {
            return new Date(Date.now() + 24 * 60 * 60 * 1000);
        },
        expires: 0
    }
}, {
    timestamps: true
});
suggestionSchema.index({ suggestionId: 1 });
suggestionSchema.index({ guildId: 1, channelId: 1 });
suggestionSchema.index({ authorId: 1 });
suggestionSchema.index({ messageId: 1 });
suggestionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });
exports.Suggestion = (0, mongoose_1.model)('Suggestion', suggestionSchema);
exports.default = exports.Suggestion;
