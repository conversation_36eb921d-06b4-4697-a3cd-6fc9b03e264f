"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeNotificationManager = void 0;
const BaseService_1 = require("../../base/BaseService");
const tradeEmbedBuilder_1 = require("../../../utils/tradeEmbedBuilder");
const embedBuilder_1 = require("../../../utils/embedBuilder");
class TradeNotificationManager extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'TradeNotificationManager';
    }
    async initialize() {
        this.logger.info('[TradeNotificationManager] Trade notification manager initialized');
    }
    async sendTradeProposal(trade, client) {
        this.logOperation('Sending trade proposal notification', { tradeId: trade.tradeId });
        try {
            const seller = await client.users.fetch(trade.sellerId);
            const buyer = await client.users.fetch(trade.buyerId);
            const embed = (0, tradeEmbedBuilder_1.createTradeProposalEmbed)(trade, seller, buyer);
            const buttons = (0, tradeEmbedBuilder_1.createTradeProposalButtons)(trade.tradeId);
            const recipient = trade.initiatedBy === 'SELLER' ? buyer : seller;
            const initiator = trade.initiatedBy === 'SELLER' ? seller : buyer;
            try {
                const dmChannel = await recipient.createDM();
                await dmChannel.send({
                    content: `${embedBuilder_1.EMOJIS.TRADE.PROPOSAL} **New Trade Proposal**\n\n${initiator.displayName} has sent you a trade proposal!`,
                    embeds: [embed],
                    components: [buttons]
                });
                this.logOperation('Trade proposal DM sent', {
                    tradeId: trade.tradeId,
                    recipient: recipient.id
                });
            }
            catch (dmError) {
                this.logger.warn('Could not send trade proposal DM', {
                    tradeId: trade.tradeId,
                    recipient: recipient.id,
                    error: dmError
                });
            }
            try {
                const guild = await client.guilds.fetch(trade.guildId);
            }
            catch (guildError) {
                this.logger.warn('Could not access guild for trade notification', {
                    tradeId: trade.tradeId,
                    guildId: trade.guildId,
                    error: guildError
                });
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'send_trade_proposal', tradeId: trade.tradeId });
        }
    }
    async sendTradeAccepted(trade, client) {
        this.logOperation('Sending trade accepted notification', { tradeId: trade.tradeId });
        try {
            const seller = await client.users.fetch(trade.sellerId);
            const buyer = await client.users.fetch(trade.buyerId);
            const embed = (0, tradeEmbedBuilder_1.createActiveTradeEmbed)(trade, seller, buyer);
            const parties = [seller, buyer];
            for (const user of parties) {
                try {
                    const userConfirmed = (user.id === trade.sellerId && trade.sellerConfirmed) ||
                        (user.id === trade.buyerId && trade.buyerConfirmed);
                    const buttons = (0, tradeEmbedBuilder_1.createActiveTradeButtons)(trade.tradeId, userConfirmed);
                    const dmChannel = await user.createDM();
                    await dmChannel.send({
                        content: `${embedBuilder_1.EMOJIS.TRADE.ACTIVE} **Trade Activated**\n\nYour trade is now active! Funds have been locked in escrow.`,
                        embeds: [embed],
                        components: [buttons]
                    });
                    this.logOperation('Trade accepted DM sent', {
                        tradeId: trade.tradeId,
                        recipient: user.id
                    });
                }
                catch (dmError) {
                    this.logger.warn('Could not send trade accepted DM', {
                        tradeId: trade.tradeId,
                        recipient: user.id,
                        error: dmError
                    });
                }
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'send_trade_accepted', tradeId: trade.tradeId });
        }
    }
    async sendTradeCompleted(trade, client) {
        this.logOperation('Sending trade completed notification', { tradeId: trade.tradeId });
        try {
            const seller = await client.users.fetch(trade.sellerId);
            const buyer = await client.users.fetch(trade.buyerId);
            const embed = (0, tradeEmbedBuilder_1.createCompletedTradeEmbed)(trade, seller, buyer);
            const parties = [seller, buyer];
            for (const user of parties) {
                try {
                    const dmChannel = await user.createDM();
                    await dmChannel.send({
                        content: `${embedBuilder_1.EMOJIS.TRADE.COMPLETED} **Trade Completed Successfully!**\n\nCongratulations! Your trade has been completed and funds have been released.`,
                        embeds: [embed]
                    });
                    this.logOperation('Trade completed DM sent', {
                        tradeId: trade.tradeId,
                        recipient: user.id
                    });
                }
                catch (dmError) {
                    this.logger.warn('Could not send trade completed DM', {
                        tradeId: trade.tradeId,
                        recipient: user.id,
                        error: dmError
                    });
                }
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'send_trade_completed', tradeId: trade.tradeId });
        }
    }
    async sendPartialConfirmation(trade, confirmingUserId, client) {
        this.logOperation('Sending partial confirmation notification', {
            tradeId: trade.tradeId,
            confirmingUserId
        });
        try {
            const seller = await client.users.fetch(trade.sellerId);
            const buyer = await client.users.fetch(trade.buyerId);
            const confirmingUser = await client.users.fetch(confirmingUserId);
            const otherPartyId = trade.getOtherParty(confirmingUserId);
            if (!otherPartyId)
                return;
            const otherParty = await client.users.fetch(otherPartyId);
            const embed = (0, tradeEmbedBuilder_1.createActiveTradeEmbed)(trade, seller, buyer);
            try {
                const otherPartyConfirmed = (otherPartyId === trade.sellerId && trade.sellerConfirmed) ||
                    (otherPartyId === trade.buyerId && trade.buyerConfirmed);
                const buttons = (0, tradeEmbedBuilder_1.createActiveTradeButtons)(trade.tradeId, otherPartyConfirmed);
                const dmChannel = await otherParty.createDM();
                await dmChannel.send({
                    content: `${embedBuilder_1.EMOJIS.TRADE.CONFIRMATION} **Trade Confirmation Update**\n\n${confirmingUser.displayName} has confirmed their part of the trade. Please confirm when you have completed your part.`,
                    embeds: [embed],
                    components: [buttons]
                });
                this.logOperation('Partial confirmation DM sent', {
                    tradeId: trade.tradeId,
                    recipient: otherPartyId
                });
            }
            catch (dmError) {
                this.logger.warn('Could not send partial confirmation DM', {
                    tradeId: trade.tradeId,
                    recipient: otherPartyId,
                    error: dmError
                });
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'send_partial_confirmation', tradeId: trade.tradeId });
        }
    }
    async sendTradeCancelled(trade, cancellingUserId, reason, client) {
        this.logOperation('Sending trade cancelled notification', {
            tradeId: trade.tradeId,
            cancellingUserId,
            reason
        });
        try {
            const seller = await client.users.fetch(trade.sellerId);
            const buyer = await client.users.fetch(trade.buyerId);
            const cancellingUser = await client.users.fetch(cancellingUserId);
            const embed = (0, tradeEmbedBuilder_1.createCancelledTradeEmbed)(trade, seller, buyer, reason);
            const otherPartyId = trade.getOtherParty(cancellingUserId);
            if (otherPartyId) {
                try {
                    const otherParty = await client.users.fetch(otherPartyId);
                    const dmChannel = await otherParty.createDM();
                    await dmChannel.send({
                        content: `${embedBuilder_1.EMOJIS.TRADE.CANCELLED} **Trade Cancelled**\n\n${cancellingUser.displayName} has cancelled the trade.`,
                        embeds: [embed]
                    });
                    this.logOperation('Trade cancelled DM sent', {
                        tradeId: trade.tradeId,
                        recipient: otherPartyId
                    });
                }
                catch (dmError) {
                    this.logger.warn('Could not send trade cancelled DM', {
                        tradeId: trade.tradeId,
                        recipient: otherPartyId,
                        error: dmError
                    });
                }
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'send_trade_cancelled', tradeId: trade.tradeId });
        }
    }
    async sendTradeExpired(trade, client) {
        this.logOperation('Sending trade expired notification', { tradeId: trade.tradeId });
        try {
            const seller = await client.users.fetch(trade.sellerId);
            const buyer = await client.users.fetch(trade.buyerId);
            const embed = (0, tradeEmbedBuilder_1.createCancelledTradeEmbed)(trade, seller, buyer, 'Trade expired');
            const parties = [seller, buyer];
            for (const user of parties) {
                try {
                    const dmChannel = await user.createDM();
                    await dmChannel.send({
                        content: `${embedBuilder_1.EMOJIS.TRADE.EXPIRED} **Trade Expired**\n\nYour trade has expired and any escrowed funds have been refunded.`,
                        embeds: [embed]
                    });
                    this.logOperation('Trade expired DM sent', {
                        tradeId: trade.tradeId,
                        recipient: user.id
                    });
                }
                catch (dmError) {
                    this.logger.warn('Could not send trade expired DM', {
                        tradeId: trade.tradeId,
                        recipient: user.id,
                        error: dmError
                    });
                }
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'send_trade_expired', tradeId: trade.tradeId });
        }
    }
    async sendTradeWarning(trade, hoursRemaining, client) {
        this.logOperation('Sending trade warning notification', {
            tradeId: trade.tradeId,
            hoursRemaining
        });
        try {
            const seller = await client.users.fetch(trade.sellerId);
            const buyer = await client.users.fetch(trade.buyerId);
            const embed = await (0, embedBuilder_1.createServerAdminEmbed)(trade.guildId, `${embedBuilder_1.EMOJIS.TRADE.TIMER} Trade Expiring Soon`);
            embed.setDescription(`Your trade will expire in ${hoursRemaining} hour(s)!`);
            embed.addFields([
                {
                    name: `${embedBuilder_1.EMOJIS.MISC.ID} Trade ID`,
                    value: `\`${trade.tradeId}\``,
                    inline: true
                },
                {
                    name: `${embedBuilder_1.EMOJIS.TRADE.PACKAGE} Item`,
                    value: trade.itemDescription,
                    inline: true
                },
                {
                    name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
                    value: `${trade.amount} coins`,
                    inline: true
                },
                {
                    name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Expires`,
                    value: `<t:${Math.floor(trade.expiresAt.getTime() / 1000)}:R>`,
                    inline: false
                }
            ]);
            const parties = [seller, buyer];
            for (const user of parties) {
                try {
                    const userConfirmed = (user.id === trade.sellerId && trade.sellerConfirmed) ||
                        (user.id === trade.buyerId && trade.buyerConfirmed);
                    let content = `${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Trade Expiring Soon**\n\n`;
                    if (trade.state === 'ACTIVE' && !userConfirmed) {
                        content += 'Please confirm your trade completion if you have finished your part!';
                    }
                    else if (trade.state === 'PROPOSED') {
                        content += 'Please accept or decline this trade proposal soon!';
                    }
                    else {
                        content += 'Your trade is expiring soon!';
                    }
                    const dmChannel = await user.createDM();
                    await dmChannel.send({
                        content,
                        embeds: [embed]
                    });
                    this.logOperation('Trade warning DM sent', {
                        tradeId: trade.tradeId,
                        recipient: user.id,
                        hoursRemaining
                    });
                }
                catch (dmError) {
                    this.logger.warn('Could not send trade warning DM', {
                        tradeId: trade.tradeId,
                        recipient: user.id,
                        error: dmError
                    });
                }
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'send_trade_warning', tradeId: trade.tradeId });
        }
    }
    async sendDisputeInitiated(trade, disputingUserId, client) {
        this.logOperation('Sending dispute initiated notification', {
            tradeId: trade.tradeId,
            disputingUserId
        });
        try {
            const disputingUser = await client.users.fetch(disputingUserId);
            const otherPartyId = trade.getOtherParty(disputingUserId);
            if (otherPartyId) {
                const otherParty = await client.users.fetch(otherPartyId);
                const embed = await (0, embedBuilder_1.createServerAdminEmbed)(trade.guildId, `${embedBuilder_1.EMOJIS.TRADE.DISPUTED} Trade Dispute Initiated`);
                embed.setDescription(`A dispute has been initiated for your trade.`);
                embed.addFields([
                    {
                        name: `${embedBuilder_1.EMOJIS.MISC.ID} Trade ID`,
                        value: `\`${trade.tradeId}\``,
                        inline: true
                    },
                    {
                        name: `${embedBuilder_1.EMOJIS.MISC.USER} Disputed By`,
                        value: disputingUser.displayName,
                        inline: true
                    },
                    {
                        name: `${embedBuilder_1.EMOJIS.TRADE.PACKAGE} Item`,
                        value: trade.itemDescription,
                        inline: false
                    },
                    {
                        name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Next Steps`,
                        value: 'An admin will review this dispute. You may be contacted for additional information.',
                        inline: false
                    }
                ]);
                try {
                    const dmChannel = await otherParty.createDM();
                    await dmChannel.send({
                        content: `${embedBuilder_1.EMOJIS.TRADE.DISPUTED} **Trade Dispute**\n\n${disputingUser.displayName} has initiated a dispute for your trade.`,
                        embeds: [embed]
                    });
                    this.logOperation('Dispute initiated DM sent', {
                        tradeId: trade.tradeId,
                        recipient: otherPartyId
                    });
                }
                catch (dmError) {
                    this.logger.warn('Could not send dispute initiated DM', {
                        tradeId: trade.tradeId,
                        recipient: otherPartyId,
                        error: dmError
                    });
                }
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'send_dispute_initiated', tradeId: trade.tradeId });
        }
    }
    async sendDisputeResolved(trade, resolution, client) {
        this.logOperation('Sending dispute resolved notification', {
            tradeId: trade.tradeId,
            resolution
        });
        try {
            const seller = await client.users.fetch(trade.sellerId);
            const buyer = await client.users.fetch(trade.buyerId);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(trade.guildId, `${embedBuilder_1.EMOJIS.TRADE.SCALES} Dispute Resolved`);
            embed.setDescription(`The dispute for your trade has been resolved.`);
            embed.addFields([
                {
                    name: `${embedBuilder_1.EMOJIS.MISC.ID} Trade ID`,
                    value: `\`${trade.tradeId}\``,
                    inline: true
                },
                {
                    name: `${embedBuilder_1.EMOJIS.ADMIN.SCALES} Resolution`,
                    value: resolution,
                    inline: true
                },
                {
                    name: `${embedBuilder_1.EMOJIS.TRADE.PACKAGE} Item`,
                    value: trade.itemDescription,
                    inline: false
                }
            ]);
            const parties = [seller, buyer];
            for (const user of parties) {
                try {
                    const dmChannel = await user.createDM();
                    await dmChannel.send({
                        content: `${embedBuilder_1.EMOJIS.ADMIN.SCALES} **Dispute Resolved**\n\nThe dispute for your trade has been resolved by an administrator.`,
                        embeds: [embed]
                    });
                    this.logOperation('Dispute resolved DM sent', {
                        tradeId: trade.tradeId,
                        recipient: user.id
                    });
                }
                catch (dmError) {
                    this.logger.warn('Could not send dispute resolved DM', {
                        tradeId: trade.tradeId,
                        recipient: user.id,
                        error: dmError
                    });
                }
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'send_dispute_resolved', tradeId: trade.tradeId });
        }
    }
}
exports.TradeNotificationManager = TradeNotificationManager;
