"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = __importDefault(require("../models/User"));
const Transaction_1 = __importDefault(require("../models/Transaction"));
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const mongoose_1 = __importDefault(require("mongoose"));
function logDatabaseOperation(operation, details) {
    console.log(`[Database Operation] ${operation}:`, JSON.stringify(details, null, 2));
}
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('pay')
        .setDescription('Pay another user coins')
        .addUserOption(option => option.setName('user')
        .setDescription('The user to pay')
        .setRequired(true))
        .addIntegerOption(option => option.setName('amount')
        .setDescription('Amount to pay')
        .setRequired(true)),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (mongoose_1.default.connection.readyState !== 1) {
            await interaction.reply({
                content: 'Database is not connected. Please try again in a moment.',
                ephemeral: true
            });
            throw new Error('Attempted to use /pay command before MongoDB connection was established.');
        }
        const senderId = interaction.user.id;
        const recipient = interaction.options.getUser('user', true);
        const amount = interaction.options.getInteger('amount', true);
        const guildId = interaction.guild?.id;
        if (!guildId) {
            throw new Error('This command can only be used in a server');
        }
        if (recipient.id === senderId) {
            throw new errorHandler_1.ValidationError('You cannot pay yourself.', 'recipient', 'must be a different user');
        }
        if (amount <= 0) {
            throw new errorHandler_1.ValidationError('Amount must be greater than zero.', 'amount', 'must be a positive number');
        }
        if (recipient.bot) {
            throw new errorHandler_1.ValidationError('You cannot pay a bot.', 'recipient', 'must be a real user, not a bot');
        }
        const session = await mongoose_1.default.startSession();
        logDatabaseOperation('Starting Payment Transaction', {
            senderId,
            recipientId: recipient.id,
            amount
        });
        try {
            await session.withTransaction(async () => {
                const sender = await User_1.default.findOneAndUpdate({ discordId: senderId, guildId: guildId }, { $setOnInsert: { discordId: senderId, guildId: guildId, balance: 0 } }, { upsert: true, new: true, session });
                if (sender.balance < amount) {
                    throw new errorHandler_1.InsufficientFundsError(amount, sender.balance);
                }
                await User_1.default.findOneAndUpdate({ discordId: senderId, guildId: guildId }, { $inc: { balance: -amount } }, { session });
                await User_1.default.findOneAndUpdate({ discordId: recipient.id, guildId: guildId }, {
                    $inc: { balance: amount },
                    $setOnInsert: { discordId: recipient.id, guildId: guildId }
                }, { upsert: true, session });
                await Transaction_1.default.create([
                    {
                        discordId: senderId,
                        guildId: guildId,
                        type: 'pay',
                        amount: -amount,
                        details: `Paid to ${recipient.tag}`,
                        timestamp: new Date()
                    },
                    {
                        discordId: recipient.id,
                        guildId: guildId,
                        type: 'pay',
                        amount: amount,
                        details: `Received from ${interaction.user.tag}`,
                        timestamp: new Date()
                    }
                ], { session });
                logDatabaseOperation('Payment Transaction Complete', {
                    senderId,
                    recipientId: recipient.id,
                    amount
                });
            });
            const formattedAmount = await (0, embedBuilder_1.formatServerCoins)(guildId, amount);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Payment Successful!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.MONEY} **Transaction Complete**\n\n` +
                `${formattedAmount} has been transferred to **${recipient.displayName}**!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ACTIONS.LIGHTNING} From`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ACTIONS.TARGET} To`,
                value: `**${recipient.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
                value: formattedAmount,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Transaction Time`,
                value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: false
            })
                .setFooter({
                text: 'Thank you for using the economy system!'
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.InsufficientFundsError) {
                throw error;
            }
            else if (error instanceof Error && error.name === 'ValidationError') {
                throw new errorHandler_1.DatabaseError('Transaction validation failed', error);
            }
            else if (error instanceof Error && error.name === 'MongoServerError') {
                const err = error;
                if (err.code === 11000) {
                    throw new errorHandler_1.DatabaseError('Transaction conflict detected', error);
                }
                throw new errorHandler_1.DatabaseError('Database operation failed', error);
            }
            else if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError('Payment processing failed', error);
            }
            throw new errorHandler_1.DatabaseError('Unexpected error during payment');
        }
        finally {
            await session.endSession();
        }
    })
};
