"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaxConfiguration = void 0;
const mongoose_1 = require("mongoose");
const taxConfigurationSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        unique: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Guild ID cannot be empty'
        }
    },
    enabled: {
        type: Boolean,
        default: false,
        required: true
    },
    frequency: {
        type: Number,
        required: function () {
            return this.enabled;
        },
        min: [1, 'Tax frequency must be at least 1 week'],
        max: [52, 'Tax frequency cannot exceed 52 weeks'],
        validate: {
            validator: function (v) {
                return Number.isInteger(v) && v > 0;
            },
            message: 'Tax frequency must be a positive integer'
        }
    },
    amount: {
        type: Number,
        required: function () {
            return this.enabled;
        },
        min: [1, 'Tax amount must be at least 1 PLC'],
        max: [10000, 'Tax amount cannot exceed 10,000 PLC'],
        validate: {
            validator: function (v) {
                return Number.isInteger(v) && v > 0;
            },
            message: 'Tax amount must be a positive integer'
        }
    },
    taxType: {
        type: String,
        enum: ['fixed', 'percentage'],
        default: 'fixed',
        required: true
    },
    percentageValue: {
        type: Number,
        required: function () {
            return this.enabled && this.taxType === 'percentage';
        },
        min: [0.1, 'Tax percentage must be at least 0.1%'],
        max: [100, 'Tax percentage cannot exceed 100%'],
        validate: {
            validator: function (v) {
                if (this.taxType === 'percentage') {
                    return v != null && v >= 0.1 && v <= 100;
                }
                return true;
            },
            message: 'Tax percentage must be between 0.1% and 100%'
        }
    },
    roleId: {
        type: String,
        required: function () {
            return this.enabled;
        },
        validate: {
            validator: function (v) {
                return !v || (v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Role ID must be a valid Discord snowflake'
        }
    },
    roleName: {
        type: String,
        required: function () {
            return this.enabled;
        },
        maxlength: [100, 'Role name cannot exceed 100 characters']
    },
    lastTaxDate: {
        type: Date,
        default: null
    },
    nextTaxDate: {
        type: Date,
        default: null
    }
}, {
    timestamps: true
});
taxConfigurationSchema.index({ guildId: 1 });
taxConfigurationSchema.index({ enabled: 1, nextTaxDate: 1 });
taxConfigurationSchema.pre('save', function (next) {
    if (this.enabled && this.frequency) {
        const now = new Date();
        if (!this.lastTaxDate) {
            this.nextTaxDate = new Date(now.getTime() + (this.frequency * 7 * 24 * 60 * 60 * 1000));
        }
        else {
            this.nextTaxDate = new Date(this.lastTaxDate.getTime() + (this.frequency * 7 * 24 * 60 * 60 * 1000));
        }
    }
    else {
        this.nextTaxDate = undefined;
    }
    next();
});
exports.TaxConfiguration = (0, mongoose_1.model)('TaxConfiguration', taxConfigurationSchema);
