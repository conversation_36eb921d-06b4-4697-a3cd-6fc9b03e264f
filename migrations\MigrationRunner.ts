/**
 * Migration Runner
 * Handles execution and tracking of database migrations
 */

import mongoose from 'mongoose';
import { ILogger } from '../src/core/interfaces';
import { CreateTradeSystemMigration } from './001_create_trade_system';
import { ConvertToMultiServerMigration } from './002_convert_to_multi_server';

export interface Migration {
  id: string;
  name: string;
  description: string;
  version: string;
  up(): Promise<MigrationResult>;
  down(): Promise<MigrationResult>;
}

export interface MigrationResult {
  success: boolean;
  message: string;
  changes: string[];
  errors: string[];
}

export interface MigrationRecord {
  migrationId: string;
  name: string;
  version: string;
  executedAt: Date;
  executionTime: number;
  changes: string[];
  checksum: string;
}

export interface MigrationOptions {
  dryRun?: boolean;
  force?: boolean;
  target?: string;
}

/**
 * Migration Runner Class
 */
export class MigrationRunner {
  private db: mongoose.Connection;
  private logger: ILogger;
  private migrations: Map<string, Migration> = new Map();

  constructor(db: mongoose.Connection, logger: ILogger) {
    this.db = db;
    this.logger = logger;
    this.registerMigrations();
  }

  /**
   * Register all available migrations
   */
  private registerMigrations(): void {
    // Register migration 001
    const migration001 = new CreateTradeSystemMigration({
      db: this.db,
      logger: this.logger
    });

    this.migrations.set('001', {
      id: '001',
      name: 'Create Trade System',
      description: 'Creates all trade-related collections and indexes',
      version: '1.0.0',
      up: () => migration001.up(),
      down: () => migration001.down()
    });

    // Register migration 002
    const migration002 = new ConvertToMultiServerMigration({
      db: this.db,
      logger: this.logger
    });

    this.migrations.set('002', {
      id: '002',
      name: 'Convert to Multi-Server Architecture',
      description: 'Converts existing single-server data to guild-isolated multi-server architecture',
      version: '1.0.0',
      up: () => migration002.up(),
      down: () => migration002.down()
    });

    this.logger.info(`[MigrationRunner] Registered ${this.migrations.size} migrations`);
  }

  /**
   * Initialize migration tracking collection
   */
  async initialize(): Promise<void> {
    try {
      // Create migrations collection if it doesn't exist
      const collections = await this.db.db?.listCollections({ name: 'migrations' }).toArray();

      if (!collections || collections.length === 0) {
        await this.db.createCollection('migrations');
        this.logger.info('[MigrationRunner] Created migrations tracking collection');
      }

      // Create index for migration tracking
      await this.db.collection('migrations').createIndex({ migrationId: 1 }, { unique: true });
      
      this.logger.info('[MigrationRunner] Migration runner initialized');
    } catch (error) {
      this.logger.error('[MigrationRunner] Failed to initialize', { error });
      throw error;
    }
  }

  /**
   * Get list of executed migrations
   */
  async getExecutedMigrations(): Promise<MigrationRecord[]> {
    try {
      const records = await this.db.collection('migrations')
        .find({})
        .sort({ migrationId: 1 })
        .toArray();

      return records as unknown as MigrationRecord[];
    } catch (error) {
      this.logger.error('[MigrationRunner] Failed to get executed migrations', { error });
      throw error;
    }
  }

  /**
   * Get list of pending migrations
   */
  async getPendingMigrations(): Promise<Migration[]> {
    try {
      const executed = await this.getExecutedMigrations();
      const executedIds = new Set(executed.map(m => m.migrationId));
      
      return Array.from(this.migrations.values())
        .filter(migration => !executedIds.has(migration.id))
        .sort((a, b) => a.id.localeCompare(b.id));
    } catch (error) {
      this.logger.error('[MigrationRunner] Failed to get pending migrations', { error });
      throw error;
    }
  }

  /**
   * Run all pending migrations
   */
  async migrate(options: MigrationOptions = {}): Promise<MigrationResult[]> {
    const { dryRun = false, target } = options;
    const results: MigrationResult[] = [];

    try {
      this.logger.info('[MigrationRunner] Starting migration process', { dryRun, target });

      const pending = await this.getPendingMigrations();
      
      if (pending.length === 0) {
        this.logger.info('[MigrationRunner] No pending migrations');
        return results;
      }

      // Filter migrations if target is specified
      const migrationsToRun = target 
        ? pending.filter(m => m.id <= target)
        : pending;

      if (migrationsToRun.length === 0) {
        this.logger.info(`[MigrationRunner] No migrations to run up to target: ${target}`);
        return results;
      }

      this.logger.info(`[MigrationRunner] Running ${migrationsToRun.length} migration(s)`, {
        migrations: migrationsToRun.map(m => `${m.id}: ${m.name}`)
      });

      for (const migration of migrationsToRun) {
        const result = await this.runMigration(migration, 'up', dryRun);
        results.push(result);

        if (!result.success) {
          this.logger.error(`[MigrationRunner] Migration ${migration.id} failed, stopping`, {
            error: result.message
          });
          break;
        }
      }

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      this.logger.info('[MigrationRunner] Migration process completed', {
        successful,
        failed,
        total: results.length
      });

      return results;

    } catch (error) {
      this.logger.error('[MigrationRunner] Migration process failed', { error });
      throw error;
    }
  }

  /**
   * Rollback migrations
   */
  async rollback(options: MigrationOptions = {}): Promise<MigrationResult[]> {
    const { dryRun = false, target } = options;
    const results: MigrationResult[] = [];

    try {
      this.logger.info('[MigrationRunner] Starting rollback process', { dryRun, target });

      const executed = await this.getExecutedMigrations();
      
      if (executed.length === 0) {
        this.logger.info('[MigrationRunner] No migrations to rollback');
        return results;
      }

      // Determine which migrations to rollback
      const migrationsToRollback = target
        ? executed.filter(m => m.migrationId > target).reverse()
        : [executed[executed.length - 1]]; // Just the last one if no target

      if (migrationsToRollback.length === 0) {
        this.logger.info(`[MigrationRunner] No migrations to rollback to target: ${target}`);
        return results;
      }

      this.logger.info(`[MigrationRunner] Rolling back ${migrationsToRollback.length} migration(s)`, {
        migrations: migrationsToRollback.map(m => `${m.migrationId}: ${m.name}`)
      });

      for (const migrationRecord of migrationsToRollback) {
        const migration = this.migrations.get(migrationRecord.migrationId);
        
        if (!migration) {
          const error = `Migration ${migrationRecord.migrationId} not found in registry`;
          this.logger.error('[MigrationRunner] ' + error);
          results.push({
            success: false,
            message: error,
            changes: [],
            errors: [error]
          });
          continue;
        }

        const result = await this.runMigration(migration, 'down', dryRun);
        results.push(result);

        if (!result.success) {
          this.logger.error(`[MigrationRunner] Rollback of ${migration.id} failed, stopping`, {
            error: result.message
          });
          break;
        }
      }

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      this.logger.info('[MigrationRunner] Rollback process completed', {
        successful,
        failed,
        total: results.length
      });

      return results;

    } catch (error) {
      this.logger.error('[MigrationRunner] Rollback process failed', { error });
      throw error;
    }
  }

  /**
   * Get migration status
   */
  async getStatus(): Promise<{
    executed: MigrationRecord[];
    pending: Migration[];
    total: number;
  }> {
    const executed = await this.getExecutedMigrations();
    const pending = await this.getPendingMigrations();
    
    return {
      executed,
      pending,
      total: this.migrations.size
    };
  }

  /**
   * Run a single migration
   */
  private async runMigration(
    migration: Migration, 
    direction: 'up' | 'down', 
    dryRun: boolean
  ): Promise<MigrationResult> {
    const startTime = Date.now();
    
    try {
      this.logger.info(`[MigrationRunner] ${dryRun ? 'DRY RUN: ' : ''}Running migration ${migration.id} (${direction})`, {
        name: migration.name,
        description: migration.description
      });

      // Execute migration
      const result = direction === 'up' ? await migration.up() : await migration.down();
      const executionTime = Date.now() - startTime;

      if (result.success && !dryRun) {
        if (direction === 'up') {
          // Record successful migration
          await this.recordMigration(migration, executionTime, result.changes);
        } else {
          // Remove migration record on rollback
          await this.removeMigrationRecord(migration.id);
        }
      }

      this.logger.info(`[MigrationRunner] Migration ${migration.id} ${direction} completed`, {
        success: result.success,
        executionTime,
        changes: result.changes.length,
        dryRun
      });

      return result;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.logger.error(`[MigrationRunner] Migration ${migration.id} ${direction} failed`, {
        error,
        executionTime
      });

      return {
        success: false,
        message: errorMessage,
        changes: [],
        errors: [errorMessage]
      };
    }
  }

  /**
   * Record successful migration
   */
  private async recordMigration(migration: Migration, executionTime: number, changes: string[]): Promise<void> {
    const record: MigrationRecord = {
      migrationId: migration.id,
      name: migration.name,
      version: migration.version,
      executedAt: new Date(),
      executionTime,
      changes,
      checksum: this.generateChecksum(migration)
    };

    await this.db.collection('migrations').insertOne(record);
  }

  /**
   * Remove migration record
   */
  private async removeMigrationRecord(migrationId: string): Promise<void> {
    await this.db.collection('migrations').deleteOne({ migrationId });
  }

  /**
   * Generate checksum for migration
   */
  private generateChecksum(migration: Migration): string {
    const content = `${migration.id}:${migration.name}:${migration.version}`;
    return Buffer.from(content).toString('base64');
  }
}
