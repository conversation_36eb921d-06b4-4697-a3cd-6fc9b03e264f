"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const WelcomeTemplate_1 = require("../models/WelcomeTemplate");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('editautomessage')
        .setDescription('Edit an existing automated message (admin only)')
        .addStringOption(option => option.setName('name')
        .setDescription('Name of the message template to edit')
        .setRequired(false))
        .addStringOption(option => option.setName('trigger')
        .setDescription('New event that triggers the message')
        .setRequired(false)
        .addChoices({ name: 'Member Join', value: 'member_join' }, { name: 'Role Added', value: 'role_add' }, { name: 'Role Removed', value: 'role_remove' }))
        .addStringOption(option => option.setName('delivery')
        .setDescription('New delivery method')
        .setRequired(false)
        .addChoices({ name: 'Direct Message', value: 'dm' }, { name: 'Channel', value: 'channel' }, { name: 'Both DM and Channel', value: 'both' }))
        .addRoleOption(option => option.setName('role')
        .setDescription('New specific role for role_add/role_remove triggers')
        .setRequired(false))
        .addChannelOption(option => option.setName('channel')
        .setDescription('New channel to send messages to')
        .setRequired(false)
        .addChannelTypes(discord_js_1.ChannelType.GuildText))
        .addStringOption(option => option.setName('title')
        .setDescription('New title for the embed message (max 256 characters)')
        .setRequired(false))
        .addStringOption(option => option.setName('description')
        .setDescription('New main content of the message (max 4000 characters)')
        .setRequired(false))
        .addStringOption(option => option.setName('image')
        .setDescription('New URL to an image to display in the embed')
        .setRequired(false))
        .addStringOption(option => option.setName('color')
        .setDescription('New hex color code for the embed (e.g., #dd7d00)')
        .setRequired(false))
        .addStringOption(option => option.setName('buttons')
        .setDescription('New buttons in format: Name1|URL1 Name2|URL2 (max 5 buttons)')
        .setRequired(false))
        .addBooleanOption(option => option.setName('embed')
        .setDescription('Send as embed (true) or plain text (false)')
        .setRequired(false))
        .addBooleanOption(option => option.setName('enabled')
        .setDescription('Enable or disable the message')
        .setRequired(false))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError('You need Administrator permissions to use this command.');
        }
        const templateName = interaction.options.getString('name');
        try {
            if (!templateName) {
                await showTemplateSelection(interaction);
            }
            else {
                await handleEdit(interaction, templateName);
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError || error instanceof errorHandler_1.PermissionError || error instanceof errorHandler_1.DatabaseError) {
                throw error;
            }
            throw new errorHandler_1.DatabaseError('An unexpected error occurred while editing the automessage.');
        }
    })
};
async function showTemplateSelection(interaction) {
    const guildId = interaction.guild.id;
    const templates = await WelcomeTemplate_1.WelcomeTemplate.find({
        guildId: guildId
    }).sort({ name: 1 });
    if (templates.length === 0) {
        const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, 'No Automated Messages');
        embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.INFO} **No Messages Found**\n\nYou haven't created any automated messages yet.`)
            .addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} Getting Started`,
            value: `Use \`/automessage action:create\` to create your first automated message.\n\n` +
                `**Example:**\n` +
                `\`/automessage action:create trigger:member_join delivery:channel name:welcome title:Welcome! description:Hello {user}!\``,
            inline: false
        });
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    const options = templates.slice(0, 25).map(template => ({
        label: template.name,
        value: template.name,
        description: `${template.triggerType} • ${template.deliveryType} • ${template.enabled ? 'Enabled' : 'Disabled'}`,
        emoji: getTriggerEmoji(template.triggerType === 'join' ? 'member_join' : template.triggerType)
    }));
    const selectMenu = new discord_js_1.StringSelectMenuBuilder()
        .setCustomId('edit_automessage_select')
        .setPlaceholder('Choose a message template to edit...')
        .addOptions(options);
    const row = new discord_js_1.ActionRowBuilder()
        .addComponents(selectMenu);
    const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, 'Edit Automated Message');
    embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} **Select Message to Edit**\n\nChoose which automated message you want to modify from the dropdown below.`)
        .addFields({
        name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Available Templates`,
        value: `Found ${templates.length} automated message(s) in this server.`,
        inline: false
    });
    await interaction.reply({
        embeds: [embed],
        components: [row],
        ephemeral: true
    });
    try {
        const response = await interaction.followUp({
            content: 'Waiting for your selection...',
            ephemeral: true
        });
        const collector = response.createMessageComponentCollector({
            componentType: discord_js_1.ComponentType.StringSelect,
            time: 60000
        });
        collector.on('collect', async (selectInteraction) => {
            if (selectInteraction.user.id !== interaction.user.id) {
                await selectInteraction.reply({
                    content: 'Only the command user can make this selection.',
                    ephemeral: true
                });
                return;
            }
            const selectedName = selectInteraction.values[0];
            await selectInteraction.deferUpdate();
            await showEditForm(selectInteraction, selectedName);
        });
        collector.on('end', async (collected) => {
            if (collected.size === 0) {
                await interaction.editReply({
                    content: 'Selection timed out. Please run the command again.',
                    components: []
                });
            }
        });
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError('Failed to create template selection interface.');
    }
}
async function showEditForm(interaction, templateName) {
    const template = await WelcomeTemplate_1.WelcomeTemplate.findOne({
        guildId: interaction.guild.id,
        name: templateName
    });
    if (!template) {
        throw new errorHandler_1.ValidationError(`No message template found with the name "${templateName}".`);
    }
    const guildId = interaction.guild.id;
    const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, 'Edit Automated Message');
    embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} **Editing: ${template.name}**\n\nTo edit this message, use the \`/editautomessage\` command with the specific parameters you want to change.`)
        .addFields({
        name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Current Configuration`,
        value: `**Name:** ${template.name}\n` +
            `**Trigger:** ${getTriggerDisplayName(template.triggerType === 'join' ? 'member_join' : template.triggerType)}\n` +
            `**Delivery:** ${template.deliveryType}\n` +
            `**Format:** ${template.useEmbed ? 'Embed' : 'Plain Text'}\n` +
            `**Status:** ${template.enabled ? 'Enabled' : 'Disabled'}`,
        inline: false
    }, {
        name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} How to Edit`,
        value: `Use: \`/editautomessage name:${template.name} [parameter]:[new_value]\`\n\n` +
            `**Available Parameters:**\n` +
            `• \`trigger\` - Change trigger type\n` +
            `• \`delivery\` - Change delivery method\n` +
            `• \`title\` - Change embed title\n` +
            `• \`description\` - Change message content\n` +
            `• \`embed\` - Toggle embed/plain text\n` +
            `• \`enabled\` - Enable/disable message\n` +
            `• And more...`,
        inline: false
    });
    await interaction.editReply({
        embeds: [embed],
        components: []
    });
}
async function handleEdit(interaction, templateName) {
    const template = await WelcomeTemplate_1.WelcomeTemplate.findOne({
        guildId: interaction.guild.id,
        name: templateName
    });
    if (!template) {
        throw new errorHandler_1.ValidationError(`No message template found with the name "${templateName}".`);
    }
    const newTrigger = interaction.options.getString('trigger');
    const newDelivery = interaction.options.getString('delivery');
    const newRole = interaction.options.getRole('role');
    const newChannel = interaction.options.getChannel('channel');
    const newTitle = interaction.options.getString('title');
    const newDescription = interaction.options.getString('description');
    const newImage = interaction.options.getString('image');
    const newColor = interaction.options.getString('color');
    const newButtons = interaction.options.getString('buttons');
    const newUseEmbed = interaction.options.getBoolean('embed');
    const newEnabled = interaction.options.getBoolean('enabled');
    const update = {};
    let hasChanges = false;
    if (newTrigger) {
        const triggerTypeMap = {
            'member_join': 'join',
            'role_add': 'role_add',
            'role_remove': 'role_remove'
        };
        update.triggerType = triggerTypeMap[newTrigger];
        hasChanges = true;
        if ((newTrigger === 'role_add' || newTrigger === 'role_remove') && !newRole && !template.triggerRoleId) {
            throw new errorHandler_1.ValidationError('A specific role must be selected for role_add and role_remove triggers.');
        }
    }
    if (newRole) {
        update.triggerRoleId = newRole.id;
        hasChanges = true;
    }
    if (newDelivery) {
        const deliveryTypeMap = {
            'dm': 'dm',
            'channel': 'channel',
            'both': 'both'
        };
        update.deliveryType = deliveryTypeMap[newDelivery];
        hasChanges = true;
        if ((newDelivery === 'channel' || newDelivery === 'both') && !newChannel && !template.channelId) {
            throw new errorHandler_1.ValidationError('A channel must be selected for channel or both delivery methods.');
        }
    }
    if (newChannel) {
        if (newChannel.type !== discord_js_1.ChannelType.GuildText) {
            throw new errorHandler_1.ValidationError('The specified channel must be a text channel.');
        }
        update.channelId = newChannel.id;
        hasChanges = true;
    }
    if (newUseEmbed !== null) {
        update.useEmbed = newUseEmbed;
        hasChanges = true;
    }
    if (newTitle !== null) {
        if (newTitle && newTitle.length > 256) {
            throw new errorHandler_1.ValidationError('Title cannot exceed 256 characters.');
        }
        update.title = newTitle || undefined;
        hasChanges = true;
    }
    if (newDescription !== null) {
        if (newDescription && newDescription.length > 4000) {
            throw new errorHandler_1.ValidationError('Description cannot exceed 4000 characters.');
        }
        update.description = newDescription || undefined;
        hasChanges = true;
    }
    const finalUseEmbed = newUseEmbed !== null ? newUseEmbed : template.useEmbed;
    const finalTitle = newTitle !== null ? newTitle : template.title;
    const finalDescription = newDescription !== null ? newDescription : template.description;
    if (finalUseEmbed) {
        if (!finalTitle && !finalDescription) {
            throw new errorHandler_1.ValidationError('Either title or description must be provided for embed messages.');
        }
    }
    else {
        if (!finalDescription) {
            throw new errorHandler_1.ValidationError('Description is required for plain text messages.');
        }
    }
    if (newColor !== null) {
        if (newColor && !/^#[0-9A-Fa-f]{6}$/.test(newColor)) {
            throw new errorHandler_1.ValidationError('Color must be a valid hex code (e.g., #dd7d00).');
        }
        update.color = newColor || undefined;
        hasChanges = true;
    }
    if (newImage !== null) {
        if (newImage && !isValidUrl(newImage)) {
            throw new errorHandler_1.ValidationError('Image must be a valid URL.');
        }
        update.imageUrl = newImage || undefined;
        hasChanges = true;
    }
    if (newButtons !== null) {
        const parsedButtons = parseButtons(newButtons);
        update.buttons = parsedButtons;
        hasChanges = true;
    }
    if (newEnabled !== null) {
        update.enabled = newEnabled;
        hasChanges = true;
    }
    if (!hasChanges) {
        throw new errorHandler_1.ValidationError('No changes specified. Please provide at least one field to update.');
    }
    const updatedTemplate = await WelcomeTemplate_1.WelcomeTemplate.findByIdAndUpdate(template._id, update, { new: true });
    if (!updatedTemplate) {
        throw new errorHandler_1.DatabaseError('Failed to update the message template.');
    }
    const guildId = interaction.guild.id;
    const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Automated Message Updated');
    embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Message Updated!**\n\nYour automated message "${templateName}" has been updated successfully.`)
        .addFields({
        name: `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} Updated Configuration`,
        value: `**Name:** ${updatedTemplate.name}\n` +
            `**Trigger:** ${getTriggerDisplayName(updatedTemplate.triggerType === 'join' ? 'member_join' : updatedTemplate.triggerType)}\n` +
            `**Delivery:** ${updatedTemplate.deliveryType}\n` +
            `**Format:** ${updatedTemplate.useEmbed ? 'Embed' : 'Plain Text'}\n` +
            `**Status:** ${updatedTemplate.enabled ? 'Enabled' : 'Disabled'}`,
        inline: false
    }, {
        name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} Next Steps`,
        value: `• Use \`/automessage action:test name:${templateName}\` to preview the updated message\n` +
            `• Use \`/automessage action:list\` to see all your messages`,
        inline: false
    });
    await interaction.reply({ embeds: [embed], ephemeral: false });
}
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    }
    catch (_) {
        return false;
    }
}
function parseButtons(buttonString) {
    if (!buttonString)
        return [];
    const buttons = [];
    const buttonPairs = buttonString.split(' ');
    for (const pair of buttonPairs) {
        if (buttons.length >= 5) {
            throw new errorHandler_1.ValidationError('Maximum of 5 buttons allowed.');
        }
        const [name, url] = pair.split('|');
        if (!name || !url) {
            throw new errorHandler_1.ValidationError('Button format must be: Name|URL (e.g., Discord|https://discord.com)');
        }
        if (name.length > 80) {
            throw new errorHandler_1.ValidationError('Button names cannot exceed 80 characters.');
        }
        if (!isValidUrl(url)) {
            throw new errorHandler_1.ValidationError(`Invalid URL for button "${name}": ${url}`);
        }
        buttons.push({
            label: name,
            url: url,
            style: 'Link'
        });
    }
    return buttons;
}
function getTriggerDisplayName(trigger) {
    switch (trigger) {
        case 'member_join':
            return 'Member Join';
        case 'role_add':
            return 'Role Added';
        case 'role_remove':
            return 'Role Removed';
        default:
            return trigger;
    }
}
function getTriggerEmoji(trigger) {
    switch (trigger) {
        case 'member_join':
            return '👋';
        case 'role_add':
            return '🎭';
        case 'role_remove':
            return '🗑️';
        default:
            return '📝';
    }
}
