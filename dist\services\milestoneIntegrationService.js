"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MilestoneIntegrationService = void 0;
const milestoneService_1 = require("./milestoneService");
const roleAssignmentService_1 = require("./roleAssignmentService");
const milestoneAuditService_1 = require("./milestoneAuditService");
class MilestoneIntegrationService {
    static async processUserActivity(client, userId, guildId, activityType, activityData) {
        const results = {
            milestoneResults: [],
            existingRewards: [],
            errors: []
        };
        try {
            const milestoneResults = await (0, milestoneService_1.checkAndProcessMilestones)(client, userId, guildId, activityType, activityData);
            results.milestoneResults = milestoneResults;
            if (milestoneResults.length > 0) {
                console.log(`[Integration] User ${userId} achieved ${milestoneResults.length} milestones`);
            }
        }
        catch (error) {
            const errorMsg = `Failed to process milestones: ${error}`;
            results.errors.push(errorMsg);
            console.error('[Integration] Milestone processing error:', error);
        }
        return results;
    }
    static async validateRoleCompatibility(client, userId, guildId, newBalance) {
        const issues = [];
        try {
            const roleResult = await (0, roleAssignmentService_1.checkAndAssignRoles)(client, userId, guildId, newBalance);
            if (roleResult && roleResult.rolesAssigned && roleResult.rolesAssigned.length > 0) {
                console.log(`[Integration] Role assignment compatible: ${roleResult.rolesAssigned.length} roles would be assigned`);
            }
            return { compatible: true, issues };
        }
        catch (error) {
            issues.push(`Role assignment compatibility check failed: ${error}`);
            console.error('[Integration] Role compatibility error:', error);
            return { compatible: false, issues };
        }
    }
    static async validateTransactionIntegrity(userId, guildId, expectedMilestoneCount) {
        const issues = [];
        try {
            const auditSummary = await milestoneAuditService_1.MilestoneAuditService.getUserActivitySummary(guildId, userId, 1);
            if (auditSummary.totalAchievements !== expectedMilestoneCount) {
                issues.push(`Milestone count mismatch: expected ${expectedMilestoneCount}, found ${auditSummary.totalAchievements}`);
            }
            return { valid: issues.length === 0, issues };
        }
        catch (error) {
            issues.push(`Transaction integrity check failed: ${error}`);
            console.error('[Integration] Transaction integrity error:', error);
            return { valid: false, issues };
        }
    }
    static async performHealthCheck(guildId) {
        const errors = [];
        const recommendations = [];
        let milestoneSystemStatus = 'unknown';
        let integrationStatus = 'unknown';
        try {
            const { createDefaultMilestoneConfigurations } = await Promise.resolve().then(() => __importStar(require('./milestoneService')));
            milestoneSystemStatus = 'operational';
            const securityAlerts = await milestoneAuditService_1.MilestoneAuditService.getSecurityAlerts(guildId, 24);
            if (securityAlerts.length > 10) {
                recommendations.push('High number of security alerts - review user activity');
            }
            integrationStatus = 'operational';
            if (securityAlerts.length === 0) {
                recommendations.push('No security alerts - system running smoothly');
            }
        }
        catch (error) {
            errors.push(`Health check failed: ${error}`);
            console.error('[Integration] Health check error:', error);
        }
        return {
            healthy: errors.length === 0,
            milestoneSystemStatus,
            integrationStatus,
            recommendations,
            errors
        };
    }
    static async migrateExistingUsers(guildId) {
        const results = {
            migrated: 0,
            errors: [],
            skipped: 0
        };
        try {
            console.log(`[Integration] Migration would process existing users for guild ${guildId}`);
            results.migrated = 0;
        }
        catch (error) {
            results.errors.push(`Migration failed: ${error}`);
            console.error('[Integration] Migration error:', error);
        }
        return results;
    }
    static async validateRateLimitCompatibility(userId, guildId) {
        const conflicts = [];
        try {
            return { compatible: true, conflicts };
        }
        catch (error) {
            conflicts.push(`Rate limit compatibility check failed: ${error}`);
            console.error('[Integration] Rate limit compatibility error:', error);
            return { compatible: false, conflicts };
        }
    }
    static async getIntegrationReport(guildId) {
        try {
            const [configs, securityAlerts, auditLogs] = await Promise.all([
                Promise.resolve().then(() => __importStar(require('../models/MilestoneConfiguration'))).then(m => m.default.countDocuments({ guildId })),
                milestoneAuditService_1.MilestoneAuditService.getSecurityAlerts(guildId, 24),
                milestoneAuditService_1.MilestoneAuditService.getAuditLogs(guildId, {
                    category: 'achievement',
                    startDate: new Date(Date.now() - (24 * 60 * 60 * 1000)),
                    limit: 100
                })
            ]);
            const activeUsers = new Set(auditLogs.map(log => log.userId)).size;
            const recentAchievements = auditLogs.filter(log => log.action === 'milestone_achieved').length;
            let status = 'healthy';
            if (securityAlerts.length > 5) {
                status = 'warning';
            }
            if (securityAlerts.length > 20) {
                status = 'error';
            }
            return {
                status,
                milestoneConfigurations: configs,
                activeUsers,
                recentAchievements,
                securityAlerts: securityAlerts.length,
                systemCompatibility: true,
                lastUpdated: new Date()
            };
        }
        catch (error) {
            console.error('[Integration] Error generating integration report:', error);
            return {
                status: 'error',
                milestoneConfigurations: 0,
                activeUsers: 0,
                recentAchievements: 0,
                securityAlerts: 0,
                systemCompatibility: false,
                lastUpdated: new Date()
            };
        }
    }
}
exports.MilestoneIntegrationService = MilestoneIntegrationService;
