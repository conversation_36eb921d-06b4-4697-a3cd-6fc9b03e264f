"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WealthElectionCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const WealthElectionService_1 = require("../../services/election/WealthElectionService");
const errorHandler_1 = require("../../utils/errorHandler");
class WealthElectionCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'election',
            description: 'Create a wealth-weighted election for council seats',
            category: BaseCommand_1.CommandCategory.ADMIN,
            guildOnly: true,
            requiredFeatures: ['ECONOMY_SYSTEM'],
            requiredPermissions: ['ManageGuild']
        });
    }
    customizeCommand(command) {
        command
            .addStringOption(option => option.setName('title')
            .setDescription('Election title (e.g., "Council Election March 2025")')
            .setRequired(true)
            .setMaxLength(200))
            .addStringOption(option => option.setName('description')
            .setDescription('Election description and details')
            .setRequired(true)
            .setMaxLength(2000))
            .addIntegerOption(option => option.setName('seats')
            .setDescription('Number of council seats to fill')
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(25))
            .addIntegerOption(option => option.setName('nomination_hours')
            .setDescription('Hours for nomination period (default: 24)')
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(168))
            .addIntegerOption(option => option.setName('voting_hours')
            .setDescription('Hours for voting period (default: 48)')
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(168))
            .addStringOption(option => option.setName('voter_roles')
            .setDescription('Roles that can vote (mention roles or role IDs separated by spaces)')
            .setRequired(true))
            .addStringOption(option => option.setName('candidate_roles')
            .setDescription('Roles that can become candidates (default: same as voter roles)')
            .setRequired(false))
            .addIntegerOption(option => option.setName('candidate_cap')
            .setDescription('Maximum number of candidates (optional limit)')
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(100));
    }
    setWealthElectionService(service) {
        this.wealthElectionService = service;
        this.logger?.info('[WealthElectionCommand] WealthElectionService injected successfully');
    }
    async executeCommand(context) {
        const { interaction, guild, member } = context;
        try {
            if (!this.wealthElectionService) {
                this.wealthElectionService = new WealthElectionService_1.WealthElectionService(null);
                this.logger.warn('WealthElectionService created without proper DI - this should be fixed in production');
            }
            if (!guild || !member) {
                throw new errorHandler_1.ValidationError('This command can only be used in a server');
            }
            const title = interaction.options.getString('title', true);
            const description = interaction.options.getString('description', true);
            const seats = interaction.options.getInteger('seats') || 5;
            const nominationHours = interaction.options.getInteger('nomination_hours') || 24;
            const votingHours = interaction.options.getInteger('voting_hours') || 48;
            const voterRolesString = interaction.options.getString('voter_roles', true);
            const candidateRolesString = interaction.options.getString('candidate_roles');
            const candidateCap = interaction.options.getInteger('candidate_cap') || undefined;
            this.logger?.info('[WealthElectionCommand] Creating election', {
                guildId: guild.id,
                createdBy: interaction.user.id,
                title,
                seats
            });
            const eligibleVoterRoles = this.parseRoles(voterRolesString, guild);
            const eligibleCandidateRoles = candidateRolesString
                ? this.parseRoles(candidateRolesString, guild)
                : eligibleVoterRoles;
            await this.validateRolesExist([...eligibleVoterRoles, ...eligibleCandidateRoles], guild);
            const electionConfig = {
                title: title.trim(),
                description: description.trim(),
                seats,
                candidateCap,
                nominationHours,
                votingHours,
                eligibleVoterRoles,
                eligibleCandidateRoles,
                guildId: guild.id,
                channelId: interaction.channelId,
                createdBy: interaction.user.id
            };
            await interaction.deferReply();
            const election = await this.wealthElectionService.createElection(electionConfig);
            const { embed, components } = this.createElectionEmbed(election, [], []);
            const reply = await interaction.editReply({ embeds: [embed], components });
            await this.updateElectionMessageId(election.electionId, reply.id);
            this.logger?.info('[WealthElectionCommand] Election created successfully', {
                electionId: election.electionId,
                guildId: guild.id,
                messageId: reply.id
            });
        }
        catch (error) {
            this.logger?.error('[WealthElectionCommand] Failed to create election', {
                error: error instanceof Error ? error.message : 'Unknown error',
                guildId: guild?.id,
                userId: interaction.user.id
            });
            const errorMessage = error instanceof errorHandler_1.ValidationError
                ? error.message
                : 'An error occurred while creating the election. Please try again.';
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `❌ **Error:** ${errorMessage}`,
                    embeds: [],
                    components: []
                });
            }
            else {
                await interaction.reply({
                    content: `❌ **Error:** ${errorMessage}`,
                    ephemeral: true
                });
            }
        }
    }
    parseRoles(rolesString, guild) {
        const roleIds = [];
        const parts = rolesString.trim().split(/\s+/);
        this.logger?.debug('[WealthElectionCommand] Parsing roles', {
            rolesString,
            guildId: guild.id,
            parts
        });
        for (const part of parts) {
            const roleId = part.replace(/[<@&>]/g, '').trim();
            if (/^\d{17,20}$/.test(roleId)) {
                if (!roleIds.includes(roleId)) {
                    roleIds.push(roleId);
                }
            }
            else if (roleId.length > 0) {
                this.logger?.warn('[WealthElectionCommand] Invalid role ID format', {
                    originalPart: part,
                    extractedRoleId: roleId,
                    guildId: guild.id
                });
            }
        }
        if (roleIds.length === 0) {
            throw new errorHandler_1.ValidationError('No valid role IDs or mentions found. Please use role mentions (@role) or valid role IDs.');
        }
        this.logger?.debug('[WealthElectionCommand] Parsed role IDs', {
            roleIds,
            count: roleIds.length,
            guildId: guild.id
        });
        return roleIds;
    }
    async validateRolesExist(roleIds, guild) {
        const missingRoles = [];
        for (const roleId of roleIds) {
            const role = guild.roles.cache.get(roleId);
            if (!role) {
                missingRoles.push(roleId);
            }
        }
        if (missingRoles.length > 0) {
            this.logger?.warn('[WealthElectionCommand] Some roles not found in guild', {
                missingRoles,
                guildId: guild.id
            });
            throw new errorHandler_1.ValidationError(`The following roles were not found in this server: ${missingRoles.join(', ')}. ` +
                'Please ensure all roles exist and try again.');
        }
        this.logger?.debug('[WealthElectionCommand] All roles validated successfully', {
            roleIds,
            guildId: guild.id
        });
    }
    createElectionEmbed(election, candidates, votes) {
        const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
        const now = new Date();
        const timeRemaining = this.getTimeRemaining(election, now);
        const embed = new EmbedBuilder()
            .setTitle(`🗳️ ${election.title}`)
            .setDescription(election.description)
            .setColor(this.getElectionColor(election.status))
            .addFields([
            {
                name: '📊 Election Info',
                value: `**Seats:** ${election.seats}\n**Status:** ${election.status}\n**${timeRemaining}**`,
                inline: true
            },
            {
                name: '👥 Candidates',
                value: `${candidates.length} registered${election.candidateCap ? ` / ${election.candidateCap} max` : ''}`,
                inline: true
            },
            {
                name: '🗳️ Votes Cast',
                value: `${votes.length} ballots`,
                inline: true
            }
        ])
            .setFooter({
            text: `Election ID: ${election.electionId}`,
            iconURL: 'https://cdn.discordapp.com/emojis/💰.png'
        })
            .setTimestamp();
        if (candidates.length > 0) {
            const candidateList = candidates
                .slice(0, 10)
                .map((candidate, index) => `${index + 1}. **${candidate.displayName}** (@${candidate.username})`)
                .join('\n');
            embed.addFields([{
                    name: '🏛️ Candidates',
                    value: candidateList + (candidates.length > 10 ? `\n... and ${candidates.length - 10} more` : ''),
                    inline: false
                }]);
        }
        const components = [];
        if (election.status === 'NOMINATING') {
            const nominationRow = new ActionRowBuilder()
                .addComponents(new ButtonBuilder()
                .setCustomId(`election_become_candidate_${election.electionId}`)
                .setLabel('Become Candidate')
                .setStyle(ButtonStyle.Primary)
                .setEmoji('🏛️'), new ButtonBuilder()
                .setCustomId(`election_list_candidates_${election.electionId}`)
                .setLabel('List Candidates')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('📋'));
            components.push(nominationRow);
            const adminRow = new ActionRowBuilder()
                .addComponents(new ButtonBuilder()
                .setCustomId(`election_close_noms_${election.electionId}`)
                .setLabel('Close Nominations')
                .setStyle(ButtonStyle.Danger)
                .setEmoji('⏰'), new ButtonBuilder()
                .setCustomId(`election_cancel_${election.electionId}`)
                .setLabel('Cancel Election')
                .setStyle(ButtonStyle.Danger)
                .setEmoji('❌'));
            components.push(adminRow);
        }
        else if (election.status === 'VOTING') {
            const candidateChunks = this.chunkArray(candidates.slice(0, 20), 5);
            candidateChunks.forEach(chunk => {
                const row = new ActionRowBuilder();
                chunk.forEach(candidate => {
                    row.addComponents(new ButtonBuilder()
                        .setCustomId(`election_vote_${election.electionId}_${candidate.candidateId}`)
                        .setLabel(`Vote: ${candidate.displayName}`)
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('✅'));
                });
                components.push(row);
            });
            const adminRow = new ActionRowBuilder()
                .addComponents(new ButtonBuilder()
                .setCustomId(`election_end_${election.electionId}`)
                .setLabel('End Election')
                .setStyle(ButtonStyle.Danger)
                .setEmoji('🏁'));
            components.push(adminRow);
        }
        return { embed, components };
    }
    getTimeRemaining(election, now) {
        if (election.status === 'NOMINATING') {
            const timeLeft = election.nominationEnd.getTime() - now.getTime();
            if (timeLeft <= 0)
                return 'Nominations: Ended';
            const hours = Math.floor(timeLeft / (1000 * 60 * 60));
            return `Nominations: ${hours}h remaining`;
        }
        else if (election.status === 'VOTING') {
            const timeLeft = election.votingEnd.getTime() - now.getTime();
            if (timeLeft <= 0)
                return 'Voting: Ended';
            const hours = Math.floor(timeLeft / (1000 * 60 * 60));
            return `Voting: ${hours}h remaining`;
        }
        else {
            return `Status: ${election.status}`;
        }
    }
    getElectionColor(status) {
        switch (status) {
            case 'NOMINATING': return 0x3498db;
            case 'VOTING': return 0x2ecc71;
            case 'ENDED': return 0x9b59b6;
            case 'CANCELLED': return 0xe74c3c;
            default: return 0x95a5a6;
        }
    }
    chunkArray(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }
    async updateElectionMessageId(electionId, messageId) {
        const { Election } = require('../../models');
        await Election.updateOne({ electionId }, { messageId });
    }
}
exports.WealthElectionCommand = WealthElectionCommand;
