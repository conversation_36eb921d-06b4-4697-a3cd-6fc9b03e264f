"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SalaryListCommand = void 0;
const discord_js_1 = require("discord.js");
const BaseCommand_1 = require("../base/BaseCommand");
const RoleSalary_1 = require("../../models/RoleSalary");
const embedBuilder_1 = require("../../utils/embedBuilder");
class SalaryListCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'salarylist',
            description: 'View all configured role salaries (admin only)',
            category: BaseCommand_1.CommandCategory.ADMIN,
            adminOnly: true,
            requiredFeatures: ['SALARY_SYSTEM'],
            requiredPermissions: ['Administrator'],
        });
    }
    async executeCommand(context) {
        const { interaction } = context;
        try {
            const salaryConfigs = await RoleSalary_1.RoleSalary.find({
                guildId: interaction.guild.id
            }).sort({ amount: -1 });
            if (salaryConfigs.length === 0) {
                const embed = (0, embedBuilder_1.createErrorEmbed)('No Salaries Configured', `${embedBuilder_1.EMOJIS.ADMIN.INFO} No role salaries have been configured for **${interaction.guild.name}**.\n\n` +
                    `Use \`/salaryadd\` to configure salary payments for roles.`);
                (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            const itemsPerPage = 10;
            const totalPages = Math.ceil(salaryConfigs.length / itemsPerPage);
            const currentPage = 1;
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageConfigs = salaryConfigs.slice(startIndex, endIndex);
            const embed = await this.buildSalaryListEmbed(interaction, pageConfigs, currentPage, totalPages, salaryConfigs.length);
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            const actionRow = (0, embedBuilder_1.createQuickActionButtons)();
            await interaction.reply({
                embeds: [embed],
                components: [actionRow],
                ephemeral: false
            });
            this.logger.info(`Admin ${interaction.user.username} viewed salary list`, {
                adminId: interaction.user.id,
                guildId: interaction.guild?.id,
                guildName: interaction.guild?.name,
                totalSalaries: salaryConfigs.length
            });
        }
        catch (error) {
            this.logger.error('Error executing salary list command', {
                error,
                adminId: interaction.user.id,
                guildId: interaction.guild?.id
            });
            throw error;
        }
    }
    async buildSalaryListEmbed(interaction, salaryConfigs, currentPage, totalPages, totalCount) {
        const guild = interaction.guild;
        const embed = new discord_js_1.EmbedBuilder()
            .setColor(embedBuilder_1.COLORS.SUCCESS)
            .setTitle(`${embedBuilder_1.EMOJIS.ADMIN.LIST} Role Salary Configurations`)
            .setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.BANK} **Server:** ${guild.name}\n` +
            `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} **Total Configured:** ${totalCount} role${totalCount !== 1 ? 's' : ''}\n` +
            (totalPages > 1 ? `${embedBuilder_1.EMOJIS.MISC.BOOK} **Page:** ${currentPage} of ${totalPages}\n` : '') +
            `\n${embedBuilder_1.EMOJIS.MISC.SCROLL} **Salary Configurations** (sorted by amount):`);
        for (let i = 0; i < salaryConfigs.length; i++) {
            const config = salaryConfigs[i];
            const roleNumber = (currentPage - 1) * 10 + i + 1;
            try {
                const role = await guild.roles.fetch(config.roleId);
                if (role) {
                    const frequencyEmoji = config.frequency === RoleSalary_1.SalaryFrequency.DAILY ?
                        embedBuilder_1.EMOJIS.MISC.CALENDAR : embedBuilder_1.EMOJIS.MISC.CLOCK;
                    const frequencyText = config.frequency.charAt(0).toUpperCase() +
                        config.frequency.slice(1);
                    embed.addFields({
                        name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} ${roleNumber}. ${role.name}`,
                        value: `**Amount:** ${(0, embedBuilder_1.formatCoins)(config.amount)}\n` +
                            `**Frequency:** ${frequencyEmoji} ${frequencyText}\n` +
                            `**Role:** ${role.toString()}\n` +
                            `**ID:** \`${config.roleId}\``,
                        inline: true
                    });
                }
                else {
                    embed.addFields({
                        name: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} ${roleNumber}. [Deleted Role]`,
                        value: `**Amount:** ${(0, embedBuilder_1.formatCoins)(config.amount)}\n` +
                            `**Frequency:** ${config.frequency.charAt(0).toUpperCase() + config.frequency.slice(1)}\n` +
                            `**Status:** ${embedBuilder_1.EMOJIS.ADMIN.WARNING} Role no longer exists\n` +
                            `**ID:** \`${config.roleId}\``,
                        inline: true
                    });
                }
            }
            catch (error) {
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} ${roleNumber}. [Error Loading Role]`,
                    value: `**Amount:** ${(0, embedBuilder_1.formatCoins)(config.amount)}\n` +
                        `**Frequency:** ${config.frequency.charAt(0).toUpperCase() + config.frequency.slice(1)}\n` +
                        `**Status:** ${embedBuilder_1.EMOJIS.ADMIN.WARNING} Error loading role\n` +
                        `**ID:** \`${config.roleId}\``,
                    inline: true
                });
            }
        }
        const totalDailyAmount = salaryConfigs
            .filter(c => c.frequency === RoleSalary_1.SalaryFrequency.DAILY)
            .reduce((sum, c) => sum + c.amount, 0);
        const totalWeeklyAmount = salaryConfigs
            .filter(c => c.frequency === RoleSalary_1.SalaryFrequency.WEEKLY)
            .reduce((sum, c) => sum + c.amount, 0);
        const dailyCount = salaryConfigs.filter(c => c.frequency === RoleSalary_1.SalaryFrequency.DAILY).length;
        const weeklyCount = salaryConfigs.filter(c => c.frequency === RoleSalary_1.SalaryFrequency.WEEKLY).length;
        if (totalCount > 0) {
            let summaryText = `${embedBuilder_1.EMOJIS.ADMIN.INFO} **Summary:**\n`;
            if (dailyCount > 0) {
                summaryText += `• **Daily:** ${dailyCount} role${dailyCount !== 1 ? 's' : ''} - ${(0, embedBuilder_1.formatCoins)(totalDailyAmount)} total\n`;
            }
            if (weeklyCount > 0) {
                summaryText += `• **Weekly:** ${weeklyCount} role${weeklyCount !== 1 ? 's' : ''} - ${(0, embedBuilder_1.formatCoins)(totalWeeklyAmount)} total\n`;
            }
            const estimatedDailyCost = totalDailyAmount + (totalWeeklyAmount / 7);
            summaryText += `• **Est. Daily Cost:** ${(0, embedBuilder_1.formatCoins)(Math.round(estimatedDailyCost))}`;
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.ECONOMY.CHART} Cost Analysis`,
                value: summaryText,
                inline: false
            });
        }
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.ADMIN.TOOLS} Management Commands`,
            value: `• \`/salaryadd\` - Add or update role salary\n` +
                `• \`/salaryremove\` - Remove role salary\n` +
                `• Check logs for automated distribution status`,
            inline: false
        });
        return embed;
    }
}
exports.SalaryListCommand = SalaryListCommand;
