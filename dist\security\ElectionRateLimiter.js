"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElectionRateLimiterService = void 0;
const events_1 = require("events");
class ElectionRateLimiterService extends events_1.EventEmitter {
    constructor(logger, monitoring) {
        super();
        this.logger = logger;
        this.monitoring = monitoring;
        this.rateLimitRules = new Map();
        this.userActivities = new Map();
        this.abusePatterns = [];
        this.suspiciousUsers = new Set();
        this.setupDefaultRateLimits();
        this.setupAbusePatterns();
        this.startCleanupTask();
    }
    setupDefaultRateLimits() {
        this.rateLimitRules.set('vote_cast', {
            windowMs: 60000,
            maxRequests: 5,
            blockDurationMs: 300000,
            skipSuccessfulRequests: false
        });
        this.rateLimitRules.set('candidate_register', {
            windowMs: 300000,
            maxRequests: 3,
            blockDurationMs: 600000,
            skipSuccessfulRequests: true
        });
        this.rateLimitRules.set('button_interaction', {
            windowMs: 30000,
            maxRequests: 20,
            blockDurationMs: 120000,
            skipSuccessfulRequests: false
        });
        this.rateLimitRules.set('election_refresh', {
            windowMs: 10000,
            maxRequests: 5,
            blockDurationMs: 60000,
            skipSuccessfulRequests: false
        });
        this.rateLimitRules.set('modal_submit', {
            windowMs: 60000,
            maxRequests: 3,
            blockDurationMs: 300000,
            skipSuccessfulRequests: true
        });
        this.rateLimitRules.set('election_create', {
            windowMs: 3600000,
            maxRequests: 5,
            blockDurationMs: 1800000,
            skipSuccessfulRequests: false
        });
    }
    setupAbusePatterns() {
        this.abusePatterns.push({
            name: 'rapid_voting',
            description: 'User attempting to vote too quickly',
            detector: (activity) => {
                const recentVotes = activity.requests.filter(r => r.action === 'vote_cast' &&
                    r.timestamp > Date.now() - 30000);
                return recentVotes.length >= 3;
            },
            severity: 'medium',
            action: 'block'
        });
        this.abusePatterns.push({
            name: 'button_spam',
            description: 'User spamming button interactions',
            detector: (activity) => {
                const recentButtons = activity.requests.filter(r => r.action === 'button_interaction' &&
                    r.timestamp > Date.now() - 60000);
                return recentButtons.length >= 30;
            },
            severity: 'high',
            action: 'block'
        });
        this.abusePatterns.push({
            name: 'registration_spam',
            description: 'User repeatedly failing candidate registration',
            detector: (activity) => {
                const failedRegistrations = activity.requests.filter(r => r.action === 'candidate_register' &&
                    !r.success &&
                    r.timestamp > Date.now() - 600000);
                return failedRegistrations.length >= 5;
            },
            severity: 'medium',
            action: 'warn'
        });
        this.abusePatterns.push({
            name: 'cross_election_abuse',
            description: 'User attempting to manipulate multiple elections',
            detector: (activity) => {
                const elections = new Set(activity.requests
                    .filter(r => r.timestamp > Date.now() - 1800000)
                    .map(r => r.electionId)
                    .filter(Boolean));
                return elections.size >= 5;
            },
            severity: 'high',
            action: 'report'
        });
        this.abusePatterns.push({
            name: 'persistent_violations',
            description: 'User with multiple recent violations',
            detector: (activity) => {
                return activity.violations >= 3 &&
                    (Date.now() - activity.lastViolation) < 3600000;
            },
            severity: 'critical',
            action: 'block'
        });
    }
    async checkRateLimit(userId, action, electionId, metadata) {
        try {
            const userActivity = this.getUserActivity(userId);
            if (userActivity.blocked && Date.now() < userActivity.blockedUntil) {
                const retryAfter = Math.ceil((userActivity.blockedUntil - Date.now()) / 1000);
                return {
                    allowed: false,
                    reason: 'User is temporarily blocked due to rate limiting',
                    retryAfter
                };
            }
            if (userActivity.blocked && Date.now() >= userActivity.blockedUntil) {
                userActivity.blocked = false;
                userActivity.blockedUntil = 0;
            }
            const rule = this.rateLimitRules.get(action);
            if (!rule) {
                this.recordUserAction(userId, action, true, electionId, metadata);
                return { allowed: true };
            }
            const windowStart = Date.now() - rule.windowMs;
            const recentRequests = userActivity.requests.filter(r => r.action === action &&
                r.timestamp >= windowStart &&
                (!rule.skipSuccessfulRequests || !r.success));
            if (recentRequests.length >= rule.maxRequests) {
                userActivity.blocked = true;
                userActivity.blockedUntil = Date.now() + rule.blockDurationMs;
                userActivity.violations++;
                userActivity.lastViolation = Date.now();
                this.logger.warn('[ElectionRateLimiter] Rate limit exceeded', {
                    userId,
                    action,
                    requests: recentRequests.length,
                    limit: rule.maxRequests,
                    blockDuration: rule.blockDurationMs
                });
                this.recordUserAction(userId, action, false, electionId, {
                    ...metadata,
                    rateLimited: true,
                    violations: userActivity.violations
                });
                await this.checkAbusePatterns(userActivity);
                if (this.monitoring) {
                    this.monitoring.recordMetric('rate_limit_violations', 1, { action, userId });
                }
                this.emit('rate_limit_exceeded', {
                    userId,
                    action,
                    electionId,
                    blockDuration: rule.blockDurationMs,
                    violations: userActivity.violations
                });
                const retryAfter = Math.ceil(rule.blockDurationMs / 1000);
                return {
                    allowed: false,
                    reason: `Rate limit exceeded for ${action}. Too many requests in ${rule.windowMs / 1000} seconds.`,
                    retryAfter
                };
            }
            return { allowed: true };
        }
        catch (error) {
            this.logger.error('[ElectionRateLimiter] Error checking rate limit', { error, userId, action });
            return { allowed: true };
        }
    }
    recordUserAction(userId, action, success, electionId, metadata) {
        const userActivity = this.getUserActivity(userId);
        userActivity.requests.push({
            timestamp: Date.now(),
            action,
            success,
            electionId,
            metadata
        });
        const cutoffTime = Date.now() - (24 * 60 * 60 * 1000);
        userActivity.requests = userActivity.requests.filter(r => r.timestamp > cutoffTime);
        setImmediate(() => {
            this.checkAbusePatterns(userActivity);
        });
    }
    getUserActivity(userId) {
        if (!this.userActivities.has(userId)) {
            this.userActivities.set(userId, {
                userId,
                requests: [],
                blocked: false,
                blockedUntil: 0,
                violations: 0,
                lastViolation: 0
            });
        }
        return this.userActivities.get(userId);
    }
    async checkAbusePatterns(userActivity) {
        for (const pattern of this.abusePatterns) {
            try {
                if (pattern.detector(userActivity)) {
                    await this.handleAbuseDetection(userActivity, pattern);
                }
            }
            catch (error) {
                this.logger.error('[ElectionRateLimiter] Error checking abuse pattern', {
                    error,
                    pattern: pattern.name,
                    userId: userActivity.userId
                });
            }
        }
    }
    async handleAbuseDetection(userActivity, pattern) {
        this.logger.warn('[ElectionRateLimiter] Abuse pattern detected', {
            userId: userActivity.userId,
            pattern: pattern.name,
            description: pattern.description,
            severity: pattern.severity,
            action: pattern.action
        });
        this.suspiciousUsers.add(userActivity.userId);
        switch (pattern.action) {
            case 'warn':
                this.emit('abuse_warning', {
                    userId: userActivity.userId,
                    pattern: pattern.name,
                    severity: pattern.severity
                });
                break;
            case 'block':
                const blockDuration = this.calculateAbuseBlockDuration(pattern.severity);
                userActivity.blocked = true;
                userActivity.blockedUntil = Date.now() + blockDuration;
                userActivity.violations++;
                userActivity.lastViolation = Date.now();
                this.emit('abuse_block', {
                    userId: userActivity.userId,
                    pattern: pattern.name,
                    severity: pattern.severity,
                    blockDuration
                });
                break;
            case 'report':
                this.emit('abuse_report', {
                    userId: userActivity.userId,
                    pattern: pattern.name,
                    severity: pattern.severity,
                    evidence: userActivity.requests.slice(-20)
                });
                break;
        }
        if (this.monitoring) {
            this.monitoring.recordMetric('abuse_detected', 1, {
                pattern: pattern.name,
                severity: pattern.severity,
                userId: userActivity.userId
            });
        }
    }
    calculateAbuseBlockDuration(severity) {
        switch (severity) {
            case 'low': return 5 * 60 * 1000;
            case 'medium': return 30 * 60 * 1000;
            case 'high': return 2 * 60 * 60 * 1000;
            case 'critical': return 24 * 60 * 60 * 1000;
            default: return 30 * 60 * 1000;
        }
    }
    blockUser(userId, durationMs, reason) {
        const userActivity = this.getUserActivity(userId);
        userActivity.blocked = true;
        userActivity.blockedUntil = Date.now() + durationMs;
        userActivity.violations++;
        userActivity.lastViolation = Date.now();
        this.logger.info('[ElectionRateLimiter] User manually blocked', {
            userId,
            durationMs,
            reason
        });
        this.emit('manual_block', { userId, durationMs, reason });
    }
    unblockUser(userId, reason) {
        const userActivity = this.getUserActivity(userId);
        userActivity.blocked = false;
        userActivity.blockedUntil = 0;
        this.logger.info('[ElectionRateLimiter] User manually unblocked', {
            userId,
            reason
        });
        this.emit('manual_unblock', { userId, reason });
    }
    getUserStatus(userId) {
        const userActivity = this.getUserActivity(userId);
        const now = Date.now();
        const lastHour = userActivity.requests.filter(r => r.timestamp > now - 3600000);
        const lastDay = userActivity.requests.filter(r => r.timestamp > now - 86400000);
        return {
            userId,
            blocked: userActivity.blocked,
            blockedUntil: userActivity.blocked ? new Date(userActivity.blockedUntil).toISOString() : null,
            violations: userActivity.violations,
            lastViolation: userActivity.lastViolation ? new Date(userActivity.lastViolation).toISOString() : null,
            suspicious: this.suspiciousUsers.has(userId),
            activity: {
                totalRequests: userActivity.requests.length,
                lastHour: lastHour.length,
                lastDay: lastDay.length,
                successRate: lastDay.length > 0 ?
                    lastDay.filter(r => r.success).length / lastDay.length : 1
            }
        };
    }
    getSystemStats() {
        const now = Date.now();
        const oneHourAgo = now - 3600000;
        let totalUsers = 0;
        let blockedUsers = 0;
        let suspiciousUsers = 0;
        let totalRequests = 0;
        let recentRequests = 0;
        for (const [userId, activity] of this.userActivities) {
            totalUsers++;
            if (activity.blocked)
                blockedUsers++;
            if (this.suspiciousUsers.has(userId))
                suspiciousUsers++;
            totalRequests += activity.requests.length;
            recentRequests += activity.requests.filter(r => r.timestamp > oneHourAgo).length;
        }
        return {
            users: {
                total: totalUsers,
                blocked: blockedUsers,
                suspicious: suspiciousUsers
            },
            requests: {
                total: totalRequests,
                lastHour: recentRequests
            },
            rateLimits: Array.from(this.rateLimitRules.keys()),
            abusePatterns: this.abusePatterns.length,
            timestamp: now
        };
    }
    getRecentIncidents(limit = 50) {
        const incidents = [];
        const cutoffTime = Date.now() - (24 * 60 * 60 * 1000);
        for (const [userId, activity] of this.userActivities) {
            if (activity.lastViolation > cutoffTime) {
                incidents.push({
                    userId,
                    lastViolation: new Date(activity.lastViolation).toISOString(),
                    violations: activity.violations,
                    blocked: activity.blocked,
                    suspicious: this.suspiciousUsers.has(userId)
                });
            }
        }
        return incidents
            .sort((a, b) => new Date(b.lastViolation).getTime() - new Date(a.lastViolation).getTime())
            .slice(0, limit);
    }
    startCleanupTask() {
        this.cleanupInterval = setInterval(() => {
            this.cleanupOldData();
        }, 60 * 60 * 1000);
    }
    cleanupOldData() {
        const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000);
        let cleanedUsers = 0;
        for (const [userId, activity] of this.userActivities) {
            if (activity.requests.length === 0 &&
                activity.violations === 0 &&
                !activity.blocked &&
                activity.lastViolation < cutoffTime) {
                this.userActivities.delete(userId);
                this.suspiciousUsers.delete(userId);
                cleanedUsers++;
            }
        }
        if (cleanedUsers > 0) {
            this.logger.info('[ElectionRateLimiter] Cleaned up old user data', { cleanedUsers });
        }
    }
    shutdown() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = undefined;
        }
        this.logger.info('[ElectionRateLimiter] Rate limiter shutdown');
    }
}
exports.ElectionRateLimiterService = ElectionRateLimiterService;
