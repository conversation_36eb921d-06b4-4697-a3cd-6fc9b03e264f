"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StarterBalance = void 0;
const mongoose_1 = require("mongoose");
const starterBalanceSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Guild ID cannot be empty'
        }
    },
    roleId: {
        type: String,
        required: [true, 'Role ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Role ID must be a valid Discord snowflake'
        }
    },
    roleName: {
        type: String,
        required: [true, 'Role name is required'],
        maxlength: [100, 'Role name cannot exceed 100 characters'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Role name cannot be empty'
        }
    },
    amount: {
        type: Number,
        required: [true, 'Amount is required'],
        min: [1, 'Starter balance amount must be at least 1 PLC'],
        max: [10000, 'Starter balance amount cannot exceed 10,000 PLC'],
        validate: {
            validator: function (v) {
                return Number.isInteger(v) && v > 0;
            },
            message: 'Starter balance amount must be a positive integer'
        }
    }
}, {
    timestamps: true
});
starterBalanceSchema.index({ guildId: 1, roleId: 1 }, { unique: true });
starterBalanceSchema.index({ guildId: 1 });
starterBalanceSchema.index({ roleId: 1 });
exports.StarterBalance = (0, mongoose_1.model)('StarterBalance', starterBalanceSchema);
