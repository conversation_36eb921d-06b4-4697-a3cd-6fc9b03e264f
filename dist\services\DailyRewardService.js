"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DailyRewardService = void 0;
const userProfile_1 = require("../utils/userProfile");
const economy_1 = require("../constants/economy");
class DailyRewardService {
    async grantDailyReward(userId) {
        const userProfile = await (0, userProfile_1.findOrCreateUser)(userId);
        if (userProfile.lastDaily) {
            const cooldown = Date.now() - userProfile.lastDaily.getTime();
            if (cooldown < economy_1.DAILY_REWARD_COOLDOWN) {
                const timeLeft = economy_1.DAILY_REWARD_COOLDOWN - cooldown;
                const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                return {
                    success: false,
                    amount: 0,
                    message: `You have already claimed your daily reward. Please wait ${hours}h ${minutes}m.`,
                };
            }
        }
        userProfile.balance += economy_1.DAILY_REWARD_AMOUNT;
        userProfile.lastDaily = new Date();
        await userProfile.save();
        return { success: true, amount: economy_1.DAILY_REWARD_AMOUNT, message: `You have claimed your daily reward of ${economy_1.DAILY_REWARD_AMOUNT} coins!` };
    }
}
exports.DailyRewardService = DailyRewardService;
