"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceStateUpdateEventHandler = exports.GuildMemberUpdateEventHandler = exports.GuildMemberRemoveEventHandler = exports.GuildMemberAddEventHandler = exports.MessageReactionAddEventHandler = exports.MessageCreateEventHandler = exports.InteractionCreateEventHandler = exports.ReadyEventHandler = exports.eventRegistry = exports.BaseEventHandler = exports.EventManager = exports.EventHandlerFactory = void 0;
const base_1 = require("./base");
const ready_1 = require("./ready");
const interactionCreate_1 = require("./interactionCreate");
const messageCreate_1 = require("./messageCreate");
const messageReactionAdd_1 = require("./messageReactionAdd");
const guildMemberAdd_1 = require("./guildMemberAdd");
const guildMemberRemove_1 = require("./guildMemberRemove");
const guildMemberUpdate_1 = require("./guildMemberUpdate");
const voiceStateUpdate_1 = require("./voiceStateUpdate");
class EventHandlerFactory {
    static createHandlers(app) {
        return [
            new ready_1.ReadyEventHandler(app),
            new interactionCreate_1.InteractionCreateEventHandler(app),
            new messageCreate_1.MessageCreateEventHandler(app),
            new messageReactionAdd_1.MessageReactionAddEventHandler(app),
            new guildMemberAdd_1.GuildMemberAddEventHandler(app),
            new guildMemberRemove_1.GuildMemberRemoveEventHandler(app),
            new guildMemberUpdate_1.GuildMemberUpdateEventHandler(app),
            new voiceStateUpdate_1.VoiceStateUpdateEventHandler(app),
        ];
    }
}
exports.EventHandlerFactory = EventHandlerFactory;
class EventManager {
    constructor(app) {
        this.handlers = [];
        this.app = app;
    }
    initialize() {
        this.handlers = EventHandlerFactory.createHandlers(this.app);
        for (const handler of this.handlers) {
            this.registerHandler(handler);
            base_1.eventRegistry.register(handler);
        }
        this.app.logger.info(`[EventManager] Registered ${this.handlers.length} event handlers`);
    }
    registerHandler(handler) {
        const wrappedHandler = async (...args) => {
            try {
                await handler.execute(...args);
            }
            catch (error) {
                this.app.logger.error(`[EventManager] Error in ${handler.name} handler`, { error });
            }
        };
        if (handler.once) {
            this.app.client.once(handler.name, wrappedHandler);
        }
        else {
            this.app.client.on(handler.name, wrappedHandler);
        }
        this.app.logger.debug(`[EventManager] Registered handler: ${handler.name} (once: ${handler.once || false})`);
    }
    shutdown() {
        this.app.client.removeAllListeners();
        base_1.eventRegistry.clear();
        this.app.logger.info('[EventManager] All event handlers unregistered');
    }
    getHandlers() {
        return [...this.handlers];
    }
    getHandler(name) {
        return this.handlers.find(handler => handler.name === name);
    }
}
exports.EventManager = EventManager;
var base_2 = require("./base");
Object.defineProperty(exports, "BaseEventHandler", { enumerable: true, get: function () { return base_2.BaseEventHandler; } });
Object.defineProperty(exports, "eventRegistry", { enumerable: true, get: function () { return base_2.eventRegistry; } });
var ready_2 = require("./ready");
Object.defineProperty(exports, "ReadyEventHandler", { enumerable: true, get: function () { return ready_2.ReadyEventHandler; } });
var interactionCreate_2 = require("./interactionCreate");
Object.defineProperty(exports, "InteractionCreateEventHandler", { enumerable: true, get: function () { return interactionCreate_2.InteractionCreateEventHandler; } });
var messageCreate_2 = require("./messageCreate");
Object.defineProperty(exports, "MessageCreateEventHandler", { enumerable: true, get: function () { return messageCreate_2.MessageCreateEventHandler; } });
var messageReactionAdd_2 = require("./messageReactionAdd");
Object.defineProperty(exports, "MessageReactionAddEventHandler", { enumerable: true, get: function () { return messageReactionAdd_2.MessageReactionAddEventHandler; } });
var guildMemberAdd_2 = require("./guildMemberAdd");
Object.defineProperty(exports, "GuildMemberAddEventHandler", { enumerable: true, get: function () { return guildMemberAdd_2.GuildMemberAddEventHandler; } });
var guildMemberRemove_2 = require("./guildMemberRemove");
Object.defineProperty(exports, "GuildMemberRemoveEventHandler", { enumerable: true, get: function () { return guildMemberRemove_2.GuildMemberRemoveEventHandler; } });
var guildMemberUpdate_2 = require("./guildMemberUpdate");
Object.defineProperty(exports, "GuildMemberUpdateEventHandler", { enumerable: true, get: function () { return guildMemberUpdate_2.GuildMemberUpdateEventHandler; } });
var voiceStateUpdate_2 = require("./voiceStateUpdate");
Object.defineProperty(exports, "VoiceStateUpdateEventHandler", { enumerable: true, get: function () { return voiceStateUpdate_2.VoiceStateUpdateEventHandler; } });
exports.default = EventManager;
