"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RolePrefix = exports.RoleForSale = void 0;
const mongoose_1 = require("mongoose");
const userSchema = new mongoose_1.Schema({
    discordId: {
        type: String,
        required: [true, 'Discord ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Discord ID must be a valid Discord snowflake'
        }
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    balance: { type: Number, default: 0, min: 0 },
    lastDaily: { type: Date, default: null }
}, {
    timestamps: true
});
userSchema.index({ discordId: 1, guildId: 1 }, { unique: true });
userSchema.index({ guildId: 1 });
userSchema.index({ guildId: 1, discordId: 1 });
userSchema.index({ guildId: 1, balance: -1 });
exports.default = (0, mongoose_1.model)('User', userSchema);
const roleForSaleSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    roleId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Role ID must be a valid Discord snowflake'
        }
    },
    price: { type: Number, required: true, min: 0 },
    name: { type: String, required: true },
    description: { type: String },
    roleType: {
        type: String,
        required: true,
        enum: ['balance_threshold', 'shop_purchase'],
        default: 'balance_threshold'
    },
    priceType: {
        type: String,
        required: true,
        enum: ['fixed', 'percentage'],
        default: 'fixed'
    },
    percentageValue: {
        type: Number,
        min: 0.1,
        max: 50,
        validate: {
            validator: function (v) {
                if (this.priceType === 'percentage') {
                    return v != null && v >= 0.1 && v <= 50;
                }
                return true;
            },
            message: 'Percentage value must be between 0.1% and 50%'
        }
    }
}, {
    timestamps: true
});
roleForSaleSchema.index({ guildId: 1, roleId: 1 }, { unique: true });
roleForSaleSchema.index({ guildId: 1 });
exports.RoleForSale = (0, mongoose_1.model)('RoleForSale', roleForSaleSchema);
const rolePrefixSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Guild ID cannot be empty'
        }
    },
    roleId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Role ID cannot be empty'
        }
    },
    prefix: {
        type: String,
        required: true,
        maxlength: [10, 'Prefix cannot exceed 10 characters'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Prefix cannot be empty'
        }
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});
rolePrefixSchema.index({ guildId: 1, roleId: 1 }, { unique: true });
exports.RolePrefix = (0, mongoose_1.model)('RolePrefix', rolePrefixSchema);
