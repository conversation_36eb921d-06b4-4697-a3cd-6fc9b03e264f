"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processJoinMessage = processJoinMessage;
exports.processRoleChangeMessage = processRoleChangeMessage;
exports.processTestMessage = processTestMessage;
const discord_js_1 = require("discord.js");
const WelcomeTemplate_1 = require("../models/WelcomeTemplate");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
async function processJoinMessage(member) {
    const result = {
        sent: false,
        templatesProcessed: 0,
        errors: []
    };
    try {
        const templates = await WelcomeTemplate_1.WelcomeTemplate.find({
            guildId: member.guild.id,
            triggerType: 'join',
            enabled: true
        }).sort({ priority: -1 });
        if (templates.length === 0) {
            return result;
        }
        for (const template of templates) {
            try {
                await processTemplate(member, template, null);
                result.templatesProcessed++;
                result.sent = true;
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                result.errors.push(`Template "${template.name}": ${errorMessage}`);
                console.error(`[AutoMessage] Error processing join template "${template.name}":`, error);
            }
        }
        return result;
    }
    catch (error) {
        result.errors.push(`Failed to fetch join templates: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return result;
    }
}
async function processRoleChangeMessage(member, role, triggerType) {
    const result = {
        sent: false,
        templatesProcessed: 0,
        errors: []
    };
    try {
        const templates = await WelcomeTemplate_1.WelcomeTemplate.find({
            guildId: member.guild.id,
            triggerType: triggerType,
            triggerRoleId: role.id,
            enabled: true
        }).sort({ priority: -1 });
        if (templates.length === 0) {
            return result;
        }
        for (const template of templates) {
            try {
                await processTemplate(member, template, role);
                result.templatesProcessed++;
                result.sent = true;
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                result.errors.push(`Template "${template.name}": ${errorMessage}`);
                console.error(`[AutoMessage] Error processing role change template "${template.name}":`, error);
            }
        }
        return result;
    }
    catch (error) {
        result.errors.push(`Failed to fetch role change templates: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return result;
    }
}
async function processTestMessage(member, template) {
    try {
        const components = createMessageComponents(template);
        const isRoleBasedTrigger = template.triggerType === 'role_add' || template.triggerType === 'role_remove';
        const userMention = `<@${member.id}>`;
        if (template.useEmbed) {
            const embed = await createMessageEmbed(member, template, null);
            const result = {
                embed,
                components: components.length > 0 ? components : undefined
            };
            if (isRoleBasedTrigger) {
                result.content = userMention;
            }
            return result;
        }
        else {
            const content = createPlainTextMessage(member, template, null);
            const finalContent = isRoleBasedTrigger ? `${userMention} ${content}` : content;
            return {
                content: finalContent,
                components: components.length > 0 ? components : undefined
            };
        }
    }
    catch (error) {
        throw new Error(`Failed to create test message: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function processTemplate(member, template, role) {
    if (template.delaySeconds > 0) {
        await new Promise(resolve => setTimeout(resolve, template.delaySeconds * 1000));
    }
    let messageOptions;
    const isRoleBasedTrigger = template.triggerType === 'role_add' || template.triggerType === 'role_remove';
    const userMention = `<@${member.id}>`;
    if (template.useEmbed) {
        const embed = await createMessageEmbed(member, template, role);
        const components = createMessageComponents(template);
        messageOptions = {
            embeds: [embed],
            components: components.length > 0 ? components : undefined
        };
        if (isRoleBasedTrigger) {
            messageOptions.content = userMention;
        }
    }
    else {
        const content = createPlainTextMessage(member, template, role);
        const components = createMessageComponents(template);
        const finalContent = isRoleBasedTrigger ? `${userMention} ${content}` : content;
        messageOptions = {
            content: finalContent,
            components: components.length > 0 ? components : undefined
        };
    }
    switch (template.deliveryType) {
        case 'dm':
            try {
                await member.send(messageOptions);
            }
            catch (error) {
                throw new Error(`Failed to send DM: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
            break;
        case 'channel':
            if (!template.channelId) {
                throw new Error('No channel specified for channel delivery');
            }
            const channel = member.guild.channels.cache.get(template.channelId);
            if (!channel || !channel.isTextBased()) {
                throw new Error('Channel not found or not text-based');
            }
            try {
                await channel.send(messageOptions);
            }
            catch (error) {
                throw new Error(`Failed to send to channel: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
            break;
        case 'both':
            if (!template.channelId) {
                throw new Error('No channel specified for both delivery');
            }
            const bothChannel = member.guild.channels.cache.get(template.channelId);
            if (!bothChannel || !bothChannel.isTextBased()) {
                throw new Error('Channel not found or not text-based');
            }
            const errors = [];
            try {
                await member.send(messageOptions);
            }
            catch (error) {
                errors.push(`DM failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
            try {
                await bothChannel.send(messageOptions);
            }
            catch (error) {
                errors.push(`Channel failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
            if (errors.length === 2) {
                throw new Error(`Both delivery methods failed: ${errors.join(', ')}`);
            }
            break;
        default:
            throw new Error(`Unknown delivery type: ${template.deliveryType}`);
    }
}
async function createMessageEmbed(member, template, role) {
    const embedColor = template.color || await configurableConstants_1.default.getEmbedColor(member.guild.id);
    const embed = new discord_js_1.EmbedBuilder()
        .setColor(embedColor);
    if (template.title) {
        embed.setTitle(replacePlaceholders(template.title, member, role));
    }
    if (template.description) {
        embed.setDescription(replacePlaceholders(template.description, member, role));
    }
    if (template.imageUrl) {
        embed.setImage(template.imageUrl);
    }
    if (template.thumbnailUrl) {
        embed.setThumbnail(template.thumbnailUrl);
    }
    else {
        if (template.triggerType === 'join') {
            embed.setThumbnail(member.displayAvatarURL({ size: 128 }));
        }
    }
    if (template.footerText) {
        embed.setFooter({ text: replacePlaceholders(template.footerText, member, role) });
    }
    else {
        const serverFooter = await configurableConstants_1.default.getEmbedFooter(member.guild.id);
        embed.setFooter({ text: serverFooter });
    }
    if (template.showTimestamp) {
        embed.setTimestamp();
    }
    if (template.fields && template.fields.length > 0) {
        for (const field of template.fields) {
            embed.addFields({
                name: replacePlaceholders(field.name, member, role),
                value: replacePlaceholders(field.value, member, role),
                inline: field.inline || false
            });
        }
    }
    return embed;
}
function createPlainTextMessage(member, template, role) {
    if (!template.description) {
        return 'No message content provided.';
    }
    return replacePlaceholders(template.description, member, role);
}
function createMessageComponents(template) {
    if (!template.buttons || template.buttons.length === 0) {
        return [];
    }
    const buttons = template.buttons.map(button => new discord_js_1.ButtonBuilder()
        .setLabel(button.label)
        .setURL(button.url)
        .setStyle(discord_js_1.ButtonStyle.Link));
    const actionRows = [];
    for (let i = 0; i < buttons.length; i += 5) {
        const row = new discord_js_1.ActionRowBuilder()
            .addComponents(buttons.slice(i, i + 5));
        actionRows.push(row);
    }
    return actionRows;
}
function replacePlaceholders(text, member, role) {
    let result = text
        .replace(/\\n/g, '\n')
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n');
    result = result.replace(/{user}/g, member.displayName);
    result = result.replace(/{user\.mention}/g, `<@${member.id}>`);
    result = result.replace(/{user\.username}/g, member.user.username);
    result = result.replace(/{user\.displayName}/g, member.displayName);
    result = result.replace(/{user\.id}/g, member.id);
    result = result.replace(/{user\.tag}/g, member.user.tag);
    result = result.replace(/{server}/g, member.guild.name);
    result = result.replace(/{server\.name}/g, member.guild.name);
    result = result.replace(/{server\.id}/g, member.guild.id);
    result = result.replace(/{server\.memberCount}/g, member.guild.memberCount.toString());
    if (role) {
        result = result.replace(/{role}/g, role.name);
        result = result.replace(/{role\.name}/g, role.name);
        result = result.replace(/{role\.id}/g, role.id);
        result = result.replace(/{role\.mention}/g, `<@&${role.id}>`);
    }
    const now = new Date();
    result = result.replace(/{date}/g, now.toLocaleDateString());
    result = result.replace(/{time}/g, now.toLocaleTimeString());
    result = result.replace(/{datetime}/g, now.toLocaleString());
    return result;
}
