"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.shutdownLogger = exports.loggers = exports.createLogger = exports.getLogger = exports.CategoryLogger = exports.Logger = exports.DatabaseService = exports.startApplication = exports.getApplication = exports.Application = void 0;
var application_1 = require("./application");
Object.defineProperty(exports, "Application", { enumerable: true, get: function () { return application_1.Application; } });
Object.defineProperty(exports, "getApplication", { enumerable: true, get: function () { return application_1.getApplication; } });
Object.defineProperty(exports, "startApplication", { enumerable: true, get: function () { return application_1.startApplication; } });
var database_1 = require("./database");
Object.defineProperty(exports, "DatabaseService", { enumerable: true, get: function () { return database_1.DatabaseService; } });
var logger_1 = require("./logger");
Object.defineProperty(exports, "Logger", { enumerable: true, get: function () { return logger_1.Logger; } });
Object.defineProperty(exports, "CategoryLogger", { enumerable: true, get: function () { return logger_1.CategoryLogger; } });
Object.defineProperty(exports, "getLogger", { enumerable: true, get: function () { return logger_1.getLogger; } });
Object.defineProperty(exports, "createLogger", { enumerable: true, get: function () { return logger_1.createLogger; } });
Object.defineProperty(exports, "loggers", { enumerable: true, get: function () { return logger_1.loggers; } });
Object.defineProperty(exports, "shutdownLogger", { enumerable: true, get: function () { return logger_1.shutdownLogger; } });
__exportStar(require("./interfaces"), exports);
