"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeValidator = void 0;
const BaseService_1 = require("../../base/BaseService");
const errorHandler_1 = require("../../../utils/errorHandler");
const constants_1 = require("../../../config/constants");
const models_1 = require("../../../models");
class TradeValidator extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'TradeValidator';
    }
    async initialize() {
        this.logger.info('[TradeValidator] Trade validator initialized');
    }
    async validateTradeCreation(params) {
        this.logOperation('Validating trade creation', params);
        try {
            this.validateBasicTradeParams(params);
            await this.validateTradeParties(params.sellerId, params.buyerId);
            await this.validateNoActiveTradeBetweenParties(params.sellerId, params.buyerId, params.guildId);
            await this.validateUserTradeEligibility(params.sellerId, params.guildId);
            await this.validateUserTradeEligibility(params.buyerId, params.guildId);
            if (params.initiatedBy === 'BUYER') {
                await this.validateBuyerBalance(params.buyerId, params.amount);
            }
            this.logOperation('Trade creation validation passed', {
                sellerId: params.sellerId,
                buyerId: params.buyerId,
                amount: params.amount
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'validate_trade_creation', params });
            throw error;
        }
    }
    async validateTradeAcceptance(trade, acceptingUserId) {
        this.logOperation('Validating trade acceptance', {
            tradeId: trade.tradeId,
            acceptingUserId
        });
        try {
            if (trade.state !== constants_1.TRADE.STATES.PROPOSED) {
                throw new errorHandler_1.ValidationError(`Trade is not in PROPOSED state. Current state: ${trade.state}`);
            }
            if (trade.isExpired()) {
                throw new errorHandler_1.ValidationError('Trade has expired and cannot be accepted');
            }
            if (!trade.involvesUser(acceptingUserId)) {
                throw new errorHandler_1.ValidationError('You are not a party to this trade');
            }
            const isInitiator = (trade.initiatedBy === 'SELLER' && trade.sellerId === acceptingUserId) ||
                (trade.initiatedBy === 'BUYER' && trade.buyerId === acceptingUserId);
            if (isInitiator) {
                throw new errorHandler_1.ValidationError('You cannot accept your own trade proposal');
            }
            await this.validateBuyerBalance(trade.buyerId, trade.amount);
            await this.validateUserTradeEligibility(acceptingUserId, trade.guildId);
            this.logOperation('Trade acceptance validation passed', {
                tradeId: trade.tradeId,
                acceptingUserId
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'validate_trade_acceptance', tradeId: trade.tradeId });
            throw error;
        }
    }
    async validateTradeConfirmation(trade, confirmingUserId) {
        this.logOperation('Validating trade confirmation', {
            tradeId: trade.tradeId,
            confirmingUserId
        });
        try {
            if (trade.state !== constants_1.TRADE.STATES.ACTIVE) {
                throw new errorHandler_1.ValidationError(`Trade is not in ACTIVE state. Current state: ${trade.state}`);
            }
            if (trade.isExpired()) {
                throw new errorHandler_1.ValidationError('Trade has expired and cannot be confirmed');
            }
            if (!trade.involvesUser(confirmingUserId)) {
                throw new errorHandler_1.ValidationError('You are not a party to this trade');
            }
            if ((trade.sellerId === confirmingUserId && trade.sellerConfirmed) ||
                (trade.buyerId === confirmingUserId && trade.buyerConfirmed)) {
                throw new errorHandler_1.ValidationError('You have already confirmed this trade');
            }
            this.logOperation('Trade confirmation validation passed', {
                tradeId: trade.tradeId,
                confirmingUserId
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'validate_trade_confirmation', tradeId: trade.tradeId });
            throw error;
        }
    }
    async validateTradeCancellation(trade, cancellingUserId) {
        this.logOperation('Validating trade cancellation', {
            tradeId: trade.tradeId,
            cancellingUserId
        });
        try {
            const cancellableStates = [constants_1.TRADE.STATES.PROPOSED, constants_1.TRADE.STATES.ACCEPTED, constants_1.TRADE.STATES.ACTIVE];
            if (!cancellableStates.includes(trade.state)) {
                throw new errorHandler_1.ValidationError(`Trade cannot be cancelled in ${trade.state} state`);
            }
            if (!trade.involvesUser(cancellingUserId)) {
                throw new errorHandler_1.ValidationError('You are not a party to this trade');
            }
            if (trade.state === constants_1.TRADE.STATES.ACTIVE) {
                if (trade.sellerConfirmed && trade.buyerConfirmed) {
                    throw new errorHandler_1.ValidationError('Trade cannot be cancelled after both parties have confirmed completion');
                }
            }
            this.logOperation('Trade cancellation validation passed', {
                tradeId: trade.tradeId,
                cancellingUserId
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'validate_trade_cancellation', tradeId: trade.tradeId });
            throw error;
        }
    }
    async validateDisputeInitiation(trade, disputingUserId) {
        this.logOperation('Validating dispute initiation', {
            tradeId: trade.tradeId,
            disputingUserId
        });
        try {
            if (trade.state !== constants_1.TRADE.STATES.ACTIVE) {
                throw new errorHandler_1.ValidationError(`Disputes can only be initiated for ACTIVE trades. Current state: ${trade.state}`);
            }
            if (!trade.involvesUser(disputingUserId)) {
                throw new errorHandler_1.ValidationError('You are not a party to this trade');
            }
            if (trade.disputeId) {
                throw new errorHandler_1.ValidationError('This trade already has an active dispute');
            }
            const hoursUntilExpiration = (trade.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60);
            if (hoursUntilExpiration < 1) {
                throw new errorHandler_1.ValidationError('Trade is too close to expiration. Please wait for automatic expiration.');
            }
            this.logOperation('Dispute initiation validation passed', {
                tradeId: trade.tradeId,
                disputingUserId
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'validate_dispute_initiation', tradeId: trade.tradeId });
            throw error;
        }
    }
    validateBasicTradeParams(params) {
        if (!constants_1.VALIDATION.DISCORD_ID_REGEX.test(params.sellerId)) {
            throw new errorHandler_1.ValidationError('Invalid seller Discord ID');
        }
        if (!constants_1.VALIDATION.DISCORD_ID_REGEX.test(params.buyerId)) {
            throw new errorHandler_1.ValidationError('Invalid buyer Discord ID');
        }
        if (!constants_1.VALIDATION.DISCORD_ID_REGEX.test(params.guildId)) {
            throw new errorHandler_1.ValidationError('Invalid guild Discord ID');
        }
        if (!Number.isInteger(params.amount) || params.amount < constants_1.TRADE.MIN_TRADE_AMOUNT) {
            throw new errorHandler_1.ValidationError(`Trade amount must be at least ${constants_1.TRADE.MIN_TRADE_AMOUNT} coins`);
        }
        if (params.amount > constants_1.TRADE.MAX_TRADE_AMOUNT) {
            throw new errorHandler_1.ValidationError(`Trade amount cannot exceed ${constants_1.TRADE.MAX_TRADE_AMOUNT} coins`);
        }
        if (!params.itemDescription || params.itemDescription.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Item description is required');
        }
        if (params.itemDescription.length > constants_1.VALIDATION.MAX_DESCRIPTION_LENGTH) {
            throw new errorHandler_1.ValidationError(`Item description cannot exceed ${constants_1.VALIDATION.MAX_DESCRIPTION_LENGTH} characters`);
        }
        if (params.notes && params.notes.length > constants_1.VALIDATION.MAX_REASON_LENGTH) {
            throw new errorHandler_1.ValidationError(`Notes cannot exceed ${constants_1.VALIDATION.MAX_REASON_LENGTH} characters`);
        }
        if (!['SELLER', 'BUYER'].includes(params.initiatedBy)) {
            throw new errorHandler_1.ValidationError('Invalid initiator type');
        }
    }
    async validateTradeParties(sellerId, buyerId) {
        if (sellerId === buyerId) {
            throw new errorHandler_1.ValidationError('You cannot trade with yourself');
        }
        const seller = await models_1.User.findOne({ discordId: sellerId });
        const buyer = await models_1.User.findOne({ discordId: buyerId });
        if (!seller) {
            throw new errorHandler_1.ValidationError('Seller not found in the system');
        }
        if (!buyer) {
            throw new errorHandler_1.ValidationError('Buyer not found in the system');
        }
    }
    async validateNoActiveTradeBetweenParties(sellerId, buyerId, guildId) {
        const existingTrade = await models_1.Trade.findOne({
            guildId,
            $or: [
                { sellerId, buyerId },
                { sellerId: buyerId, buyerId: sellerId }
            ],
            state: { $in: [constants_1.TRADE.STATES.PROPOSED, constants_1.TRADE.STATES.ACCEPTED, constants_1.TRADE.STATES.ACTIVE] }
        });
        if (existingTrade) {
            throw new errorHandler_1.ValidationError('You already have an active trade with this user');
        }
    }
    async validateUserTradeEligibility(discordId, guildId) {
        let userStats = await models_1.UserTradeStats.findOne({ discordId, guildId });
        if (!userStats) {
            userStats = new models_1.UserTradeStats({
                discordId,
                guildId,
                totalTrades: 0,
                successfulTrades: 0,
                cancelledTrades: 0,
                expiredTrades: 0,
                disputedTrades: 0,
                tradesAsSeller: 0,
                tradesAsBuyer: 0,
                totalVolumeTraded: 0,
                averageTradeValue: 0,
                largestTrade: 0,
                reputationScore: 50,
                disputeRatio: 0,
                completionRate: 0,
                averageCompletionTime: 0,
                fastestCompletion: 0,
                activeTrades: 0,
                isRestricted: false,
                dailyTradeCount: 0,
                lastTradeDate: new Date(),
                lastResetDate: new Date(),
                lastUpdated: new Date(),
                warningsReceived: 0,
                violationHistory: []
            });
            await userStats.save();
        }
        const eligibility = userStats.canTrade();
        if (!eligibility.canTrade) {
            throw new errorHandler_1.ValidationError(eligibility.reason || 'User is not eligible to trade');
        }
    }
    async validateBuyerBalance(buyerId, amount) {
        const buyer = await models_1.User.findOne({ discordId: buyerId });
        if (!buyer) {
            throw new errorHandler_1.ValidationError('Buyer not found');
        }
        if (buyer.balance < amount) {
            throw new errorHandler_1.ValidationError(`Insufficient balance. Required: ${amount} coins, Available: ${buyer.balance} coins`);
        }
    }
}
exports.TradeValidator = TradeValidator;
