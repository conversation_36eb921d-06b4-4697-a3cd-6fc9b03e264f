"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElectionService = void 0;
const discord_js_1 = require("discord.js");
const mongoose_1 = __importDefault(require("mongoose"));
const crypto_1 = require("crypto");
const BaseService_1 = require("../base/BaseService");
const features_1 = require("../../config/features");
const errorHandler_1 = require("../../utils/errorHandler");
const idGenerator_1 = require("../../utils/idGenerator");
const Election_1 = __importDefault(require("../../models/Election"));
const ElectionCandidate_1 = __importDefault(require("../../models/ElectionCandidate"));
const ElectionVote_1 = __importDefault(require("../../models/ElectionVote"));
const ElectionHistory_1 = __importDefault(require("../../models/ElectionHistory"));
const economyService_1 = require("../economyService");
class ElectionService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'ElectionService';
    }
    async onInitialize() {
        if (!this.isFeatureEnabled('ECONOMY_SYSTEM')) {
            throw new Error('Election system requires economy system to be enabled');
        }
        this.logger.info('[ElectionService] Election system initialized');
    }
    async createElection(guildId, channelId, createdBy, config) {
        this.validateElectionConfig(config);
        if (mongoose_1.default.connection.readyState !== 1) {
            throw new errorHandler_1.DatabaseError('Database is not connected. Please try again in a moment.');
        }
        const electionId = (0, idGenerator_1.generateId)('election');
        try {
            const election = new Election_1.default({
                electionId,
                guildId,
                channelId,
                createdBy,
                title: config.title,
                description: config.description,
                rolesToPing: config.rolesToPing,
                eligibleVoterRoles: config.eligibleVoterRoles,
                eligibleCandidateRoles: config.eligibleCandidateRoles,
                allowMultipleVotes: config.allowMultipleVotes ?? true,
                showVoteWeights: config.showVoteWeights ?? true,
                status: 'ACTIVE'
            });
            await election.save();
            this.logger.info(`[ElectionService] Created election: ${electionId}`, {
                guildId,
                createdBy,
                title: config.title
            });
            return election;
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to create election', { error, electionId });
            throw new errorHandler_1.DatabaseError('Failed to create election');
        }
    }
    async getElection(electionId) {
        try {
            const election = await Election_1.default.findOne({ electionId });
            this.logger.debug('[ElectionService] Retrieved election', {
                electionId,
                found: !!election,
                status: election?.status,
                title: election?.title
            });
            return election;
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to get election', { error, electionId });
            throw new errorHandler_1.DatabaseError('Failed to retrieve election');
        }
    }
    async addCandidate(electionId, discordId, displayName, username, campaignMessage) {
        const session = await mongoose_1.default.startSession();
        try {
            return await session.withTransaction(async () => {
                const election = await Election_1.default.findOne({ electionId }).session(session);
                if (!election) {
                    throw new errorHandler_1.ValidationError('Election not found');
                }
                if (election.status !== 'ACTIVE') {
                    throw new errorHandler_1.ValidationError('Election is not currently accepting candidates');
                }
                const existingCandidate = await ElectionCandidate_1.default.findOne({
                    electionId,
                    userId: discordId,
                    withdrawn: false,
                    disqualified: false
                }).session(session);
                if (existingCandidate) {
                    throw new errorHandler_1.ValidationError('User is already a candidate in this election');
                }
                const candidateId = (0, crypto_1.randomUUID)();
                const bio = campaignMessage || `Candidate for ${election.title}`;
                const candidate = new ElectionCandidate_1.default({
                    candidateId,
                    electionId,
                    userId: discordId,
                    guildId: election.guildId,
                    displayName,
                    username,
                    bio,
                    campaignMessage,
                    withdrawn: false,
                    disqualified: false,
                    nominationTime: new Date()
                });
                await candidate.save({ session });
                await Election_1.default.updateOne({ electionId }, { $inc: { totalCandidates: 1 } }).session(session);
                this.logger.info(`[ElectionService] Added candidate to election`, {
                    electionId,
                    candidateId: discordId,
                    displayName
                });
                return candidate;
            });
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to add candidate', {
                error,
                electionId,
                discordId
            });
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            throw new errorHandler_1.DatabaseError('Failed to add candidate to election');
        }
        finally {
            await session.endSession();
        }
    }
    async removeCandidate(electionId, discordId) {
        const session = await mongoose_1.default.startSession();
        try {
            await session.withTransaction(async () => {
                const candidate = await ElectionCandidate_1.default.findOneAndUpdate({
                    electionId,
                    userId: discordId,
                    withdrawn: false,
                    disqualified: false
                }, {
                    withdrawn: true,
                    withdrawnAt: new Date()
                }, { session, new: true });
                if (!candidate) {
                    throw new errorHandler_1.ValidationError('Candidate not found or already resigned');
                }
                await ElectionVote_1.default.updateMany({
                    electionId,
                    candidateId: discordId,
                    status: 'ACTIVE'
                }, {
                    status: 'INVALIDATED',
                    invalidatedAt: new Date(),
                    invalidationReason: 'Candidate resigned'
                }).session(session);
                const voteStats = await this.calculateElectionStats(electionId, session);
                await Election_1.default.updateOne({ electionId }, {
                    $inc: { totalCandidates: -1 },
                    totalVotes: voteStats.totalVotes,
                    totalVoteWeight: voteStats.totalVoteWeight
                }).session(session);
                await this.recalculateCandidateVotes(electionId, session);
                this.logger.info(`[ElectionService] Removed candidate from election`, {
                    electionId,
                    candidateId: discordId
                });
            });
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to remove candidate', {
                error,
                electionId,
                discordId
            });
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            throw new errorHandler_1.DatabaseError('Failed to remove candidate from election');
        }
        finally {
            await session.endSession();
        }
    }
    async castVote(electionId, voterId, candidateId, voterDisplayName, voterUsername, candidateDisplayName, candidateUsername, guildId) {
        const user = await (0, economyService_1.ensureUser)(voterId, guildId);
        const voteWeight = user.balance;
        if (voteWeight <= 0) {
            throw new errorHandler_1.ValidationError('You need coins to vote. Your vote weight is based on your coin balance.');
        }
        const session = await mongoose_1.default.startSession();
        try {
            return await session.withTransaction(async () => {
                const election = await Election_1.default.findOne({
                    electionId,
                    status: 'ACTIVE'
                }).session(session);
                if (!election) {
                    throw new errorHandler_1.ValidationError('Election not found or not active');
                }
                const candidate = await ElectionCandidate_1.default.findOne({
                    electionId,
                    userId: candidateId,
                    withdrawn: false,
                    disqualified: false
                }).session(session);
                if (!candidate) {
                    throw new errorHandler_1.ValidationError('Candidate not found or not active');
                }
                const existingVote = await ElectionVote_1.default.findOne({
                    electionId,
                    voterId,
                    status: 'ACTIVE'
                }).session(session);
                let newVote;
                if (existingVote) {
                    if (!election.allowMultipleVotes) {
                        throw new errorHandler_1.ValidationError('Vote changes are not allowed in this election');
                    }
                    existingVote.status = 'REPLACED';
                    existingVote.replacedAt = new Date();
                    await existingVote.save({ session });
                    newVote = new ElectionVote_1.default({
                        voteId: (0, crypto_1.randomUUID)(),
                        electionId,
                        voterId,
                        candidateId,
                        guildId: election.guildId,
                        voteWeight,
                        voterBalance: user.balance,
                        voterDisplayName,
                        voterUsername,
                        candidateDisplayName,
                        candidateUsername,
                        status: 'ACTIVE',
                        replacedBy: existingVote._id
                    });
                }
                else {
                    newVote = new ElectionVote_1.default({
                        voteId: (0, crypto_1.randomUUID)(),
                        electionId,
                        voterId,
                        candidateId,
                        guildId: election.guildId,
                        voteWeight,
                        voterBalance: user.balance,
                        voterDisplayName,
                        voterUsername,
                        candidateDisplayName,
                        candidateUsername,
                        status: 'ACTIVE'
                    });
                }
                await newVote.save({ session });
                const stats = await this.calculateElectionStats(electionId, session);
                await Election_1.default.updateOne({ electionId }, {
                    totalVotes: stats.totalVotes,
                    totalVoteWeight: stats.totalVoteWeight
                }).session(session);
                await this.recalculateCandidateVotes(electionId, session);
                this.logger.info(`[ElectionService] Vote cast`, {
                    electionId,
                    voterId,
                    candidateId,
                    voteWeight,
                    isUpdate: !!existingVote
                });
                return newVote;
            });
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to cast vote', {
                error,
                electionId,
                voterId,
                candidateId
            });
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            throw new errorHandler_1.DatabaseError('Failed to cast vote');
        }
        finally {
            await session.endSession();
        }
    }
    async getElectionCandidates(electionId) {
        try {
            const candidates = await ElectionCandidate_1.default.find({
                electionId,
                withdrawn: false,
                disqualified: false
            }).sort({ nominationTime: 1 });
            const candidatesWithVotes = await Promise.all(candidates.map(async (candidate) => {
                const voteStats = await ElectionVote_1.default.aggregate([
                    { $match: { electionId, candidateId: candidate.userId, status: 'ACTIVE' } },
                    {
                        $group: {
                            _id: null,
                            voteCount: { $sum: 1 },
                            voteWeight: { $sum: '$voteWeight' }
                        }
                    }
                ]);
                const stats = voteStats[0] || { voteCount: 0, voteWeight: 0 };
                return {
                    rank: 0,
                    percentage: 0,
                    electionId: candidate.electionId,
                    discordId: candidate.userId,
                    guildId: candidate.guildId,
                    displayName: candidate.displayName,
                    username: candidate.username,
                    campaignMessage: candidate.campaignMessage,
                    voteCount: stats.voteCount,
                    voteWeight: stats.voteWeight,
                    status: candidate.withdrawn ? 'WITHDRAWN' : candidate.disqualified ? 'DISQUALIFIED' : 'ACTIVE',
                    joinedAt: candidate.nominationTime,
                    resignedAt: candidate.withdrawnAt,
                    disqualifiedAt: candidate.disqualified ? new Date() : undefined,
                    disqualificationReason: candidate.disqualificationReason,
                    disqualifiedBy: candidate.disqualifiedBy
                };
            }));
            candidatesWithVotes.sort((a, b) => b.voteWeight - a.voteWeight);
            const totalVoteWeight = candidatesWithVotes.reduce((sum, c) => sum + c.voteWeight, 0);
            candidatesWithVotes.forEach((candidate, index) => {
                candidate.rank = index + 1;
                candidate.percentage = totalVoteWeight > 0 ? (candidate.voteWeight / totalVoteWeight) * 100 : 0;
            });
            return candidatesWithVotes;
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to get candidates', { error, electionId });
            throw new errorHandler_1.DatabaseError('Failed to retrieve election candidates');
        }
    }
    async canUserVote(electionId, userId, member) {
        try {
            if (!electionId || !userId || !member) {
                this.logger.warn('[ElectionService] Invalid parameters for vote eligibility check', { electionId, userId, hasMember: !!member });
                return false;
            }
            const election = await Election_1.default.findOne({ electionId, status: 'ACTIVE' });
            if (!election) {
                this.logger.debug('[ElectionService] Election not found or not active for vote check', { electionId });
                return false;
            }
            if (!member.roles || !member.roles.cache) {
                this.logger.error('[ElectionService] Member roles cache not available', { userId, electionId });
                return false;
            }
            try {
                const user = await (0, economyService_1.ensureUser)(userId, member.guild.id);
                if (user.balance <= 0) {
                    this.logger.debug('[ElectionService] User has insufficient balance to vote', { userId, balance: user.balance });
                    return false;
                }
            }
            catch (balanceError) {
                this.logger.error('[ElectionService] Failed to check user balance for voting', { error: balanceError, userId });
                return false;
            }
            const hasEligibleRole = election.eligibleVoterRoles.some(roleId => {
                const hasRole = member.roles.cache.has(roleId);
                this.logger.debug('[ElectionService] Role check', {
                    userId,
                    roleId,
                    hasRole,
                    userRoles: member.roles.cache.map(r => r.id)
                });
                return hasRole;
            });
            this.logger.debug('[ElectionService] Vote eligibility check result', {
                electionId,
                userId,
                hasEligibleRole,
                eligibleVoterRoles: election.eligibleVoterRoles,
                userRoles: member.roles.cache.map(r => r.id)
            });
            return hasEligibleRole;
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to check vote eligibility', {
                error,
                electionId,
                userId
            });
            return false;
        }
    }
    async canUserBeCandidate(electionId, userId, member) {
        try {
            if (!electionId || !userId || !member) {
                this.logger.warn('[ElectionService] Invalid parameters for candidate eligibility check', { electionId, userId, hasMember: !!member });
                return false;
            }
            const election = await Election_1.default.findOne({ electionId, status: 'ACTIVE' });
            if (!election) {
                this.logger.debug('[ElectionService] Election not found or not active for candidate check', { electionId });
                return false;
            }
            if (!member.roles || !member.roles.cache) {
                this.logger.error('[ElectionService] Member roles cache not available for candidate check', { userId, electionId });
                return false;
            }
            const isAlreadyCandidate = await this.isUserCandidate(electionId, userId);
            if (isAlreadyCandidate) {
                this.logger.debug('[ElectionService] User is already a candidate', { userId, electionId });
                return false;
            }
            const hasEligibleRole = election.eligibleCandidateRoles.some(roleId => {
                const hasRole = member.roles.cache.has(roleId);
                this.logger.debug('[ElectionService] Candidate role check', {
                    userId,
                    roleId,
                    hasRole,
                    userRoles: member.roles.cache.map(r => r.id)
                });
                return hasRole;
            });
            this.logger.debug('[ElectionService] Candidate eligibility check result', {
                electionId,
                userId,
                hasEligibleRole,
                eligibleCandidateRoles: election.eligibleCandidateRoles,
                userRoles: member.roles.cache.map(r => r.id)
            });
            return hasEligibleRole;
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to check candidate eligibility', {
                error,
                electionId,
                userId
            });
            return false;
        }
    }
    async getUserVote(electionId, userId) {
        try {
            return await ElectionVote_1.default.findOne({
                electionId,
                voterId: userId,
                status: 'ACTIVE'
            });
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to get user vote', {
                error,
                electionId,
                userId
            });
            return null;
        }
    }
    async isUserCandidate(electionId, userId) {
        try {
            const candidate = await ElectionCandidate_1.default.findOne({
                electionId,
                userId: userId,
                withdrawn: false,
                disqualified: false
            });
            return !!candidate;
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to check candidate status', {
                error,
                electionId,
                userId
            });
            return false;
        }
    }
    async canUserEndElection(electionId, userId, member) {
        try {
            this.logger.debug('[ElectionService] Checking end election permissions', {
                electionId,
                userId,
                guildId: member.guild.id
            });
            const election = await Election_1.default.findOne({ electionId });
            if (!election) {
                this.logger.debug('[ElectionService] Election not found for end check', { electionId });
                return false;
            }
            this.logger.debug('[ElectionService] End permission debug', {
                electionId,
                userId,
                hasAdminPerms: member.permissions.has(discord_js_1.PermissionFlagsBits.Administrator),
                isCreator: election.createdBy === userId
            });
            if (!member || !member.permissions) {
                this.logger.error('[ElectionService] Invalid member object for end election', {
                    userId,
                    electionId,
                    hasMember: !!member,
                    hasPermissions: !!(member?.permissions)
                });
                return false;
            }
            if (member.guild.id !== election.guildId) {
                this.logger.error('[ElectionService] Guild mismatch for end election', {
                    userId,
                    electionId,
                    memberGuildId: member.guild.id,
                    electionGuildId: election.guildId
                });
                return false;
            }
            const isCreator = election.createdBy === userId;
            const isGuildOwner = member.guild.ownerId === userId;
            const hasAdminPermissions = member.permissions.has(discord_js_1.PermissionFlagsBits.Administrator);
            const hasManageMessages = member.permissions.has(discord_js_1.PermissionFlagsBits.ManageMessages);
            const canEnd = isCreator || isGuildOwner || hasAdminPermissions || hasManageMessages;
            console.log('=== ELECTION END PERMISSION CHECK (FIXED SYSTEM) ===');
            console.log('Election ID:', electionId);
            console.log('User ID:', userId);
            console.log('Election Creator:', election.createdBy);
            console.log('Is Creator:', isCreator);
            console.log('Is Guild Owner:', isGuildOwner);
            console.log('Has Administrator:', hasAdminPermissions);
            console.log('Has Manage Messages:', hasManageMessages);
            console.log('Can End Election:', canEnd);
            console.log('Guild Match:', member.guild.id === election.guildId);
            console.log('=== END PERMISSION CHECK ===');
            this.logger.debug('[ElectionService] End election permission result', {
                electionId,
                userId,
                isCreator,
                hasAdminPermissions,
                hasManageMessages,
                canEnd,
                guildMatch: member.guild.id === election.guildId
            });
            return canEnd;
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to check end election permission', {
                error,
                electionId,
                userId
            });
            return false;
        }
    }
    async endElection(electionId, endedBy) {
        try {
            this.logger.info('[ElectionService] Ending election', {
                electionId,
                endedBy
            });
            const election = await Election_1.default.findOne({ electionId });
            if (!election) {
                this.logger.error('[ElectionService] Election not found for ending', { electionId });
                throw new errorHandler_1.ValidationError('Election not found');
            }
            if (election.status !== 'ACTIVE') {
                this.logger.error('[ElectionService] Election not active for ending', {
                    electionId,
                    currentStatus: election.status,
                    endedBy,
                    endedAt: election.endedAt,
                    archivedAt: election.archivedAt,
                    possibleRaceCondition: election.status === 'ENDED' || election.status === 'ARCHIVED'
                });
                if (election.status === 'ENDED') {
                    throw new errorHandler_1.ValidationError('Election has already been ended');
                }
                else if (election.status === 'ARCHIVED') {
                    throw new errorHandler_1.ValidationError('Election has already been ended and archived');
                }
                else {
                    throw new errorHandler_1.ValidationError(`Election is not active (current status: ${election.status})`);
                }
            }
            const previousStatus = election.status;
            const statusChangeTimestamp = new Date();
            this.logger.info('[ElectionService] Status change initiated', {
                electionId,
                previousStatus,
                newStatus: 'ENDED',
                endedBy,
                timestamp: statusChangeTimestamp.toISOString(),
                stackTrace: new Error().stack?.split('\n').slice(1, 4).join(' | ')
            });
            election.status = 'ENDED';
            election.endedAt = statusChangeTimestamp;
            election.endedBy = endedBy;
            await election.save();
            this.logger.info('[ElectionService] Status change completed', {
                electionId,
                previousStatus,
                newStatus: 'ENDED',
                endedBy,
                timestamp: statusChangeTimestamp.toISOString(),
                saved: true
            });
            this.logger.info('[ElectionService] Election ended successfully', {
                electionId,
                endedBy,
                endedAt: election.endedAt,
                totalVotes: election.totalVotes,
                totalVoteWeight: election.totalVoteWeight
            });
            return election;
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to end election', {
                error: error instanceof Error ? error.message : 'Unknown error',
                electionId,
                endedBy
            });
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            throw new errorHandler_1.DatabaseError('Failed to end election', error);
        }
    }
    async calculateFinalResults(electionId, guildMemberCount) {
        try {
            const candidates = await this.getElectionCandidates(electionId);
            const election = await this.getElection(electionId);
            if (!election) {
                throw new errorHandler_1.ValidationError('Election not found');
            }
            const stats = {
                totalVotes: election.totalVotes,
                totalVoteWeight: election.totalVoteWeight,
                totalCandidates: election.totalCandidates,
                activeCandidates: candidates.filter(c => c.status === 'ACTIVE').length
            };
            let participationRate;
            if (guildMemberCount && guildMemberCount > 0) {
                participationRate = (stats.totalVotes / guildMemberCount) * 100;
            }
            return {
                candidates,
                stats,
                participationRate
            };
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to calculate final results', {
                error,
                electionId
            });
            throw new errorHandler_1.DatabaseError('Failed to calculate election results');
        }
    }
    async archiveElection(electionId, endedBy, guildMemberCount) {
        const session = await mongoose_1.default.startSession();
        try {
            await session.withTransaction(async () => {
                const election = await Election_1.default.findOne({ electionId }).session(session);
                if (!election) {
                    throw new errorHandler_1.ValidationError('Election not found');
                }
                const candidates = await this.getElectionCandidates(electionId);
                const winner = candidates.length > 0 ? candidates[0] : null;
                let participationRate;
                if (guildMemberCount && guildMemberCount > 0) {
                    participationRate = (election.totalVotes / guildMemberCount) * 100;
                }
                const historyRecord = new ElectionHistory_1.default({
                    originalElectionId: electionId,
                    guildId: election.guildId,
                    title: election.title,
                    description: election.description,
                    createdBy: election.createdBy,
                    winnerId: winner?.discordId,
                    winnerDisplayName: winner?.displayName,
                    winnerVoteWeight: winner?.voteWeight || 0,
                    winnerPercentage: winner?.percentage || 0,
                    totalVotes: election.totalVotes,
                    totalVoteWeight: election.totalVoteWeight,
                    totalCandidates: election.totalCandidates,
                    participationRate,
                    electionStarted: election.createdAt,
                    electionEnded: election.endedAt || new Date(),
                    archivedAt: new Date(),
                    rolesToPing: election.rolesToPing,
                    eligibleVoterRoles: election.eligibleVoterRoles,
                    eligibleCandidateRoles: election.eligibleCandidateRoles,
                    allowedMultipleVotes: election.allowMultipleVotes,
                    showedVoteWeights: election.showVoteWeights,
                    endedBy: endedBy || election.createdBy,
                    duration: (election.endedAt || new Date()).getTime() - election.createdAt.getTime()
                });
                await historyRecord.save({ session });
                await Election_1.default.updateOne({ electionId }, {
                    status: 'ARCHIVED',
                    archivedAt: new Date()
                }).session(session);
                this.logger.info(`[ElectionService] Election archived with historical record`, {
                    electionId,
                    historyId: historyRecord._id,
                    archivedAt: new Date()
                });
            });
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to archive election', {
                error,
                electionId
            });
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            throw new errorHandler_1.DatabaseError('Failed to archive election');
        }
        finally {
            await session.endSession();
        }
    }
    async getElectionHistory(guildId, limit = 10) {
        try {
            return await ElectionHistory_1.default.find({ guildId })
                .sort({ archivedAt: -1 })
                .limit(limit);
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to get election history', {
                error,
                guildId
            });
            throw new errorHandler_1.DatabaseError('Failed to retrieve election history');
        }
    }
    async getUserElectionHistory(userId, guildId) {
        try {
            const query = guildId ? { guildId } : {};
            const [created, won] = await Promise.all([
                ElectionHistory_1.default.find({ ...query, createdBy: userId }).sort({ archivedAt: -1 }).limit(10),
                ElectionHistory_1.default.find({ ...query, winnerId: userId }).sort({ archivedAt: -1 }).limit(10)
            ]);
            const participated = [];
            return {
                created,
                won,
                participated
            };
        }
        catch (error) {
            this.logger.error('[ElectionService] Failed to get user election history', {
                error,
                userId,
                guildId
            });
            throw new errorHandler_1.DatabaseError('Failed to retrieve user election history');
        }
    }
    validateElectionConfig(config) {
        if (!config.title || config.title.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Election title is required');
        }
        if (config.title.length > 100) {
            throw new errorHandler_1.ValidationError('Election title cannot exceed 100 characters');
        }
        if (config.description && config.description.length > 500) {
            throw new errorHandler_1.ValidationError('Election description cannot exceed 500 characters');
        }
        if (!config.rolesToPing || config.rolesToPing.length === 0) {
            throw new errorHandler_1.ValidationError('At least one role to ping is required');
        }
        if (!config.eligibleVoterRoles || config.eligibleVoterRoles.length === 0) {
            throw new errorHandler_1.ValidationError('At least one eligible voter role is required');
        }
        if (!config.eligibleCandidateRoles || config.eligibleCandidateRoles.length === 0) {
            throw new errorHandler_1.ValidationError('At least one eligible candidate role is required');
        }
    }
    async calculateElectionStats(electionId, session) {
        const query = ElectionVote_1.default.aggregate([
            { $match: { electionId, status: 'ACTIVE' } },
            {
                $group: {
                    _id: null,
                    totalVotes: { $sum: 1 },
                    totalVoteWeight: { $sum: '$voteWeight' }
                }
            }
        ]);
        if (session) {
            query.session(session);
        }
        const result = await query.exec();
        const stats = result[0] || { totalVotes: 0, totalVoteWeight: 0 };
        const candidateCount = await ElectionCandidate_1.default.countDocuments({
            electionId,
            status: 'ACTIVE'
        }).session(session);
        return {
            totalVotes: stats.totalVotes,
            totalVoteWeight: stats.totalVoteWeight,
            totalCandidates: candidateCount,
            activeCandidates: candidateCount
        };
    }
    async recalculateCandidateVotes(electionId, session) {
        const pipeline = [
            { $match: { electionId, status: 'ACTIVE' } },
            {
                $group: {
                    _id: '$candidateId',
                    voteCount: { $sum: 1 },
                    voteWeight: { $sum: '$voteWeight' }
                }
            }
        ];
        const query = ElectionVote_1.default.aggregate(pipeline);
        if (session) {
            query.session(session);
        }
        const voteStats = await query.exec();
        await ElectionCandidate_1.default.updateMany({ electionId, status: 'ACTIVE' }, { voteCount: 0, voteWeight: 0 }).session(session);
        for (const stat of voteStats) {
            await ElectionCandidate_1.default.updateOne({ electionId, discordId: stat._id, status: 'ACTIVE' }, {
                voteCount: stat.voteCount,
                voteWeight: stat.voteWeight
            }).session(session);
        }
    }
}
exports.ElectionService = ElectionService;
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Object]),
    __metadata("design:returntype", Promise)
], ElectionService.prototype, "createElection", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], ElectionService.prototype, "addCandidate", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ElectionService.prototype, "removeCandidate", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], ElectionService.prototype, "castVote", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ElectionService.prototype, "endElection", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number]),
    __metadata("design:returntype", Promise)
], ElectionService.prototype, "archiveElection", null);
