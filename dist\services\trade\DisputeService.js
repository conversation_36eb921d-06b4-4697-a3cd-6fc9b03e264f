"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DisputeService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const discord_js_1 = require("discord.js");
const BaseService_1 = require("../base/BaseService");
const features_1 = require("../../config/features");
const errorHandler_1 = require("../../utils/errorHandler");
const constants_1 = require("../../config/constants");
const models_1 = require("../../models");
const EscrowManager_1 = require("./managers/EscrowManager");
const TradeNotificationManager_1 = require("./managers/TradeNotificationManager");
class DisputeService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'DisputeService';
        this.escrowManager = new EscrowManager_1.EscrowManager(app);
        this.notificationManager = new TradeNotificationManager_1.TradeNotificationManager(app);
    }
    async onInitialize() {
        this.logger.info('[DisputeService] Dispute service initialized');
        await this.escrowManager.initialize();
        await this.notificationManager.initialize();
    }
    async initiateDispute(params, client) {
        this.logOperation('Initiating trade dispute', params);
        const session = await mongoose_1.default.startSession();
        try {
            return await session.withTransaction(async () => {
                const trade = await models_1.Trade.findOne({ tradeId: params.tradeId }).session(session);
                if (!trade) {
                    throw new errorHandler_1.ValidationError('Trade not found');
                }
                this.validateDisputeInitiation(trade, params.initiatorId);
                const respondentId = trade.getOtherParty(params.initiatorId);
                if (!respondentId) {
                    throw new errorHandler_1.ValidationError('Could not determine other party in trade');
                }
                const disputeId = this.generateDisputeId();
                const evidenceDeadline = new Date();
                evidenceDeadline.setHours(evidenceDeadline.getHours() + 48);
                const disputeCase = await models_1.DisputeCase.create([{
                        disputeId,
                        tradeId: trade.tradeId,
                        guildId: trade.guildId,
                        initiatorId: params.initiatorId,
                        respondentId,
                        reason: params.reason,
                        description: params.description,
                        category: params.category,
                        evidenceDeadline,
                        status: 'EVIDENCE_COLLECTION',
                        priority: this.calculateDisputePriority(trade, params),
                        createdAt: new Date(),
                        lastUpdatedAt: new Date(),
                        adminNotes: [],
                        escalationLevel: 0,
                        tags: [params.category.toLowerCase()]
                    }], { session });
                trade.state = constants_1.TRADE.STATES.DISPUTED;
                trade.disputeId = disputeId;
                trade.disputedBy = params.initiatorId;
                trade.disputedAt = new Date();
                trade.disputeReason = params.reason;
                await trade.save({ session });
                await this.updateUserTradeStats(params.initiatorId, trade.guildId, 'DISPUTE_INITIATED', session);
                await this.updateUserTradeStats(respondentId, trade.guildId, 'DISPUTE_RECEIVED', session);
                this.logOperation('Dispute initiated successfully', {
                    disputeId,
                    tradeId: trade.tradeId,
                    initiatorId: params.initiatorId
                });
                if (client) {
                    setImmediate(async () => {
                        try {
                            await this.notificationManager.sendDisputeInitiated(trade, params.initiatorId, client);
                        }
                        catch (error) {
                            this.handleError(error, { operation: 'dispute_initiated_notification' });
                        }
                    });
                }
                return disputeCase[0];
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'initiate_dispute', params });
            throw error;
        }
        finally {
            await session.endSession();
        }
    }
    async resolveDispute(params, client) {
        this.logOperation('Resolving dispute', params);
        const session = await mongoose_1.default.startSession();
        try {
            return await session.withTransaction(async () => {
                const disputeCase = await models_1.DisputeCase.findOne({ disputeId: params.disputeId }).session(session);
                if (!disputeCase) {
                    throw new errorHandler_1.ValidationError('Dispute case not found');
                }
                const trade = await models_1.Trade.findOne({ tradeId: disputeCase.tradeId }).session(session);
                if (!trade) {
                    throw new errorHandler_1.ValidationError('Associated trade not found');
                }
                this.validateDisputeResolution(disputeCase, params.adminId);
                await this.executeResolution(trade, disputeCase, params, session);
                disputeCase.status = 'RESOLVED';
                disputeCase.resolution = params.resolution;
                disputeCase.resolutionDetails = params.resolutionDetails;
                disputeCase.resolutionAmount = params.resolutionAmount;
                disputeCase.resolvedAt = new Date();
                disputeCase.assignedAdminId = params.adminId;
                if (params.adminNotes) {
                    disputeCase.addAdminNote(params.adminNotes, params.adminId);
                }
                await disputeCase.save({ session });
                trade.state = constants_1.TRADE.STATES.COMPLETED;
                trade.completedAt = new Date();
                await trade.save({ session });
                await this.updateUserTradeStats(disputeCase.initiatorId, trade.guildId, 'DISPUTE_RESOLVED', session, { wasSuccessful: params.resolution === 'FAVOR_INITIATOR' });
                await this.updateUserTradeStats(disputeCase.respondentId, trade.guildId, 'DISPUTE_RESOLVED', session, { wasSuccessful: params.resolution === 'FAVOR_RESPONDENT' });
                this.logOperation('Dispute resolved successfully', {
                    disputeId: params.disputeId,
                    resolution: params.resolution,
                    adminId: params.adminId
                });
                if (client) {
                    setImmediate(async () => {
                        try {
                            await this.notificationManager.sendDisputeResolved(trade, params.resolutionDetails, client);
                        }
                        catch (error) {
                            this.handleError(error, { operation: 'dispute_resolved_notification' });
                        }
                    });
                }
                return disputeCase;
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'resolve_dispute', params });
            throw error;
        }
        finally {
            await session.endSession();
        }
    }
    async getDispute(disputeId) {
        try {
            return await models_1.DisputeCase.findOne({ disputeId }).lean();
        }
        catch (error) {
            this.handleError(error, { operation: 'get_dispute', disputeId });
            throw error;
        }
    }
    async getActiveDisputes(guildId, limit = 20) {
        try {
            const query = {
                status: { $in: ['OPEN', 'EVIDENCE_COLLECTION', 'UNDER_REVIEW'] }
            };
            if (guildId) {
                query.guildId = guildId;
            }
            return await models_1.DisputeCase.find(query)
                .sort({ priority: -1, createdAt: -1 })
                .limit(limit)
                .lean();
        }
        catch (error) {
            this.handleError(error, { operation: 'get_active_disputes', guildId });
            throw error;
        }
    }
    async addEvidence(disputeId, userId, evidence) {
        this.logOperation('Adding evidence to dispute', { disputeId, userId, evidenceCount: evidence.length });
        try {
            const disputeCase = await models_1.DisputeCase.findOne({ disputeId });
            if (!disputeCase) {
                throw new errorHandler_1.ValidationError('Dispute case not found');
            }
            if (userId !== disputeCase.initiatorId && userId !== disputeCase.respondentId) {
                throw new errorHandler_1.ValidationError('You are not a party to this dispute');
            }
            if (disputeCase.isEvidenceDeadlinePassed()) {
                throw new errorHandler_1.ValidationError('Evidence submission deadline has passed');
            }
            if (userId === disputeCase.initiatorId) {
                disputeCase.initiatorEvidence.push(...evidence);
            }
            else {
                disputeCase.respondentEvidence.push(...evidence);
            }
            await disputeCase.save();
            this.logOperation('Evidence added successfully', {
                disputeId,
                userId,
                evidenceCount: evidence.length
            });
            return disputeCase;
        }
        catch (error) {
            this.handleError(error, { operation: 'add_evidence', disputeId, userId });
            throw error;
        }
    }
    validateDisputeInitiation(trade, initiatorId) {
        if (trade.state !== constants_1.TRADE.STATES.ACTIVE) {
            throw new errorHandler_1.ValidationError('Disputes can only be initiated for active trades');
        }
        if (!trade.involvesUser(initiatorId)) {
            throw new errorHandler_1.ValidationError('You are not a party to this trade');
        }
        if (trade.disputeId) {
            throw new errorHandler_1.ValidationError('This trade already has an active dispute');
        }
    }
    validateDisputeResolution(disputeCase, adminId) {
        if (!disputeCase.isActive()) {
            throw new errorHandler_1.ValidationError('Dispute is not in an active state');
        }
    }
    async executeResolution(trade, disputeCase, params, session) {
        if (!trade.escrowLocked || trade.escrowAmount <= 0) {
            return;
        }
        switch (params.resolution) {
            case 'FAVOR_INITIATOR':
                if (trade.buyerId === disputeCase.initiatorId) {
                    await this.escrowManager.refundEscrow(trade, 'Dispute resolved in favor of initiator', session);
                }
                else {
                    await this.escrowManager.releaseEscrow(trade, 'Dispute resolved in favor of initiator', session);
                }
                break;
            case 'FAVOR_RESPONDENT':
                if (trade.sellerId === disputeCase.respondentId) {
                    await this.escrowManager.releaseEscrow(trade, 'Dispute resolved in favor of respondent', session);
                }
                else {
                    await this.escrowManager.refundEscrow(trade, 'Dispute resolved in favor of respondent', session);
                }
                break;
            case 'SPLIT_ESCROW':
                const halfAmount = Math.floor(trade.escrowAmount / 2);
                const remainder = trade.escrowAmount - halfAmount;
                await this.escrowManager.splitEscrow(trade, trade.sellerId === disputeCase.initiatorId ? halfAmount : remainder, trade.buyerId === disputeCase.initiatorId ? halfAmount : remainder, 'Dispute resolved with split escrow', session);
                break;
            case 'FULL_REFUND':
                await this.escrowManager.refundEscrow(trade, 'Dispute resolved with full refund', session);
                break;
            case 'CUSTOM':
                if (params.resolutionAmount === undefined) {
                    throw new errorHandler_1.ValidationError('Custom resolution requires resolution amount');
                }
                const sellerAmount = params.resolutionAmount;
                const buyerAmount = trade.escrowAmount - sellerAmount;
                await this.escrowManager.splitEscrow(trade, sellerAmount, buyerAmount, 'Dispute resolved with custom amount', session);
                break;
        }
    }
    calculateDisputePriority(trade, params) {
        if (trade.amount >= 10000)
            return 'HIGH';
        if (trade.amount >= 5000)
            return 'MEDIUM';
        if (params.category === 'PAYMENT_ISSUE')
            return 'HIGH';
        return 'LOW';
    }
    async updateUserTradeStats(discordId, guildId, action, session, data) {
        try {
            const userStats = await models_1.UserTradeStats.findOne({ discordId, guildId }).session(session);
            if (userStats) {
                switch (action) {
                    case 'DISPUTE_INITIATED':
                    case 'DISPUTE_RECEIVED':
                        break;
                    case 'DISPUTE_RESOLVED':
                        if (data?.wasSuccessful) {
                            userStats.reputationScore = Math.min(100, userStats.reputationScore + 2);
                        }
                        else {
                            userStats.reputationScore = Math.max(0, userStats.reputationScore - 5);
                        }
                        break;
                }
                await userStats.save({ session });
            }
        }
        catch (error) {
            this.logger.warn('Failed to update user trade stats', { error, discordId, action });
        }
    }
    generateDisputeId() {
        return `dispute_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.DisputeService = DisputeService;
__decorate([
    (0, features_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DisputeService.prototype, "onInitialize", null);
__decorate([
    (0, features_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, discord_js_1.Client]),
    __metadata("design:returntype", Promise)
], DisputeService.prototype, "initiateDispute", null);
__decorate([
    (0, features_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, discord_js_1.Client]),
    __metadata("design:returntype", Promise)
], DisputeService.prototype, "resolveDispute", null);
__decorate([
    (0, features_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DisputeService.prototype, "getDispute", null);
__decorate([
    (0, features_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], DisputeService.prototype, "getActiveDisputes", null);
__decorate([
    (0, features_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Array]),
    __metadata("design:returntype", Promise)
], DisputeService.prototype, "addEvidence", null);
