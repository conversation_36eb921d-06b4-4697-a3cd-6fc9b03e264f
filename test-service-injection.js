/**
 * Service Injection Test
 * Tests that the ElectionService is properly injected into ElectionsCommand
 */

require('dotenv').config();
const mongoose = require('mongoose');

async function testServiceInjection() {
  console.log('🔧 Testing Service Injection...\n');

  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to database');

    // Import the compiled modules
    const { ElectionService } = require('./dist/services/election/ElectionService');
    const { ElectionsCommand } = require('./dist/commands/election/ElectionsCommand');

    // Mock application context
    const mockApp = {
      logger: {
        info: (msg, data) => console.log(`[INFO] ${msg}`, data || ''),
        error: (msg, data) => console.log(`[ERROR] ${msg}`, data || ''),
        warn: (msg, data) => console.log(`[WARN] ${msg}`, data || ''),
        debug: (msg, data) => console.log(`[DEBUG] ${msg}`, data || '')
      },
      getService: (serviceName) => {
        if (serviceName === 'ElectionService') {
          console.log('📦 Application context providing ElectionService');
          return new ElectionService(mockApp);
        }
        return null;
      },
      isFeatureEnabled: () => true
    };

    console.log('\n🧪 Test 1: Creating ElectionsCommand...');
    const electionsCommand = new ElectionsCommand();
    console.log('✅ ElectionsCommand created');

    console.log('\n🧪 Test 2: Initializing with application context...');
    try {
      await electionsCommand.initialize(mockApp);
      console.log('✅ ElectionsCommand initialized successfully');
    } catch (error) {
      console.error('❌ Initialization failed:', error.message);
      throw error;
    }

    console.log('\n🧪 Test 3: Checking service availability...');
    // Access the private electionService property to verify it's set
    const hasService = electionsCommand.electionService !== undefined;
    if (hasService) {
      console.log('✅ ElectionService is properly injected');
    } else {
      console.error('❌ ElectionService is not available');
      throw new Error('ElectionService injection failed');
    }

    console.log('\n🧪 Test 4: Testing alternative injection method...');
    const electionsCommand2 = new ElectionsCommand();
    const electionService = new ElectionService(mockApp);
    await electionService.onInitialize();
    
    electionsCommand2.setElectionService(electionService);
    console.log('✅ Alternative injection method works');

    console.log('\n🧪 Test 5: Simulating command execution context...');
    
    // Mock Discord interaction
    const mockInteraction = {
      options: {
        getString: (name) => {
          const mockData = {
            'title': 'Test Election',
            'roles_to_ping': '<@&123456789012345678>',
            'eligible_voter_roles': '<@&234567890123456789>',
            'eligible_candidate_roles': '<@&345678901234567890>'
          };
          return mockData[name] || null;
        }
      },
      guild: {
        id: '987654321098765432',
        name: 'Test Guild'
      },
      channel: {
        id: '444444444444444444'
      },
      user: {
        id: '567890123456789012'
      },
      member: {
        permissions: {
          has: () => true
        }
      },
      reply: async (options) => {
        console.log('📤 Mock reply sent:', {
          embeds: options.embeds ? options.embeds.length : 0,
          components: options.components ? options.components.length : 0
        });
        return { id: 'mock-message-id' };
      }
    };

    const mockContext = {
      interaction: mockInteraction,
      guild: mockInteraction.guild,
      user: mockInteraction.user,
      member: mockInteraction.member
    };

    console.log('🎯 Attempting to execute command with mock context...');
    
    try {
      // This should work now that the service is injected
      await electionsCommand.execute(mockContext);
      console.log('✅ Command executed successfully (mock)');
    } catch (error) {
      if (error.message.includes('Election service is not available')) {
        console.error('❌ Service injection failed - command cannot access ElectionService');
        throw error;
      } else {
        // Other errors are expected in mock environment (database, Discord API, etc.)
        console.log('⚠️ Command execution failed with expected error:', error.message);
        console.log('✅ Service injection is working (error is not service-related)');
      }
    }

    console.log('\n🎉 Service injection test completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ ElectionsCommand can be created');
    console.log('✅ ElectionService can be injected via initialize()');
    console.log('✅ ElectionService can be injected via setElectionService()');
    console.log('✅ Command can access the injected service');
    console.log('✅ Ready for Discord testing');

  } catch (error) {
    console.error('\n❌ Service injection test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from database');
  }
}

// Run the test
testServiceInjection().catch(console.error);
