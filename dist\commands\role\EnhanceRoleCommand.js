"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhanceRoleCommand = void 0;
const discord_js_1 = require("discord.js");
const BaseCommand_1 = require("../base/BaseCommand");
const User_1 = require("../../models/User");
const errorHandler_1 = require("../../utils/errorHandler");
const embedBuilder_1 = require("../../utils/embedBuilder");
class EnhanceRoleCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'enhancerole',
            description: 'Assign a prefix to all users with a specified role',
            category: BaseCommand_1.CommandCategory.ROLE,
            adminOnly: false,
            guildOnly: true,
            cooldown: 10,
            requiredPermissions: ['ManageNicknames'],
        });
    }
    customizeCommand(command) {
        command
            .addRoleOption(option => option
            .setName('role')
            .setDescription('Discord role to target')
            .setRequired(true))
            .addStringOption(option => option
            .setName('prefix')
            .setDescription('Text/emoji prefix to prepend (max 10 characters)')
            .setRequired(true)
            .setMaxLength(10));
    }
    async executeCommand(context) {
        const { interaction, guild, member } = context;
        await interaction.deferReply();
        if (!guild || !member) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        const botMember = await guild.members.fetch(interaction.client.user.id);
        if (!botMember.permissions.has(discord_js_1.PermissionFlagsBits.ManageNicknames)) {
            throw new errorHandler_1.PermissionError('I need the "Manage Nicknames" permission to use this command.');
        }
        const targetRole = interaction.options.getRole('role', true);
        const prefix = interaction.options.getString('prefix', true).trim();
        if (prefix.length === 0) {
            throw new errorHandler_1.ValidationError('Prefix cannot be empty.');
        }
        if (prefix.length > 10) {
            throw new errorHandler_1.ValidationError('Prefix cannot exceed 10 characters.');
        }
        const guildRole = await guild.roles.fetch(targetRole.id);
        if (!guildRole) {
            throw new errorHandler_1.ValidationError('The specified role does not exist in this server.');
        }
        if (guildRole.position >= botMember.roles.highest.position) {
            throw new errorHandler_1.PermissionError('I cannot manage this role due to role hierarchy restrictions.');
        }
        try {
            const membersWithRole = guildRole.members;
            if (membersWithRole.size === 0) {
                const embed = (0, embedBuilder_1.createErrorEmbed)('No Members Found', `No members have the role ${guildRole.name}.`);
                await interaction.editReply({ embeds: [embed] });
                return;
            }
            let updatedCount = 0;
            let skippedCount = 0;
            const errors = [];
            const existingRolePrefixes = await User_1.RolePrefix.find({ guildId: guild.id });
            for (const [, guildMember] of membersWithRole) {
                try {
                    const result = await this.updateMemberNickname(guildMember, prefix, botMember, existingRolePrefixes);
                    if (result.updated) {
                        updatedCount++;
                    }
                    else {
                        skippedCount++;
                    }
                }
                catch (error) {
                    errors.push(`${guildMember.displayName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                    skippedCount++;
                }
            }
            try {
                await User_1.RolePrefix.findOneAndUpdate({ guildId: guild.id, roleId: targetRole.id }, {
                    guildId: guild.id,
                    roleId: targetRole.id,
                    prefix: prefix,
                    createdAt: new Date()
                }, { upsert: true, new: true });
            }
            catch (error) {
                this.logger.error('Failed to store role-prefix mapping', { error, guildId: guild.id, roleId: targetRole.id });
            }
            const guildId = interaction.guild.id;
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, '✅ Role Enhancement Complete');
            embed.setDescription(`Updated **${updatedCount}** out of **${membersWithRole.size}** members with role **${guildRole.name}**`);
            if (skippedCount > 0) {
                embed.addFields({
                    name: '⚠️ Skipped Members',
                    value: `${skippedCount} members were skipped (already had prefix or permission issues)`,
                    inline: false
                });
            }
            if (errors.length > 0 && errors.length <= 5) {
                embed.addFields({
                    name: '❌ Errors',
                    value: errors.slice(0, 5).join('\n'),
                    inline: false
                });
            }
            else if (errors.length > 5) {
                embed.addFields({
                    name: '❌ Errors',
                    value: `${errors.length} errors occurred. Check logs for details.`,
                    inline: false
                });
            }
            embed.addFields({
                name: '🏷️ Prefix Applied',
                value: `\`${prefix}\``,
                inline: true
            });
            await interaction.editReply({ embeds: [embed] });
        }
        catch (error) {
            this.logger.error('Failed to enhance role', { error, guildId: guild.id, roleId: targetRole.id });
            throw new errorHandler_1.DatabaseError(`Failed to enhance role: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async updateMemberNickname(member, prefix, botMember, existingRolePrefixes) {
        if (member.roles.highest.position >= botMember.roles.highest.position && member.id !== member.guild.ownerId) {
            return { updated: false, reason: 'Higher role hierarchy' };
        }
        const currentName = member.nickname || member.user.username;
        let baseName = currentName;
        let prefixRemoved = true;
        while (prefixRemoved) {
            prefixRemoved = false;
            for (const rp of existingRolePrefixes) {
                if (baseName.startsWith(rp.prefix)) {
                    baseName = baseName.substring(rp.prefix.length);
                    prefixRemoved = true;
                    break;
                }
            }
        }
        if (baseName.trim() === '') {
            baseName = member.user.username;
        }
        const expectedNickname = `${prefix}${baseName}`;
        if (currentName === expectedNickname) {
            return { updated: false, reason: 'Prefix already correctly applied' };
        }
        let newNickname = expectedNickname;
        if (newNickname.length > 32) {
            const maxNameLength = 32 - prefix.length;
            const truncatedName = baseName.substring(0, maxNameLength);
            newNickname = `${prefix}${truncatedName}`;
        }
        try {
            await member.setNickname(newNickname, `Role enhancement: ${prefix}`);
            return { updated: true };
        }
        catch (error) {
            throw new Error(`Failed to update nickname: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
exports.EnhanceRoleCommand = EnhanceRoleCommand;
