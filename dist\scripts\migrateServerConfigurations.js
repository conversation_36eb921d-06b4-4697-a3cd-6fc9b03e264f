"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.migrateServerConfigurations = migrateServerConfigurations;
exports.verifyMigration = verifyMigration;
const mongoose_1 = __importDefault(require("mongoose"));
const discord_js_1 = require("discord.js");
const serverConfigurationService_1 = __importDefault(require("../services/serverConfigurationService"));
const models_1 = require("../models");
async function migrateServerConfigurations() {
    console.log('🔄 Starting server configuration migration...');
    try {
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/economy-bot';
        await mongoose_1.default.connect(mongoUri);
        console.log('✅ Connected to MongoDB');
        const client = new discord_js_1.Client({
            intents: [discord_js_1.GatewayIntentBits.Guilds]
        });
        await client.login(process.env.DISCORD_TOKEN);
        console.log('✅ Connected to Discord');
        const existingGuilds = await models_1.User.distinct('guildId');
        console.log(`📊 Found ${existingGuilds.length} existing guilds in database`);
        const discordGuilds = client.guilds.cache.map(guild => guild.id);
        console.log(`📊 Found ${discordGuilds.length} guilds from Discord client`);
        const allGuildIds = [...new Set([...existingGuilds, ...discordGuilds])];
        console.log(`📊 Total unique guilds to migrate: ${allGuildIds.length}`);
        let migratedCount = 0;
        let skippedCount = 0;
        for (const guildId of allGuildIds) {
            try {
                const existingConfig = await serverConfigurationService_1.default.getServerConfig(guildId);
                if (existingConfig && existingConfig.createdAt) {
                    skippedCount++;
                    console.log(`⏭️  Skipped guild ${guildId} (configuration already exists)`);
                    continue;
                }
                await serverConfigurationService_1.default.updateServerConfig(guildId, {
                    nationName: 'Phalanx Order',
                    coinName: 'Phalanx Loyalty Coins',
                    coinSymbol: 'PLC',
                    embedColor: '#dd7d00'
                });
                migratedCount++;
                console.log(`✅ Migrated guild ${guildId} with default configuration`);
            }
            catch (error) {
                console.error(`❌ Failed to migrate guild ${guildId}:`, error);
            }
        }
        console.log('\n📈 Migration Summary:');
        console.log(`✅ Successfully migrated: ${migratedCount} guilds`);
        console.log(`⏭️  Skipped (already configured): ${skippedCount} guilds`);
        console.log(`❌ Failed: ${allGuildIds.length - migratedCount - skippedCount} guilds`);
        client.destroy();
        await mongoose_1.default.disconnect();
        console.log('\n🎉 Server configuration migration completed!');
        console.log('\n📝 Next steps:');
        console.log('1. Deploy the updated bot with configurable system');
        console.log('2. Inform server administrators about the new /setup command');
        console.log('3. Monitor logs for any issues with server-specific configurations');
    }
    catch (error) {
        console.error('💥 Migration failed:', error);
        process.exit(1);
    }
}
async function verifyMigration() {
    console.log('\n🔍 Verifying migration...');
    try {
        const allConfigs = await serverConfigurationService_1.default.getAllConfigurations();
        console.log(`📊 Total configurations in database: ${allConfigs.length}`);
        if (allConfigs.length > 0) {
            const samplesToCheck = Math.min(3, allConfigs.length);
            console.log(`🔍 Checking ${samplesToCheck} sample configurations:`);
            for (let i = 0; i < samplesToCheck; i++) {
                const config = allConfigs[i];
                console.log(`  Guild ${config.guildId}:`);
                console.log(`    Nation: ${config.nationName}`);
                console.log(`    Coin: ${config.coinName} (${config.coinSymbol})`);
                console.log(`    Color: ${config.embedColor}`);
                console.log(`    Created: ${config.createdAt}`);
            }
            console.log('✅ Migration verification completed successfully');
        }
        else {
            console.log('⚠️  No configurations found - migration may have failed');
        }
    }
    catch (error) {
        console.error('❌ Migration verification failed:', error);
    }
}
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    switch (command) {
        case 'migrate':
            await migrateServerConfigurations();
            break;
        case 'verify':
            const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/economy-bot';
            await mongoose_1.default.connect(mongoUri);
            await verifyMigration();
            await mongoose_1.default.disconnect();
            break;
        default:
            console.log('Usage:');
            console.log('  npm run migrate:server-configs migrate  - Run the migration');
            console.log('  npm run migrate:server-configs verify   - Verify the migration');
            break;
    }
}
if (require.main === module) {
    main().catch(error => {
        console.error('Script failed:', error);
        process.exit(1);
    });
}
