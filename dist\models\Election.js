"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const electionSchema = new mongoose_1.Schema({
    electionId: {
        type: String,
        required: [true, 'Election ID is required'],
        unique: true,
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    channelId: {
        type: String,
        required: [true, 'Channel ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Channel ID must be a valid Discord snowflake'
        }
    },
    messageId: {
        type: String,
        required: false,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Message ID must be a valid Discord snowflake'
        }
    },
    title: {
        type: String,
        required: [true, 'Election title is required'],
        maxlength: [200, 'Election title cannot exceed 200 characters'],
        minlength: [1, 'Election title cannot be empty']
    },
    description: {
        type: String,
        maxlength: [500, 'Election description cannot exceed 500 characters'],
        trim: true
    },
    eligibleVoterRoles: [{
            type: String,
            required: true,
            validate: {
                validator: function (v) {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }],
    eligibleCandidateRoles: [{
            type: String,
            required: true,
            validate: {
                validator: function (v) {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }],
    rolesToPing: [{
            type: String,
            required: true,
            validate: {
                validator: function (v) {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }],
    allowMultipleVotes: {
        type: Boolean,
        default: true
    },
    showVoteWeights: {
        type: Boolean,
        default: true
    },
    status: {
        type: String,
        enum: ['ACTIVE', 'ENDED', 'CANCELLED', 'ARCHIVED'],
        required: [true, 'Election status is required'],
        default: 'ACTIVE',
        index: true
    },
    totalVotes: {
        type: Number,
        default: 0,
        min: [0, 'Total votes cannot be negative']
    },
    totalVoteWeight: {
        type: Number,
        default: 0,
        min: [0, 'Total vote weight cannot be negative']
    },
    totalCandidates: {
        type: Number,
        default: 0,
        min: [0, 'Total candidates cannot be negative']
    },
    createdBy: {
        type: String,
        required: [true, 'Creator ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Creator ID must be a valid Discord snowflake'
        },
        index: true
    },
    endedBy: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Ended by ID must be a valid Discord snowflake'
        }
    },
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    endedAt: {
        type: Date,
        index: true
    },
    archivedAt: {
        type: Date,
        index: true
    }
}, {
    timestamps: false
});
electionSchema.index({ guildId: 1, status: 1 });
electionSchema.index({ guildId: 1, createdAt: -1 });
electionSchema.index({ status: 1, createdAt: -1 });
electionSchema.pre('validate', function () {
    if (this.eligibleVoterRoles && this.eligibleVoterRoles.length === 0) {
        this.invalidate('eligibleVoterRoles', 'At least one eligible voter role is required');
    }
    if (this.eligibleCandidateRoles && this.eligibleCandidateRoles.length === 0) {
        this.invalidate('eligibleCandidateRoles', 'At least one eligible candidate role is required');
    }
    if (this.rolesToPing && this.rolesToPing.length === 0) {
        this.invalidate('rolesToPing', 'At least one role to ping is required');
    }
});
const Election = (0, mongoose_1.model)('Election', electionSchema);
exports.default = Election;
