"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.data = void 0;
exports.execute = execute;
const discord_js_1 = require("discord.js");
const serverConfigurationService_1 = __importDefault(require("../services/serverConfigurationService"));
const embedBuilder_1 = require("../utils/embedBuilder");
exports.data = new discord_js_1.SlashCommandBuilder()
    .setName('setup')
    .setDescription('Configure your server\'s economy system settings')
    .addStringOption(option => option.setName('nationname')
    .setDescription('The name of your nation/organization (e.g., "Phalanx Order")')
    .setRequired(true)
    .setMaxLength(100))
    .addStringOption(option => option.setName('coinname')
    .setDescription('The name of your currency (e.g., "Phalanx Loyalty Coins")')
    .setRequired(true)
    .setMaxLength(50))
    .addStringOption(option => option.setName('coinsymbol')
    .setDescription('The symbol/abbreviation for your currency (e.g., "PLC")')
    .setRequired(false)
    .setMaxLength(10))
    .addStringOption(option => option.setName('embedcolor')
    .setDescription('Embed color: #FF0000, FF0000, #F00, F00, red, BLUE, Purple (case-insensitive)')
    .setRequired(false))
    .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator);
async function execute(interaction) {
    if (!interaction.guild) {
        await interaction.reply({
            embeds: [(0, embedBuilder_1.createErrorEmbed)('Server Only', 'This command can only be used in a server.')],
            ephemeral: true
        });
        return;
    }
    if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
        await interaction.reply({
            embeds: [(0, embedBuilder_1.createErrorEmbed)('Permission Denied', 'You need Administrator permissions to use this command.')],
            ephemeral: true
        });
        return;
    }
    await interaction.deferReply();
    try {
        const nationName = interaction.options.get('nationname')?.value;
        const coinName = interaction.options.get('coinname')?.value;
        const coinSymbol = interaction.options.get('coinsymbol')?.value;
        const embedColor = interaction.options.get('embedcolor')?.value;
        if (!nationName.trim()) {
            await interaction.editReply({
                embeds: [(0, embedBuilder_1.createErrorEmbed)('Invalid Input', 'Nation name cannot be empty.')]
            });
            return;
        }
        if (!coinName.trim()) {
            await interaction.editReply({
                embeds: [(0, embedBuilder_1.createErrorEmbed)('Invalid Input', 'Coin name cannot be empty.')]
            });
            return;
        }
        const updates = {
            nationName: nationName.trim(),
            coinName: coinName.trim()
        };
        if (coinSymbol) {
            updates.coinSymbol = coinSymbol.trim().toUpperCase();
        }
        else {
            const words = coinName.trim().split(/\s+/);
            updates.coinSymbol = words
                .map(word => word.charAt(0).toUpperCase())
                .join('')
                .substring(0, 5);
        }
        if (embedColor) {
            updates.embedColor = embedColor.trim();
        }
        const config = await serverConfigurationService_1.default.updateServerConfig(interaction.guild.id, updates);
        const successEmbed = new discord_js_1.EmbedBuilder()
            .setTitle(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Server Configuration Updated`)
            .setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Your server's economy system has been configured!**\n\n` +
            `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} **Configuration Details:**\n` +
            `• **Nation Name:** ${config.nationName}\n` +
            `• **Currency Name:** ${config.coinName}\n` +
            `• **Currency Symbol:** ${config.coinSymbol}\n` +
            `• **Embed Color:** ${config.embedColor}\n\n` +
            `${embedBuilder_1.EMOJIS.ECONOMY.COINS} All economy commands will now use these settings!\n` +
            `${embedBuilder_1.EMOJIS.ADMIN.INFO} Use \`/setup\` again to change these settings anytime.`)
            .setColor(config.embedColor)
            .setFooter({ text: `${config.nationName} Economy System` })
            .setTimestamp();
        successEmbed.addFields({
            name: `${embedBuilder_1.EMOJIS.ECONOMY.MONEY} Preview`,
            value: `Here's how your currency will appear:\n` +
                `• **1,000 ${config.coinSymbol}** (symbol format)\n` +
                `• **1,000 ${config.coinName}** (full name format)`,
            inline: false
        });
        if (embedColor) {
            successEmbed.addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Color Format Help`,
                value: `Supported color formats:\n` +
                    `• **Hex codes:** #FF0000, FF0000, #F00, F00\n` +
                    `• **Color names:** red, blue, green, purple, orange, etc.\n` +
                    `• **Case-insensitive:** RED, Blue, PURPLE all work!`,
                inline: false
            });
        }
        await interaction.editReply({
            embeds: [successEmbed]
        });
        console.log(`[Setup] Server ${interaction.guild.name} (${interaction.guild.id}) configured:`, {
            nationName: config.nationName,
            coinName: config.coinName,
            coinSymbol: config.coinSymbol,
            embedColor: config.embedColor,
            configuredBy: interaction.user.tag
        });
    }
    catch (error) {
        console.error('[Setup] Error updating server configuration:', error);
        let errorMessage = 'An unexpected error occurred while updating the configuration.';
        if (error.message.includes('Invalid color')) {
            errorMessage = error.message;
        }
        else if (error.name === 'ValidationError') {
            errorMessage = 'Invalid input provided. Please check your values and try again.';
        }
        await interaction.editReply({
            embeds: [(0, embedBuilder_1.createErrorEmbed)('Configuration Error', errorMessage)]
        });
    }
}
exports.default = {
    data: exports.data,
    execute
};
