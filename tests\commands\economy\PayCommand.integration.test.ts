/**
 * PayCommand Integration Tests
 * Tests the complete payment flow with real database operations
 */

import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { PayCommand } from '../../../src/commands/economy/PayCommand';
import { CommandContext } from '../../../src/core/interfaces';
import User from '../../../src/models/User';
import Transaction from '../../../src/models/Transaction';

// Mock only the Discord-specific parts
jest.mock('../../../src/utils/embedBuilder', () => ({
  createServerSuccessEmbed: jest.fn().mockResolvedValue({
    setDescription: jest.fn().mockReturnThis(),
    addFields: jest.fn().mockReturnThis()
  }),
  addUserInfo: jest.fn(),
  formatServerCoins: jest.fn().mockImplementation((guildId, amount) => `${amount} coins`),
  EMOJIS: {
    ECONOMY: { TRANSFER: '💸', BALANCE: '💰', COINS: '🪙' },
    ACTIONS: { SENDER: '👤', TARGET: '🎯' }
  }
}));

describe('PayCommand Integration Tests', () => {
  let mongoServer: MongoMemoryServer;
  let payCommand: PayCommand;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await User.deleteMany({});
    await Transaction.deleteMany({});
    payCommand = new PayCommand();
    jest.clearAllMocks();
  });

  describe('Multi-Guild Isolation', () => {
    test('should isolate transactions between different guilds', async () => {
      const guildA = '111111111111111111';
      const guildB = '222222222222222222';
      const senderId = '333333333333333333';
      const recipientId = '444444444444444444';

      // Create users in both guilds
      await User.create([
        { discordId: senderId, guildId: guildA, balance: 1000 },
        { discordId: senderId, guildId: guildB, balance: 500 },
        { discordId: recipientId, guildId: guildA, balance: 0 },
        { discordId: recipientId, guildId: guildB, balance: 0 }
      ]);

      // Create mock interaction for Guild A
      const mockContext: CommandContext = {
        interaction: {
          user: { id: senderId, username: 'sender', displayName: 'Sender', tag: 'sender#1234' },
          guild: { 
            id: guildA,
            members: {
              fetch: jest.fn().mockResolvedValue({ id: recipientId })
            }
          },
          options: {
            getUser: jest.fn().mockReturnValue({
              id: recipientId,
              username: 'recipient',
              displayName: 'Recipient',
              tag: 'recipient#5678',
              bot: false,
              send: jest.fn().mockResolvedValue(undefined)
            }),
            getInteger: jest.fn().mockReturnValue(200)
          },
          reply: jest.fn().mockResolvedValue(undefined)
        }
      } as any;

      // Execute payment in Guild A
      await payCommand.execute(mockContext);

      // Verify Guild A balances
      const senderGuildA = await User.findOne({ discordId: senderId, guildId: guildA });
      const recipientGuildA = await User.findOne({ discordId: recipientId, guildId: guildA });
      expect(senderGuildA?.balance).toBe(800);
      expect(recipientGuildA?.balance).toBe(200);

      // Verify Guild B balances are unchanged
      const senderGuildB = await User.findOne({ discordId: senderId, guildId: guildB });
      const recipientGuildB = await User.findOne({ discordId: recipientId, guildId: guildB });
      expect(senderGuildB?.balance).toBe(500);
      expect(recipientGuildB?.balance).toBe(0);
    });
  });

  describe('Concurrent Transaction Handling', () => {
    test('should handle concurrent payments correctly', async () => {
      const guildId = '111111111111111111';
      const senderId = '222222222222222222';
      const recipient1Id = '333333333333333333';
      const recipient2Id = '444444444444444444';

      // Create sender with balance
      await User.create({
        discordId: senderId,
        guildId: guildId,
        balance: 1000
      });

      // Create two concurrent payment contexts
      const createMockContext = (recipientId: string, amount: number): CommandContext => ({
        interaction: {
          user: { id: senderId, username: 'sender', displayName: 'Sender', tag: 'sender#1234' },
          guild: { 
            id: guildId,
            members: {
              fetch: jest.fn().mockResolvedValue({ id: recipientId })
            }
          },
          options: {
            getUser: jest.fn().mockReturnValue({
              id: recipientId,
              username: `recipient${recipientId.slice(-1)}`,
              displayName: `Recipient ${recipientId.slice(-1)}`,
              tag: `recipient${recipientId.slice(-1)}#1234`,
              bot: false,
              send: jest.fn().mockResolvedValue(undefined)
            }),
            getInteger: jest.fn().mockReturnValue(amount)
          },
          reply: jest.fn().mockResolvedValue(undefined)
        }
      } as any);

      const context1 = createMockContext(recipient1Id, 300);
      const context2 = createMockContext(recipient2Id, 400);

      // Execute both payments concurrently
      const command1 = new PayCommand();
      const command2 = new PayCommand();

      await Promise.all([
        command1.execute(context1),
        command2.execute(context2)
      ]);

      // Verify final balances
      const sender = await User.findOne({ discordId: senderId, guildId: guildId });
      const recipient1 = await User.findOne({ discordId: recipient1Id, guildId: guildId });
      const recipient2 = await User.findOne({ discordId: recipient2Id, guildId: guildId });

      expect(sender?.balance).toBe(300); // 1000 - 300 - 400
      expect(recipient1?.balance).toBe(300);
      expect(recipient2?.balance).toBe(400);

      // Verify transaction records
      const transactions = await Transaction.find({ guildId: guildId, type: 'pay' });
      expect(transactions).toHaveLength(4); // 2 debit + 2 credit transactions
    });
  });

  describe('Large Transaction Volumes', () => {
    test('should handle multiple small transactions efficiently', async () => {
      const guildId = '111111111111111111';
      const senderId = '222222222222222222';
      const recipientId = '333333333333333333';

      // Create sender with large balance
      await User.create({
        discordId: senderId,
        guildId: guildId,
        balance: 10000
      });

      const createMockContext = (amount: number): CommandContext => ({
        interaction: {
          user: { id: senderId, username: 'sender', displayName: 'Sender', tag: 'sender#1234' },
          guild: { 
            id: guildId,
            members: {
              fetch: jest.fn().mockResolvedValue({ id: recipientId })
            }
          },
          options: {
            getUser: jest.fn().mockReturnValue({
              id: recipientId,
              username: 'recipient',
              displayName: 'Recipient',
              tag: 'recipient#1234',
              bot: false,
              send: jest.fn().mockResolvedValue(undefined)
            }),
            getInteger: jest.fn().mockReturnValue(amount)
          },
          reply: jest.fn().mockResolvedValue(undefined)
        }
      } as any);

      // Execute 10 payments of 100 coins each
      const paymentPromises = [];
      for (let i = 0; i < 10; i++) {
        const command = new PayCommand();
        paymentPromises.push(command.execute(createMockContext(100)));
      }

      await Promise.all(paymentPromises);

      // Verify final balances
      const sender = await User.findOne({ discordId: senderId, guildId: guildId });
      const recipient = await User.findOne({ discordId: recipientId, guildId: guildId });

      expect(sender?.balance).toBe(9000); // 10000 - (10 * 100)
      expect(recipient?.balance).toBe(1000); // 10 * 100

      // Verify transaction count
      const transactions = await Transaction.find({ guildId: guildId, type: 'pay' });
      expect(transactions).toHaveLength(20); // 10 debit + 10 credit transactions
    });
  });

  describe('Error Recovery', () => {
    test('should maintain data consistency on partial failures', async () => {
      const guildId = '111111111111111111';
      const senderId = '222222222222222222';
      const recipientId = '333333333333333333';

      // Create sender with balance
      await User.create({
        discordId: senderId,
        guildId: guildId,
        balance: 1000
      });

      const mockContext: CommandContext = {
        interaction: {
          user: { id: senderId, username: 'sender', displayName: 'Sender', tag: 'sender#1234' },
          guild: { 
            id: guildId,
            members: {
              fetch: jest.fn().mockResolvedValue({ id: recipientId })
            }
          },
          options: {
            getUser: jest.fn().mockReturnValue({
              id: recipientId,
              username: 'recipient',
              displayName: 'Recipient',
              tag: 'recipient#1234',
              bot: false,
              send: jest.fn().mockRejectedValue(new Error('DM failed'))
            }),
            getInteger: jest.fn().mockReturnValue(200)
          },
          reply: jest.fn().mockResolvedValue(undefined)
        }
      } as any;

      // Execute payment (DM will fail but transaction should succeed)
      await payCommand.execute(mockContext);

      // Verify balances are still updated correctly
      const sender = await User.findOne({ discordId: senderId, guildId: guildId });
      const recipient = await User.findOne({ discordId: recipientId, guildId: guildId });

      expect(sender?.balance).toBe(800);
      expect(recipient?.balance).toBe(200);

      // Verify transaction records exist
      const transactions = await Transaction.find({ guildId: guildId, type: 'pay' });
      expect(transactions).toHaveLength(2);
    });
  });

  describe('Database Constraint Validation', () => {
    test('should enforce minimum balance constraints', async () => {
      const guildId = '111111111111111111';
      const senderId = '222222222222222222';
      const recipientId = '333333333333333333';

      // Create sender with minimal balance
      await User.create({
        discordId: senderId,
        guildId: guildId,
        balance: 50
      });

      const mockContext: CommandContext = {
        interaction: {
          user: { id: senderId, username: 'sender', displayName: 'Sender', tag: 'sender#1234' },
          guild: { 
            id: guildId,
            members: {
              fetch: jest.fn().mockResolvedValue({ id: recipientId })
            }
          },
          options: {
            getUser: jest.fn().mockReturnValue({
              id: recipientId,
              username: 'recipient',
              displayName: 'Recipient',
              tag: 'recipient#1234',
              bot: false,
              send: jest.fn().mockResolvedValue(undefined)
            }),
            getInteger: jest.fn().mockReturnValue(100) // More than available
          },
          reply: jest.fn().mockResolvedValue(undefined)
        }
      } as any;

      // Should reject the transaction
      await expect(payCommand.execute(mockContext)).rejects.toThrow('Insufficient balance');

      // Verify no changes to database
      const sender = await User.findOne({ discordId: senderId, guildId: guildId });
      const recipient = await User.findOne({ discordId: recipientId, guildId: guildId });

      expect(sender?.balance).toBe(50); // Unchanged
      expect(recipient).toBeNull(); // Should not exist

      // Verify no transaction records
      const transactions = await Transaction.find({ guildId: guildId, type: 'pay' });
      expect(transactions).toHaveLength(0);
    });
  });
});
