/**
 * Simple Election System Test
 * Quick test to verify core functionality
 */

require('dotenv').config();
const mongoose = require('mongoose');

async function simpleTest() {
  console.log('🗳️ Simple Election System Test...\n');

  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to database');

    // Import models and services
    const { ElectionService } = require('./dist/services/election/ElectionService');
    const Election = require('./dist/models/Election').default;
    const ElectionCandidate = require('./dist/models/ElectionCandidate').default;
    const ElectionVote = require('./dist/models/ElectionVote').default;
    const User = require('./dist/models/User').default;

    // Mock app context
    const mockApp = {
      logger: {
        info: (msg, data) => console.log(`[INFO] ${msg}`, data || ''),
        error: (msg, data) => console.log(`[ERROR] ${msg}`, data || ''),
        warn: (msg, data) => console.log(`[WARN] ${msg}`, data || ''),
        debug: (msg, data) => console.log(`[DEBUG] ${msg}`, data || '')
      },
      getService: () => null,
      isFeatureEnabled: () => true
    };

    // Clean up test data
    const testGuildId = '987654321098765432';
    await Election.deleteMany({ guildId: testGuildId });
    await ElectionCandidate.deleteMany({ guildId: testGuildId });
    await ElectionVote.deleteMany({ guildId: testGuildId });
    await User.deleteMany({ guildId: testGuildId });
    console.log('✅ Cleaned up test data');

    // Create test users
    const voter1 = await User.create({ discordId: '123456789012345678', guildId: testGuildId, balance: 100 });
    const voter2 = await User.create({ discordId: '234567890123456789', guildId: testGuildId, balance: 200 });
    const candidate1 = await User.create({ discordId: '345678901234567890', guildId: testGuildId, balance: 50 });
    const candidate2 = await User.create({ discordId: '456789012345678901', guildId: testGuildId, balance: 75 });
    console.log('✅ Created test users');

    // Initialize election service
    const electionService = new ElectionService(mockApp);
    await electionService.onInitialize();
    console.log('✅ Initialized election service');

    // Test 1: Create Election
    console.log('\n📝 Test 1: Creating Election...');
    const election = await electionService.createElection(
      testGuildId,
      '444444444444444444',
      '567890123456789012',
      {
        title: 'Simple Test Election',
        description: 'Testing the election system',
        rolesToPing: ['111111111111111111'],
        eligibleVoterRoles: ['222222222222222222'],
        eligibleCandidateRoles: ['333333333333333333'],
        allowMultipleVotes: true,
        showVoteWeights: true
      }
    );
    console.log(`✅ Election created: ${election.electionId}`);

    // Test 2: Add Candidates
    console.log('\n👥 Test 2: Adding Candidates...');
    await electionService.addCandidate(
      election.electionId,
      '345678901234567890',
      'Candidate One',
      'candidate1',
      'Vote for me!'
    );
    console.log('✅ Added candidate 1');

    await electionService.addCandidate(
      election.electionId,
      '456789012345678901',
      'Candidate Two',
      'candidate2',
      'I am the best choice!'
    );
    console.log('✅ Added candidate 2');

    // Test 3: Get Candidates
    console.log('\n📋 Test 3: Getting Candidates...');
    const candidates = await electionService.getElectionCandidates(election.electionId);
    console.log(`✅ Found ${candidates.length} candidates`);
    candidates.forEach(c => console.log(`   - ${c.displayName} (${c.discordId})`));

    // Test 4: Cast Votes
    console.log('\n🗳️ Test 4: Casting Votes...');
    
    // Voter 1 votes for candidate 1
    const vote1 = await electionService.castVote(
      election.electionId,
      '123456789012345678',
      '345678901234567890',
      'Voter One',
      'voter1',
      'Candidate One',
      'candidate1',
      testGuildId
    );
    console.log(`✅ Vote 1 cast: weight=${vote1.voteWeight}`);

    // Voter 2 votes for candidate 2
    const vote2 = await electionService.castVote(
      election.electionId,
      '234567890123456789',
      '456789012345678901',
      'Voter Two',
      'voter2',
      'Candidate Two',
      'candidate2',
      testGuildId
    );
    console.log(`✅ Vote 2 cast: weight=${vote2.voteWeight}`);

    // Test 5: Get Results
    console.log('\n📊 Test 5: Getting Results...');
    const finalCandidates = await electionService.getElectionCandidates(election.electionId);
    console.log('Final results:');
    finalCandidates.forEach((c, i) => {
      console.log(`   ${i + 1}. ${c.displayName}: ${c.voteWeight} coins (${c.voteCount} votes)`);
    });

    // Test 6: End Election
    console.log('\n🏁 Test 6: Ending Election...');
    const endedElection = await electionService.endElection(election.electionId, '567890123456789012');
    console.log(`✅ Election ended: status=${endedElection.status}`);

    console.log('\n🎉 Simple test completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from database');
  }
}

// Run the test
simpleTest().catch(console.error);
