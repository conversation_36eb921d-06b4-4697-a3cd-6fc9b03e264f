"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.legacyCommands = exports.commandManager = exports.CommandManager = exports.commandRegistry = exports.CommandRegistry = exports.CommandCategory = exports.BaseCommand = void 0;
exports.initializeCommands = initializeCommands;
exports.getDiscordCommands = getDiscordCommands;
var BaseCommand_1 = require("./base/BaseCommand");
Object.defineProperty(exports, "BaseCommand", { enumerable: true, get: function () { return BaseCommand_1.BaseCommand; } });
Object.defineProperty(exports, "CommandCategory", { enumerable: true, get: function () { return BaseCommand_1.CommandCategory; } });
Object.defineProperty(exports, "CommandRegistry", { enumerable: true, get: function () { return BaseCommand_1.CommandRegistry; } });
Object.defineProperty(exports, "commandRegistry", { enumerable: true, get: function () { return BaseCommand_1.commandRegistry; } });
var CommandManager_1 = require("./CommandManager");
Object.defineProperty(exports, "CommandManager", { enumerable: true, get: function () { return CommandManager_1.CommandManager; } });
Object.defineProperty(exports, "commandManager", { enumerable: true, get: function () { return CommandManager_1.commandManager; } });
__exportStar(require("./economy"), exports);
__exportStar(require("./admin"), exports);
__exportStar(require("./role"), exports);
__exportStar(require("./election"), exports);
exports.legacyCommands = {
    balance: require('./balance'),
    pay: require('./pay'),
    history: require('./history'),
    leaderboard: require('./leaderboard'),
    give: require('./give'),
    fine: require('./fine'),
    announce: require('./announce'),
    tax: require('./tax'),
    starterbalance: require('./starterbalance'),
    testcleanup: require('./testcleanup'),
    incomecredentials: require('./incomecredentials'),
    roles: require('./roles'),
    addrole: require('./addrole'),
    editrole: require('./editrole'),
    removerole: require('./removerole'),
    buyrole: require('./buyrole'),
    richestrole: require('./richestrole'),
    shop: require('./shop'),
    setroleforsale: require('./setroleforsale'),
    removerolefromsale: require('./removerolefromsale'),
    milestone: require('./milestone'),
    milestones: require('./milestones'),
    milestonestatus: require('./milestonestatus'),
    coindecay: require('./coindecay'),
    help: require('./help'),
    placeholders: require('./placeholders'),
    automessage: require('./automessage'),
    editautomessage: require('./editautomessage'),
    monetizechannel: require('./monetizechannel'),
};
async function initializeCommands() {
    const { commandManager } = await Promise.resolve().then(() => __importStar(require('./CommandManager')));
    return await commandManager.loadCommands();
}
function getDiscordCommands() {
    const { commandManager } = require('./CommandManager');
    return commandManager.getDiscordCommands();
}
