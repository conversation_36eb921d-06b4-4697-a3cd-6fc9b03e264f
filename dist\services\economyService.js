"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.adjustBalance = adjustBalance;
exports.getLeaderboard = getLeaderboard;
exports.getTransactionHistory = getTransactionHistory;
exports.ensureUser = ensureUser;
const User_1 = __importDefault(require("../models/User"));
const Transaction_1 = __importDefault(require("../models/Transaction"));
const errorHandler_1 = require("../utils/errorHandler");
const mongoose_1 = __importDefault(require("mongoose"));
const roleAssignmentService_1 = require("./roleAssignmentService");
function logDatabaseOperation(operation, details) {
    console.log(`[Database Operation] ${operation}:`, JSON.stringify(details, null, 2));
}
async function adjustBalance(discordId, guildId, amount, type, details, client) {
    if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
        throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
    }
    if (!guildId || typeof guildId !== 'string' || guildId.trim().length === 0) {
        throw new errorHandler_1.DatabaseError('Guild ID is required for all balance operations');
    }
    if (typeof amount !== 'number' || isNaN(amount)) {
        throw new errorHandler_1.DatabaseError('Invalid amount provided');
    }
    if (mongoose_1.default.connection.readyState !== 1) {
        throw new errorHandler_1.DatabaseError('Database is not connected. Please try again in a moment.');
    }
    const session = await mongoose_1.default.startSession();
    logDatabaseOperation('Starting Transaction', { discordId, guildId, amount, type, details });
    try {
        await session.withTransaction(async () => {
            logDatabaseOperation('Finding/Updating User', { discordId, guildId, amount });
            const trimmedDiscordId = discordId.trim();
            const trimmedGuildId = guildId.trim();
            if (!trimmedDiscordId) {
                throw new Error('Discord ID cannot be empty after trimming');
            }
            if (!trimmedGuildId) {
                throw new Error('Guild ID cannot be empty after trimming');
            }
            const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId, guildId: trimmedGuildId }, {
                $inc: { balance: amount },
                $setOnInsert: { discordId: trimmedDiscordId, guildId: trimmedGuildId }
            }, {
                new: true,
                upsert: true,
                runValidators: true,
                session
            });
            logDatabaseOperation('Creating Transaction Record', {
                discordId: trimmedDiscordId,
                guildId: trimmedGuildId,
                type,
                amount,
                details
            });
            await Transaction_1.default.create([{
                    discordId: trimmedDiscordId,
                    guildId: trimmedGuildId,
                    type,
                    amount,
                    details,
                    timestamp: new Date()
                }], { session });
            logDatabaseOperation('Transaction Complete', { userId: user?._id, newBalance: user?.balance });
            if (amount > 0 && client && trimmedGuildId && user) {
                setImmediate(async () => {
                    try {
                        const roleResult = await (0, roleAssignmentService_1.checkAndAssignRoles)(client, trimmedDiscordId, trimmedGuildId, user.balance);
                        if (roleResult) {
                            await (0, roleAssignmentService_1.sendRoleAchievementNotifications)(roleResult, client);
                        }
                    }
                    catch (error) {
                        console.error('Error checking roles after balance adjustment:', error);
                    }
                });
            }
            return user;
        });
    }
    catch (error) {
        console.error('Error in adjustBalance:', error);
        if (error instanceof Error && error.name === 'ValidationError') {
            throw new errorHandler_1.DatabaseError('Transaction validation failed', error);
        }
        else if (error instanceof Error && error.name === 'MongoServerError') {
            const err = error;
            if (err.code === 11000) {
                throw new errorHandler_1.DatabaseError('Transaction conflict detected', error);
            }
            throw new errorHandler_1.DatabaseError('Database operation failed', error);
        }
        else if (error instanceof Error) {
            throw new errorHandler_1.DatabaseError('Transaction processing failed', error);
        }
        throw new errorHandler_1.DatabaseError('Unexpected error during transaction');
    }
    finally {
        await session.endSession();
    }
}
async function getLeaderboard(guildId, limit = 10) {
    try {
        if (!guildId || typeof guildId !== 'string' || guildId.trim().length === 0) {
            throw new errorHandler_1.DatabaseError('Guild ID is required for leaderboard queries');
        }
        const trimmedGuildId = guildId.trim();
        logDatabaseOperation('Fetching Guild Leaderboard', { guildId: trimmedGuildId, limit });
        const users = await User_1.default.find({ guildId: trimmedGuildId })
            .sort({ balance: -1 })
            .limit(limit);
        logDatabaseOperation('Guild Leaderboard Fetched', { guildId: trimmedGuildId, count: users.length });
        return users;
    }
    catch (error) {
        if (error instanceof Error) {
            throw new errorHandler_1.DatabaseError('Failed to fetch guild leaderboard', error);
        }
        throw new errorHandler_1.DatabaseError('Failed to fetch guild leaderboard');
    }
}
async function getTransactionHistory(discordId, guildId, limit = 10) {
    try {
        if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
            throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
        }
        if (!guildId || typeof guildId !== 'string' || guildId.trim().length === 0) {
            throw new errorHandler_1.DatabaseError('Guild ID is required for transaction history queries');
        }
        const trimmedDiscordId = discordId.trim();
        const trimmedGuildId = guildId.trim();
        logDatabaseOperation('Fetching Guild Transaction History', { discordId: trimmedDiscordId, guildId: trimmedGuildId, limit });
        const transactions = await Transaction_1.default.find({
            discordId: trimmedDiscordId,
            guildId: trimmedGuildId
        }).sort({ timestamp: -1 }).limit(limit);
        logDatabaseOperation('Guild Transaction History Fetched', {
            discordId: trimmedDiscordId,
            guildId: trimmedGuildId,
            count: transactions.length
        });
        return transactions;
    }
    catch (error) {
        if (error instanceof Error) {
            throw new errorHandler_1.DatabaseError('Failed to fetch guild transaction history', error);
        }
        throw new errorHandler_1.DatabaseError('Failed to fetch guild transaction history');
    }
}
async function ensureUser(discordId, guildId) {
    if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
        throw new errorHandler_1.DatabaseError('Invalid Discord ID provided to ensureUser');
    }
    if (!guildId || typeof guildId !== 'string' || guildId.trim().length === 0) {
        throw new errorHandler_1.DatabaseError('Guild ID is required for ensureUser');
    }
    try {
        const trimmedDiscordId = discordId.trim();
        const trimmedGuildId = guildId.trim();
        logDatabaseOperation('Ensuring User Exists in Guild', { discordId: trimmedDiscordId, guildId: trimmedGuildId });
        let user = await User_1.default.findOne({ discordId: trimmedDiscordId, guildId: trimmedGuildId });
        if (user) {
            logDatabaseOperation('User Found in Guild', {
                userId: user._id,
                discordId: trimmedDiscordId,
                guildId: trimmedGuildId
            });
            return user;
        }
        try {
            user = new User_1.default({
                discordId: trimmedDiscordId,
                guildId: trimmedGuildId,
                balance: 0
            });
            await user.save();
            logDatabaseOperation('User Created in Guild', {
                userId: user._id,
                discordId: trimmedDiscordId,
                guildId: trimmedGuildId
            });
            return user;
        }
        catch (createError) {
            if (createError.code === 11000) {
                user = await User_1.default.findOne({ discordId: trimmedDiscordId, guildId: trimmedGuildId });
                if (user) {
                    logDatabaseOperation('User Found After Race Condition', {
                        userId: user._id,
                        discordId: trimmedDiscordId,
                        guildId: trimmedGuildId
                    });
                    return user;
                }
            }
            throw createError;
        }
    }
    catch (error) {
        if (error instanceof Error && error.name === 'ValidationError') {
            throw new errorHandler_1.DatabaseError('Invalid user data format', error);
        }
        else if (error instanceof Error) {
            throw new errorHandler_1.DatabaseError('Failed to create/fetch user', error);
        }
        throw new errorHandler_1.DatabaseError('Unexpected error while creating/fetching user');
    }
}
