"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const incomeGuideService_1 = require("../services/incomeGuideService");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('incomecredentials')
        .setDescription('Customize the income earning guide text displayed in /help command (admin only)')
        .addStringOption(option => option.setName('text')
        .setDescription('Custom text explaining how users can earn coins')
        .setRequired(true))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError();
        }
        const guild = interaction.guild;
        if (!guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        const customText = interaction.options.getString('text', true);
        if (customText.length > 1000) {
            throw new errorHandler_1.ValidationError('Income guide text cannot exceed 1000 characters. Please shorten your text.');
        }
        if (customText.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Income guide text cannot be empty.');
        }
        const processedText = customText
            .replace(/\\n/g, '\n')
            .replace(/\r\n/g, '\n')
            .replace(/\r/g, '\n')
            .trim();
        const guildId = interaction.guild.id;
        const loadingEmbed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, 'Updating Income Guide');
        loadingEmbed.setDescription(`${embedBuilder_1.EMOJIS.MISC.CLOCK} Updating the income earning guide...`);
        await interaction.reply({
            embeds: [loadingEmbed],
            ephemeral: true
        });
        try {
            await (0, incomeGuideService_1.setIncomeGuideText)(guild.id, customText);
            const successEmbed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Income Guide Updated Successfully');
            successEmbed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Income earning guide has been updated!**\n\n` +
                `The new text will now appear in all \`/help\` command responses with proper formatting.`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Formatted Text Preview`,
                value: processedText.length > 500 ?
                    `${processedText.substring(0, 500)}...\n\n*Text truncated for display*` :
                    processedText,
                inline: false
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Text Statistics`,
                value: `**Length:** ${customText.length}/1000 characters\n**Lines:** ${processedText.split('\n').length}\n**Line breaks:** ${(processedText.match(/\n/g) || []).length}`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Updated`,
                value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: true
            })
                .setFooter({
                text: 'Line breaks (\\n) and empty lines are preserved for better formatting'
            });
            await interaction.editReply({
                embeds: [successEmbed]
            });
            console.log(`[IncomeCredentials] Income guide updated by ${interaction.user.tag} (${interaction.user.id}) in ${guild.name} (${guild.id})`);
            console.log(`[IncomeCredentials] New text length: ${customText.length} characters`);
        }
        catch (error) {
            console.error(`[IncomeCredentials] Error updating income guide:`, error);
            throw error;
        }
    })
};
