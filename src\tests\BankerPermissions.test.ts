/**
 * Banker Permissions Test
 * Test the banker role functionality and bot fining capability
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { GuildMember, PermissionFlagsBits, Guild, Role, Collection } from 'discord.js';
import { hasBankerPermissions, validateBankerPermissions, setBankerRole, getBankerRoleId } from '../utils/permissions/BankerPermissions';
import ServerConfiguration from '../models/ServerConfiguration';

// Mock Discord.js objects
const createMockGuild = (id: string): Partial<Guild> => ({
  id,
  roles: {
    fetch: jest.fn(),
    cache: new Collection()
  } as any
});

const createMockRole = (id: string, name: string): Partial<Role> => ({
  id,
  name,
  position: 1,
  members: new Collection()
});

const createMockMember = (
  userId: string, 
  guildId: string, 
  hasAdmin: boolean = false, 
  roleIds: string[] = []
): Partial<GuildMember> => {
  const roleCollection = new Collection();
  roleIds.forEach(roleId => {
    roleCollection.set(roleId, createMockRole(roleId, `Role-${roleId}`) as Role);
  });

  return {
    id: userId,
    guild: createMockGuild(guildId) as Guild,
    permissions: {
      has: jest.fn((permission) => {
        if (permission === PermissionFlagsBits.Administrator) {
          return hasAdmin;
        }
        return false;
      })
    } as any,
    roles: {
      cache: roleCollection
    } as any
  };
};

// Mock ServerConfiguration
jest.mock('../models/ServerConfiguration', () => ({
  findByGuildId: jest.fn(),
  createOrUpdate: jest.fn()
}));

const mockServerConfiguration = ServerConfiguration as jest.Mocked<typeof ServerConfiguration>;

describe('Banker Permissions', () => {
  const testGuildId = '123456789012345678';
  const testUserId = '987654321098765432';
  const testRoleId = '555666777888999000';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('hasBankerPermissions', () => {
    it('should return true for admin users', async () => {
      const member = createMockMember(testUserId, testGuildId, true) as GuildMember;
      
      const result = await hasBankerPermissions(member);
      
      expect(result).toBe(true);
    });

    it('should return true for users with banker role', async () => {
      const member = createMockMember(testUserId, testGuildId, false, [testRoleId]) as GuildMember;
      
      mockServerConfiguration.findByGuildId.mockResolvedValue({
        bankerRoleId: testRoleId
      } as any);
      
      const result = await hasBankerPermissions(member);
      
      expect(result).toBe(true);
      expect(mockServerConfiguration.findByGuildId).toHaveBeenCalledWith(testGuildId);
    });

    it('should return false for users without admin or banker role', async () => {
      const member = createMockMember(testUserId, testGuildId, false, []) as GuildMember;
      
      mockServerConfiguration.findByGuildId.mockResolvedValue({
        bankerRoleId: testRoleId
      } as any);
      
      const result = await hasBankerPermissions(member);
      
      expect(result).toBe(false);
    });

    it('should return false when no banker role is configured', async () => {
      const member = createMockMember(testUserId, testGuildId, false, [testRoleId]) as GuildMember;
      
      mockServerConfiguration.findByGuildId.mockResolvedValue({
        bankerRoleId: null
      } as any);
      
      const result = await hasBankerPermissions(member);
      
      expect(result).toBe(false);
    });

    it('should return false when server configuration does not exist', async () => {
      const member = createMockMember(testUserId, testGuildId, false, [testRoleId]) as GuildMember;
      
      mockServerConfiguration.findByGuildId.mockResolvedValue(null);
      
      const result = await hasBankerPermissions(member);
      
      expect(result).toBe(false);
    });

    it('should handle database errors gracefully', async () => {
      const member = createMockMember(testUserId, testGuildId, false, [testRoleId]) as GuildMember;
      
      mockServerConfiguration.findByGuildId.mockRejectedValue(new Error('Database error'));
      
      const result = await hasBankerPermissions(member);
      
      expect(result).toBe(false);
    });
  });

  describe('validateBankerPermissions', () => {
    it('should not throw for users with banker permissions', async () => {
      const member = createMockMember(testUserId, testGuildId, true) as GuildMember;
      
      await expect(validateBankerPermissions(member)).resolves.not.toThrow();
    });

    it('should throw ValidationError for users without banker permissions', async () => {
      const member = createMockMember(testUserId, testGuildId, false, []) as GuildMember;
      
      mockServerConfiguration.findByGuildId.mockResolvedValue(null);
      
      await expect(validateBankerPermissions(member)).rejects.toThrow('This command requires administrator permissions or the designated banker role.');
    });
  });

  describe('setBankerRole', () => {
    it('should call ServerConfiguration.createOrUpdate with correct parameters', async () => {
      mockServerConfiguration.createOrUpdate.mockResolvedValue({} as any);
      
      await setBankerRole(testGuildId, testRoleId);
      
      expect(mockServerConfiguration.createOrUpdate).toHaveBeenCalledWith(testGuildId, {
        bankerRoleId: testRoleId
      });
    });

    it('should handle null role ID', async () => {
      mockServerConfiguration.createOrUpdate.mockResolvedValue({} as any);
      
      await setBankerRole(testGuildId, null);
      
      expect(mockServerConfiguration.createOrUpdate).toHaveBeenCalledWith(testGuildId, {
        bankerRoleId: null
      });
    });

    it('should throw ValidationError on database error', async () => {
      mockServerConfiguration.createOrUpdate.mockRejectedValue(new Error('Database error'));
      
      await expect(setBankerRole(testGuildId, testRoleId)).rejects.toThrow('Failed to update banker role configuration.');
    });
  });

  describe('getBankerRoleId', () => {
    it('should return banker role ID when configured', async () => {
      mockServerConfiguration.findByGuildId.mockResolvedValue({
        bankerRoleId: testRoleId
      } as any);
      
      const result = await getBankerRoleId(testGuildId);
      
      expect(result).toBe(testRoleId);
    });

    it('should return null when no banker role is configured', async () => {
      mockServerConfiguration.findByGuildId.mockResolvedValue({
        bankerRoleId: null
      } as any);
      
      const result = await getBankerRoleId(testGuildId);
      
      expect(result).toBe(null);
    });

    it('should return null when server configuration does not exist', async () => {
      mockServerConfiguration.findByGuildId.mockResolvedValue(null);
      
      const result = await getBankerRoleId(testGuildId);
      
      expect(result).toBe(null);
    });

    it('should return null on database error', async () => {
      mockServerConfiguration.findByGuildId.mockRejectedValue(new Error('Database error'));
      
      const result = await getBankerRoleId(testGuildId);
      
      expect(result).toBe(null);
    });
  });
});

/**
 * Integration Test Notes:
 * 
 * To test the complete functionality:
 * 1. Set up a test Discord server
 * 2. Create a test role for banker permissions
 * 3. Use /bankers set @role to configure the banker role
 * 4. Test /give and /fine commands with:
 *    - Admin users (should work)
 *    - Users with banker role (should work)
 *    - Users without admin or banker role (should fail)
 * 5. Test /fine command with bot targets (should work now)
 * 6. Use /bankers remove to clear banker role
 * 7. Verify only admins can use /give and /fine after removal
 */
