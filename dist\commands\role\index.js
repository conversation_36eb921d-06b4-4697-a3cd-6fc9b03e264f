"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateNamesCommand = exports.EnhanceRoleCommand = void 0;
var EnhanceRoleCommand_1 = require("./EnhanceRoleCommand");
Object.defineProperty(exports, "EnhanceRoleCommand", { enumerable: true, get: function () { return EnhanceRoleCommand_1.EnhanceRoleCommand; } });
var UpdateNamesCommand_1 = require("./UpdateNamesCommand");
Object.defineProperty(exports, "UpdateNamesCommand", { enumerable: true, get: function () { return UpdateNamesCommand_1.UpdateNamesCommand; } });
