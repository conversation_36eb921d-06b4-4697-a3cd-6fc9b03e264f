# Banker Role Implementation - Deployment Troubleshooting

## Root Cause Analysis

**Issue Identified:** The TypeScript source code changes have not been compiled to JavaScript. The bot is running the old compiled code from the `dist/` directory.

**Evidence:**
- ✅ Source code in `src/` contains correct changes
- ❌ Compiled code in `dist/` still has old bot restriction
- ❌ `BankersCommand.js` doesn't exist in `dist/commands/admin/`
- ❌ `BankerPermissions.js` doesn't exist in `dist/utils/permissions/`

## Step-by-Step Resolution

### Step 1: Check Build Configuration

First, verify your build setup:

```bash
# Check if you have a build script in package.json
cat package.json | grep -A 5 -B 5 "scripts"

# Look for TypeScript configuration
ls -la tsconfig.json
```

### Step 2: Compile TypeScript Code

Run the TypeScript compiler to build your changes:

```bash
# Option A: If you have npm scripts
npm run build

# Option B: If you have yarn
yarn build

# Option C: Direct TypeScript compilation
npx tsc

# Option D: If using ts-node in development
npx ts-node-dev --respawn src/index.ts
```

### Step 3: Verify Compilation Success

Check that the new files were created:

```bash
# Check if BankersCommand was compiled
ls -la dist/commands/admin/BankersCommand.js

# Check if BankerPermissions was compiled
ls -la dist/utils/permissions/BankerPermissions.js

# Check if fine.js was updated (should not contain bot restriction)
grep -n "cannot fine a bot" dist/commands/fine.js
```

**Expected Results:**
- `BankersCommand.js` should exist
- `BankerPermissions.js` should exist  
- `grep` command should return no results (bot restriction removed)

### Step 4: Stop the Bot Process

```bash
# Find the bot process
ps aux | grep node
# or
pm2 list

# Stop the bot (choose appropriate method)
# Option A: If using PM2
pm2 stop your-bot-name
pm2 delete your-bot-name

# Option B: If running directly
pkill -f "node.*bot"
# or use Ctrl+C if running in terminal

# Option C: If using systemd
sudo systemctl stop your-bot-service
```

### Step 5: Start the Bot with New Code

```bash
# Option A: If using PM2
pm2 start dist/index.js --name "your-bot-name"

# Option B: Direct execution
node dist/index.js

# Option C: If using systemd
sudo systemctl start your-bot-service

# Option D: Development mode with auto-restart
npm run dev
# or
yarn dev
```

### Step 6: Force Discord Command Registration

The bot needs to re-register slash commands with Discord API:

```bash
# Look for a command registration script or add this to your bot startup
# This should happen automatically when the bot starts, but you may need to:

# 1. Check if there's a deploy-commands script
npm run deploy-commands
# or
node deploy-commands.js

# 2. Or restart the bot completely to trigger re-registration
```

### Step 7: Verify Deployment Success

**Check 1: Console Logs**
Look for these messages in bot console:
```
✅ Registered command: bankers (admin)
✅ Command loaded: BankersCommand
✅ Slash commands registered with Discord
```

**Check 2: Discord Command Availability**
- Type `/bankers` in Discord - should show autocomplete
- Should see subcommands: `set`, `remove`, `view`

**Check 3: Test Bot Fining**
```
/fine @SomeBot 1
```
Should work without "cannot fine a bot" error.

## Common Build Issues & Solutions

### Issue: "tsc command not found"
```bash
# Install TypeScript globally
npm install -g typescript

# Or use npx
npx tsc
```

### Issue: "Module not found" errors
```bash
# Install dependencies
npm install
# or
yarn install

# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Issue: Permission errors
```bash
# Fix file permissions
chmod +x dist/index.js

# Or run with sudo if needed
sudo npm run build
```

### Issue: Port already in use
```bash
# Find and kill process using the port
lsof -ti:3000 | xargs kill -9
# Replace 3000 with your bot's port
```

## Verification Commands

After deployment, run these tests:

```bash
# 1. Check compiled files exist
ls -la dist/commands/admin/BankersCommand.js
ls -la dist/utils/permissions/BankerPermissions.js

# 2. Verify bot restriction removed
grep -i "cannot fine a bot" dist/commands/fine.js
# Should return nothing

# 3. Check bot is running
ps aux | grep node
# or
pm2 status

# 4. Check bot logs for errors
tail -f logs/bot.log
# or
pm2 logs your-bot-name
```

## Discord Testing Checklist

Once deployed:

- [ ] `/bankers` command appears in autocomplete
- [ ] `/bankers set @role` works for admins
- [ ] `/bankers view` shows current configuration
- [ ] `/bankers remove` clears banker role
- [ ] `/give` works for users with banker role
- [ ] `/fine` works for users with banker role
- [ ] `/fine @BotName amount` works (no bot restriction error)
- [ ] Regular users without banker role get permission error

## Emergency Rollback

If issues persist:

```bash
# 1. Stop the bot
pm2 stop your-bot-name

# 2. Checkout previous working commit
git log --oneline -10
git checkout <previous-commit-hash>

# 3. Rebuild and restart
npm run build
pm2 start dist/index.js --name "your-bot-name"
```

## Next Steps

After successful deployment:
1. Test all functionality in a development server first
2. Monitor bot logs for any runtime errors
3. Verify database connections work properly
4. Test with actual users having banker roles
5. Document the new commands for your server members
