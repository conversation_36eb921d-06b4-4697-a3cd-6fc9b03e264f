"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EscrowManager = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const BaseService_1 = require("../../base/BaseService");
const features_1 = require("../../../config/features");
const errorHandler_1 = require("../../../utils/errorHandler");
const models_1 = require("../../../models");
class EscrowManager extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'EscrowManager';
    }
    async initialize() {
        this.logger.info('[EscrowManager] Escrow manager initialized');
    }
    async lockEscrow(trade, session) {
        this.logOperation('Locking escrow funds', {
            tradeId: trade.tradeId,
            buyerId: trade.buyerId,
            amount: trade.amount
        });
        try {
            const buyer = await models_1.User.findOne({ discordId: trade.buyerId }).session(session);
            if (!buyer) {
                throw new errorHandler_1.ValidationError('Buyer not found');
            }
            if (buyer.balance < trade.amount) {
                throw new errorHandler_1.ValidationError(`Insufficient balance. Required: ${trade.amount}, Available: ${buyer.balance}`);
            }
            buyer.balance -= trade.amount;
            await buyer.save({ session });
            const transactionId = this.generateTransactionId();
            await models_1.Transaction.create([{
                    discordId: trade.buyerId,
                    type: 'trade_escrow',
                    amount: -trade.amount,
                    details: `Escrow locked for trade ${trade.tradeId}`,
                    tradeId: trade.tradeId,
                    timestamp: new Date()
                }], { session });
            const escrowTransaction = await models_1.EscrowTransaction.create([{
                    escrowId: this.generateEscrowId(),
                    tradeId: trade.tradeId,
                    discordId: trade.buyerId,
                    guildId: trade.guildId,
                    amount: trade.amount,
                    transactionType: 'LOCK',
                    status: 'COMPLETED',
                    timestamp: new Date(),
                    completedAt: new Date(),
                    relatedTransactionId: transactionId,
                    details: `Funds locked in escrow for trade ${trade.tradeId}`,
                    reason: 'Trade escrow lock'
                }], { session });
            trade.escrowLocked = true;
            trade.escrowAmount = trade.amount;
            await trade.save({ session });
            this.logOperation('Escrow funds locked successfully', {
                tradeId: trade.tradeId,
                escrowId: escrowTransaction[0].escrowId,
                amount: trade.amount
            });
            return escrowTransaction[0];
        }
        catch (error) {
            this.handleError(error, { operation: 'lock_escrow', tradeId: trade.tradeId });
            throw error;
        }
    }
    async releaseEscrow(trade, reason, session) {
        this.logOperation('Releasing escrow funds', {
            tradeId: trade.tradeId,
            sellerId: trade.sellerId,
            amount: trade.escrowAmount
        });
        try {
            if (!trade.escrowLocked || trade.escrowAmount <= 0) {
                throw new errorHandler_1.ValidationError('No funds in escrow for this trade');
            }
            const seller = await models_1.User.findOneAndUpdate({ discordId: trade.sellerId }, { $inc: { balance: trade.escrowAmount } }, { new: true, upsert: true, session });
            if (!seller) {
                throw new errorHandler_1.DatabaseError('Failed to update seller balance');
            }
            const transactionId = this.generateTransactionId();
            await models_1.Transaction.create([{
                    discordId: trade.sellerId,
                    type: 'trade_release',
                    amount: trade.escrowAmount,
                    details: `Escrow released for trade ${trade.tradeId}`,
                    tradeId: trade.tradeId,
                    timestamp: new Date()
                }], { session });
            const escrowTransaction = await models_1.EscrowTransaction.create([{
                    escrowId: this.generateEscrowId(),
                    tradeId: trade.tradeId,
                    discordId: trade.sellerId,
                    guildId: trade.guildId,
                    amount: trade.escrowAmount,
                    transactionType: 'RELEASE',
                    status: 'COMPLETED',
                    timestamp: new Date(),
                    completedAt: new Date(),
                    relatedTransactionId: transactionId,
                    details: `Escrow funds released to seller for trade ${trade.tradeId}`,
                    reason
                }], { session });
            trade.escrowLocked = false;
            await trade.save({ session });
            this.logOperation('Escrow funds released successfully', {
                tradeId: trade.tradeId,
                escrowId: escrowTransaction[0].escrowId,
                amount: trade.escrowAmount,
                sellerId: trade.sellerId
            });
            return escrowTransaction[0];
        }
        catch (error) {
            this.handleError(error, { operation: 'release_escrow', tradeId: trade.tradeId });
            throw error;
        }
    }
    async refundEscrow(trade, reason, session) {
        this.logOperation('Refunding escrow funds', {
            tradeId: trade.tradeId,
            buyerId: trade.buyerId,
            amount: trade.escrowAmount
        });
        try {
            if (!trade.escrowLocked || trade.escrowAmount <= 0) {
                throw new errorHandler_1.ValidationError('No funds in escrow for this trade');
            }
            const buyer = await models_1.User.findOneAndUpdate({ discordId: trade.buyerId }, { $inc: { balance: trade.escrowAmount } }, { new: true, upsert: true, session });
            if (!buyer) {
                throw new errorHandler_1.DatabaseError('Failed to update buyer balance');
            }
            const transactionId = this.generateTransactionId();
            await models_1.Transaction.create([{
                    discordId: trade.buyerId,
                    type: 'trade_refund',
                    amount: trade.escrowAmount,
                    details: `Escrow refunded for trade ${trade.tradeId}`,
                    tradeId: trade.tradeId,
                    timestamp: new Date()
                }], { session });
            const escrowTransaction = await models_1.EscrowTransaction.create([{
                    escrowId: this.generateEscrowId(),
                    tradeId: trade.tradeId,
                    discordId: trade.buyerId,
                    guildId: trade.guildId,
                    amount: trade.escrowAmount,
                    transactionType: 'REFUND',
                    status: 'COMPLETED',
                    timestamp: new Date(),
                    completedAt: new Date(),
                    relatedTransactionId: transactionId,
                    details: `Escrow funds refunded to buyer for trade ${trade.tradeId}`,
                    reason
                }], { session });
            trade.escrowLocked = false;
            await trade.save({ session });
            this.logOperation('Escrow funds refunded successfully', {
                tradeId: trade.tradeId,
                escrowId: escrowTransaction[0].escrowId,
                amount: trade.escrowAmount,
                buyerId: trade.buyerId
            });
            return escrowTransaction[0];
        }
        catch (error) {
            this.handleError(error, { operation: 'refund_escrow', tradeId: trade.tradeId });
            throw error;
        }
    }
    async splitEscrow(trade, sellerAmount, buyerAmount, reason, session) {
        this.logOperation('Splitting escrow funds', {
            tradeId: trade.tradeId,
            sellerAmount,
            buyerAmount,
            totalEscrow: trade.escrowAmount
        });
        try {
            if (!trade.escrowLocked || trade.escrowAmount <= 0) {
                throw new errorHandler_1.ValidationError('No funds in escrow for this trade');
            }
            if (sellerAmount + buyerAmount !== trade.escrowAmount) {
                throw new errorHandler_1.ValidationError('Split amounts must equal total escrow amount');
            }
            if (sellerAmount < 0 || buyerAmount < 0) {
                throw new errorHandler_1.ValidationError('Split amounts cannot be negative');
            }
            let sellerTransaction;
            let buyerTransaction;
            if (sellerAmount > 0) {
                const seller = await models_1.User.findOneAndUpdate({ discordId: trade.sellerId }, { $inc: { balance: sellerAmount } }, { new: true, upsert: true, session });
                if (!seller) {
                    throw new errorHandler_1.DatabaseError('Failed to update seller balance');
                }
                const sellerTransactionId = this.generateTransactionId();
                await models_1.Transaction.create([{
                        discordId: trade.sellerId,
                        type: 'trade_release',
                        amount: sellerAmount,
                        details: `Partial escrow release (dispute resolution) for trade ${trade.tradeId}`,
                        tradeId: trade.tradeId,
                        timestamp: new Date()
                    }], { session });
                const sellerEscrowTx = await models_1.EscrowTransaction.create([{
                        escrowId: this.generateEscrowId(),
                        tradeId: trade.tradeId,
                        discordId: trade.sellerId,
                        guildId: trade.guildId,
                        amount: sellerAmount,
                        transactionType: 'RELEASE',
                        status: 'COMPLETED',
                        timestamp: new Date(),
                        completedAt: new Date(),
                        relatedTransactionId: sellerTransactionId,
                        details: `Partial escrow release to seller (dispute resolution) for trade ${trade.tradeId}`,
                        reason
                    }], { session });
                sellerTransaction = sellerEscrowTx[0];
            }
            if (buyerAmount > 0) {
                const buyer = await models_1.User.findOneAndUpdate({ discordId: trade.buyerId }, { $inc: { balance: buyerAmount } }, { new: true, upsert: true, session });
                if (!buyer) {
                    throw new errorHandler_1.DatabaseError('Failed to update buyer balance');
                }
                const buyerTransactionId = this.generateTransactionId();
                await models_1.Transaction.create([{
                        discordId: trade.buyerId,
                        type: 'trade_refund',
                        amount: buyerAmount,
                        details: `Partial escrow refund (dispute resolution) for trade ${trade.tradeId}`,
                        tradeId: trade.tradeId,
                        timestamp: new Date()
                    }], { session });
                const buyerEscrowTx = await models_1.EscrowTransaction.create([{
                        escrowId: this.generateEscrowId(),
                        tradeId: trade.tradeId,
                        discordId: trade.buyerId,
                        guildId: trade.guildId,
                        amount: buyerAmount,
                        transactionType: 'REFUND',
                        status: 'COMPLETED',
                        timestamp: new Date(),
                        completedAt: new Date(),
                        relatedTransactionId: buyerTransactionId,
                        details: `Partial escrow refund to buyer (dispute resolution) for trade ${trade.tradeId}`,
                        reason
                    }], { session });
                buyerTransaction = buyerEscrowTx[0];
            }
            trade.escrowLocked = false;
            await trade.save({ session });
            this.logOperation('Escrow funds split successfully', {
                tradeId: trade.tradeId,
                sellerAmount,
                buyerAmount
            });
            return { sellerTransaction: sellerTransaction, buyerTransaction };
        }
        catch (error) {
            this.handleError(error, { operation: 'split_escrow', tradeId: trade.tradeId });
            throw error;
        }
    }
    async getEscrowBalance(tradeId) {
        try {
            return await models_1.EscrowTransaction.getEscrowBalance(tradeId);
        }
        catch (error) {
            this.handleError(error, { operation: 'get_escrow_balance', tradeId });
            throw error;
        }
    }
    async getUserEscrowedAmount(discordId) {
        try {
            return await models_1.EscrowTransaction.getUserEscrowedAmount(discordId);
        }
        catch (error) {
            this.handleError(error, { operation: 'get_user_escrowed_amount', discordId });
            throw error;
        }
    }
    generateEscrowId() {
        return `escrow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generateTransactionId() {
        return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.EscrowManager = EscrowManager;
__decorate([
    (0, features_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], EscrowManager.prototype, "lockEscrow", null);
__decorate([
    (0, features_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], EscrowManager.prototype, "releaseEscrow", null);
__decorate([
    (0, features_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], EscrowManager.prototype, "refundEscrow", null);
__decorate([
    (0, features_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number, String, Object]),
    __metadata("design:returntype", Promise)
], EscrowManager.prototype, "splitEscrow", null);
