"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncomeGuide = void 0;
const mongoose_1 = require("mongoose");
const incomeGuideSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        unique: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    customText: {
        type: String,
        required: [true, 'Custom income guide text is required'],
        maxlength: [1000, 'Income guide text cannot exceed 1000 characters'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Income guide text cannot be empty'
        }
    }
}, {
    timestamps: true
});
exports.IncomeGuide = (0, mongoose_1.model)('IncomeGuide', incomeGuideSchema);
exports.default = exports.IncomeGuide;
