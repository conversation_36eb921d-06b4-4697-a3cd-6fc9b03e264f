"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const announcementCooldowns = new Map();
const COOLDOWN_DURATION = 10 * 60 * 1000;
const DM_DELAY = 2500;
const MAX_TITLE_LENGTH = 100;
const MAX_DESCRIPTION_LENGTH = 1500;
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('announce')
        .setDescription('Send announcements to all members with a specific role (admin only)')
        .addRoleOption(option => option.setName('role')
        .setDescription('The role to target for announcements')
        .setRequired(true))
        .addStringOption(option => option.setName('title')
        .setDescription('Announcement title/subject line')
        .setRequired(true)
        .setMaxLength(MAX_TITLE_LENGTH))
        .addStringOption(option => option.setName('description')
        .setDescription('Main announcement message content')
        .setRequired(true)
        .setMaxLength(MAX_DESCRIPTION_LENGTH))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError();
        }
        const guild = interaction.guild;
        if (!guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        const targetRole = interaction.options.getRole('role', true);
        const title = interaction.options.getString('title', true);
        const description = interaction.options.getString('description', true);
        const adminId = interaction.user.id;
        const lastAnnouncement = announcementCooldowns.get(guild.id);
        if (lastAnnouncement && Date.now() - lastAnnouncement < COOLDOWN_DURATION) {
            const remainingTime = Math.ceil((COOLDOWN_DURATION - (Date.now() - lastAnnouncement)) / 1000 / 60);
            throw new errorHandler_1.ValidationError(`Announcement cooldown active. Please wait ${remainingTime} more minutes before sending another announcement.`);
        }
        if (targetRole.name === '@everyone' || targetRole.name === '@here') {
            throw new errorHandler_1.ValidationError('Cannot target @everyone or @here roles for announcements.');
        }
        await guild.members.fetch();
        const roleMembers = targetRole.members.filter(member => !member.user.bot);
        if (roleMembers.size === 0) {
            throw new errorHandler_1.ValidationError(`No members found with the ${targetRole.name} role.`);
        }
        const guildId = interaction.guild.id;
        const confirmationEmbed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, 'Announcement Confirmation Required');
        confirmationEmbed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Please confirm this announcement**\n\n` +
            `You are about to send a DM to **${roleMembers.size}** members with the **${targetRole.name}** role.`)
            .addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Target Role`,
            value: `**${targetRole.name}**`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.ACTIONS.TARGET} Recipients`,
            value: `**${roleMembers.size}** members`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Estimated Time`,
            value: `~${Math.ceil(roleMembers.size * DM_DELAY / 1000 / 60)} minutes`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.BOOK} Title`,
            value: title,
            inline: false
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Message Preview`,
            value: description.length > 200 ? description.substring(0, 200) + '...' : description,
            inline: false
        })
            .setColor(embedBuilder_1.COLORS.WARNING)
            .setFooter({
            text: 'This action cannot be undone. Click Confirm to proceed.'
        });
        const confirmationButtons = (0, embedBuilder_1.createConfirmationButtons)('announce_confirm', 'announce_cancel');
        await interaction.reply({
            embeds: [confirmationEmbed],
            components: [confirmationButtons],
            ephemeral: true
        });
        const announcementData = {
            targetRole,
            title,
            description,
            roleMembers: Array.from(roleMembers.values()),
            adminId,
            guildId: guild.id,
            guildName: guild.name
        };
        global.pendingAnnouncements = global.pendingAnnouncements || new Map();
        global.pendingAnnouncements.set(interaction.id, announcementData);
        setTimeout(() => {
            global.pendingAnnouncements?.delete(interaction.id);
        }, 5 * 60 * 1000);
    })
};
async function processAnnouncement(interaction, announcementData) {
    const { targetRole, title, description, roleMembers, adminId, guildId, guildName } = announcementData;
    announcementCooldowns.set(guildId, Date.now());
    const progressEmbed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, 'Announcement in Progress');
    progressEmbed.setDescription(`${embedBuilder_1.EMOJIS.ACTIONS.ROCKET} **Sending announcements...**\n\n` +
        `${embedBuilder_1.EMOJIS.MISC.CLOCK} Please wait while DMs are being sent to **${roleMembers.length}** members.`)
        .addFields({
        name: `${embedBuilder_1.EMOJIS.ACTIONS.LIGHTNING} Status`,
        value: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Starting...`,
        inline: true
    }, {
        name: `${embedBuilder_1.EMOJIS.ECONOMY.CHART} Progress`,
        value: `0/${roleMembers.length}`,
        inline: true
    })
        .setColor(embedBuilder_1.COLORS.INFO);
    await interaction.editReply({
        embeds: [progressEmbed],
        components: []
    });
    const dmEmbed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, title);
    dmEmbed.setDescription(description)
        .addFields({
        name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Server`,
        value: guildName,
        inline: true
    }, {
        name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Sent`,
        value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
        inline: true
    })
        .setFooter({
        text: `Official announcement from ${guildName} administration`
    });
    let successCount = 0;
    let failureCount = 0;
    const failedUsers = [];
    for (let i = 0; i < roleMembers.length; i++) {
        const member = roleMembers[i];
        try {
            await member.send({ embeds: [dmEmbed] });
            successCount++;
            console.log(`[Announcement] Successfully sent DM to ${member.user.tag} (${member.id})`);
        }
        catch (error) {
            failureCount++;
            failedUsers.push(member.user.tag);
            console.log(`[Announcement] Failed to send DM to ${member.user.tag} (${member.id}):`, error);
        }
        if ((i + 1) % 5 === 0 || i === roleMembers.length - 1) {
            const updatedProgressEmbed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, 'Announcement in Progress');
            updatedProgressEmbed.setDescription(`${embedBuilder_1.EMOJIS.ACTIONS.ROCKET} **Sending announcements...**\n\n` +
                `${embedBuilder_1.EMOJIS.MISC.CLOCK} Progress: **${i + 1}/${roleMembers.length}** members processed`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Successful`,
                value: `${successCount}`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Failed`,
                value: `${failureCount}`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.CHART} Progress`,
                value: `${i + 1}/${roleMembers.length}`,
                inline: true
            })
                .setColor(embedBuilder_1.COLORS.INFO);
            try {
                await interaction.editReply({ embeds: [updatedProgressEmbed] });
            }
            catch (editError) {
                console.error('[Announcement] Failed to update progress:', editError);
            }
        }
        if (i < roleMembers.length - 1) {
            await new Promise(resolve => setTimeout(resolve, DM_DELAY));
        }
    }
    const resultsEmbed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Announcement Complete!');
    resultsEmbed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Announcement delivery finished!**\n\n` +
        `Your message has been sent to members with the **${targetRole.name}** role.`)
        .addFields({
        name: `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Successfully Delivered`,
        value: `**${successCount}** members`,
        inline: true
    }, {
        name: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Failed Deliveries`,
        value: `**${failureCount}** members`,
        inline: true
    }, {
        name: `${embedBuilder_1.EMOJIS.ECONOMY.CHART} Success Rate`,
        value: `**${Math.round((successCount / roleMembers.length) * 100)}%**`,
        inline: true
    }, {
        name: `${embedBuilder_1.EMOJIS.MISC.BOOK} Announcement Title`,
        value: title,
        inline: false
    }, {
        name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Completed`,
        value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
        inline: false
    });
    if (failureCount > 0 && failedUsers.length > 0) {
        const failedList = failedUsers.slice(0, 10).join(', ') + (failedUsers.length > 10 ? ` and ${failedUsers.length - 10} more...` : '');
        resultsEmbed.addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.MAGNIFYING} Failed Recipients`,
            value: failedList,
            inline: false
        });
    }
    resultsEmbed.setFooter({
        text: 'Announcement logged for audit purposes'
    });
    await interaction.editReply({
        embeds: [resultsEmbed],
        components: []
    });
    console.log(`[Announcement] Completed by ${interaction.user.tag} (${adminId}) in ${guildName} (${guildId})`);
    console.log(`[Announcement] Role: ${targetRole.name}, Recipients: ${roleMembers.length}, Success: ${successCount}, Failed: ${failureCount}`);
}
module.exports.processAnnouncement = processAnnouncement;
