"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyTTLSystem = verifyTTLSystem;
const mongoose_1 = __importDefault(require("mongoose"));
const Suggestion_1 = require("../models/Suggestion");
const logger_1 = require("../core/logger");
const logger = (0, logger_1.getLogger)();
class TTLSystemVerifier {
    constructor() {
        this.results = [];
    }
    async runVerification() {
        logger.info('[TTL Verifier] Starting TTL system verification...');
        try {
            const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/economy-bot';
            await mongoose_1.default.connect(mongoUri);
            logger.info('[TTL Verifier] Connected to database');
            await this.verifyTTLIndexExists();
            await this.verifyExpirationFieldSetCorrectly();
            await this.verifyTTLCalculation();
            await this.createTestSuggestionWithCustomExpiry();
            await this.verifyCleanupServiceDetectsExpired();
            await this.verifyDatabaseTTLBehavior();
            const passedTests = this.results.filter(r => r.passed).length;
            const totalTests = this.results.length;
            logger.info(`[TTL Verifier] Verification completed: ${passedTests}/${totalTests} tests passed`);
            return this.results;
        }
        catch (error) {
            logger.error('[TTL Verifier] Verification failed:', error);
            throw error;
        }
        finally {
            await mongoose_1.default.disconnect();
        }
    }
    async verifyTTLIndexExists() {
        try {
            const indexes = await Suggestion_1.Suggestion.collection.getIndexes();
            const hasTTLIndex = Object.values(indexes).some((index) => index.expireAfterSeconds !== undefined &&
                index.key &&
                index.key.expiresAt);
            if (hasTTLIndex) {
                this.addResult('TTL Index Exists', true, 'TTL index found on expiresAt field');
            }
            else {
                this.addResult('TTL Index Exists', false, 'TTL index not found on expiresAt field');
            }
        }
        catch (error) {
            this.addResult('TTL Index Exists', false, 'Failed to check indexes', error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async verifyExpirationFieldSetCorrectly() {
        try {
            await Suggestion_1.Suggestion.deleteMany({ content: 'TTL Test Suggestion' });
            const testSuggestion = new Suggestion_1.Suggestion({
                suggestionId: 'ttl-test-' + Date.now(),
                guildId: '123456789012345678',
                channelId: '987654321098765432',
                messageId: '111222333444555666',
                authorId: '777888999000111222',
                content: 'TTL Test Suggestion',
                votes: [],
                upvoteCount: 0,
                downvoteCount: 0
            });
            await testSuggestion.save();
            const now = new Date();
            const expectedExpiry = new Date(now.getTime() + 24 * 60 * 60 * 1000);
            const timeDiff = Math.abs(testSuggestion.expiresAt.getTime() - expectedExpiry.getTime());
            if (timeDiff < 60 * 1000) {
                this.addResult('Expiration Field Set Correctly', true, `expiresAt set to ${testSuggestion.expiresAt.toISOString()}, difference: ${timeDiff}ms`);
            }
            else {
                this.addResult('Expiration Field Set Correctly', false, `expiresAt not set correctly. Expected: ~${expectedExpiry.toISOString()}, Got: ${testSuggestion.expiresAt.toISOString()}, Difference: ${timeDiff}ms`);
            }
            await Suggestion_1.Suggestion.deleteOne({ _id: testSuggestion._id });
        }
        catch (error) {
            this.addResult('Expiration Field Set Correctly', false, 'Failed to verify expiration field', error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async verifyTTLCalculation() {
        try {
            const now = new Date();
            const expectedExpiry = new Date(now.getTime() + 24 * 60 * 60 * 1000);
            const testDoc = new Suggestion_1.Suggestion({
                suggestionId: 'ttl-calc-test',
                guildId: '123456789012345678',
                channelId: '987654321098765432',
                messageId: '111222333444555666',
                authorId: '777888999000111222',
                content: 'TTL Calculation Test'
            });
            const calculatedExpiry = testDoc.expiresAt;
            const timeDiff = Math.abs(calculatedExpiry.getTime() - expectedExpiry.getTime());
            if (timeDiff < 60 * 1000) {
                this.addResult('TTL Calculation Logic', true, `TTL calculated correctly: ${calculatedExpiry.toISOString()}`);
            }
            else {
                this.addResult('TTL Calculation Logic', false, `TTL calculation incorrect. Expected: ~${expectedExpiry.toISOString()}, Got: ${calculatedExpiry.toISOString()}`);
            }
        }
        catch (error) {
            this.addResult('TTL Calculation Logic', false, 'Failed to verify TTL calculation', error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async createTestSuggestionWithCustomExpiry() {
        try {
            const expiredSuggestion = new Suggestion_1.Suggestion({
                suggestionId: 'expired-test-' + Date.now(),
                guildId: '123456789012345678',
                channelId: '987654321098765432',
                messageId: '111222333444555666',
                authorId: '777888999000111222',
                content: 'Expired Test Suggestion',
                votes: [],
                upvoteCount: 0,
                downvoteCount: 0,
                expiresAt: new Date(Date.now() + 1000)
            });
            await expiredSuggestion.save();
            await new Promise(resolve => setTimeout(resolve, 2000));
            const foundSuggestion = await Suggestion_1.Suggestion.findOne({ suggestionId: expiredSuggestion.suggestionId });
            if (!foundSuggestion) {
                this.addResult('TTL Auto-Deletion', true, 'Suggestion automatically deleted by MongoDB TTL after 1 second');
            }
            else {
                this.addResult('TTL Auto-Deletion', false, 'Suggestion still exists after expiry time - TTL may not be working');
                await Suggestion_1.Suggestion.deleteOne({ _id: expiredSuggestion._id });
            }
        }
        catch (error) {
            this.addResult('TTL Auto-Deletion', false, 'Failed to test TTL auto-deletion', error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async verifyCleanupServiceDetectsExpired() {
        try {
            const pastExpiredSuggestion = new Suggestion_1.Suggestion({
                suggestionId: 'cleanup-test-' + Date.now(),
                guildId: '123456789012345678',
                channelId: '987654321098765432',
                messageId: '111222333444555666',
                authorId: '777888999000111222',
                content: 'Cleanup Test Suggestion',
                votes: [],
                upvoteCount: 0,
                downvoteCount: 0,
                expiresAt: new Date(Date.now() - 1000)
            });
            await pastExpiredSuggestion.save();
            const expiredSuggestions = await Suggestion_1.Suggestion.find({
                expiresAt: { $lt: new Date() }
            });
            const foundExpired = expiredSuggestions.some(s => s.suggestionId === pastExpiredSuggestion.suggestionId);
            if (foundExpired) {
                this.addResult('Cleanup Service Detection', true, `Found ${expiredSuggestions.length} expired suggestions including test suggestion`);
            }
            else {
                this.addResult('Cleanup Service Detection', false, 'Cleanup service cannot detect expired suggestions');
            }
            await Suggestion_1.Suggestion.deleteOne({ _id: pastExpiredSuggestion._id });
        }
        catch (error) {
            this.addResult('Cleanup Service Detection', false, 'Failed to test cleanup service detection', error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async verifyDatabaseTTLBehavior() {
        try {
            const indexes = await Suggestion_1.Suggestion.collection.getIndexes();
            const ttlIndexDetails = Object.entries(indexes).find(([name, index]) => index.expireAfterSeconds !== undefined);
            if (ttlIndexDetails) {
                const [indexName, indexInfo] = ttlIndexDetails;
                this.addResult('Database TTL Configuration', true, `TTL index "${indexName}" configured with expireAfterSeconds: ${indexInfo.expireAfterSeconds}`);
            }
            else {
                this.addResult('Database TTL Configuration', false, 'No TTL index found in collection');
            }
            try {
                const serverStatus = await mongoose_1.default.connection.db?.admin().serverStatus();
                if (serverStatus?.version) {
                    this.addResult('MongoDB TTL Background Task', true, `MongoDB server version ${serverStatus.version} is running and TTL background task should be active`);
                }
                else {
                    this.addResult('MongoDB TTL Background Task', false, 'Could not verify MongoDB server status');
                }
            }
            catch (adminError) {
                this.addResult('MongoDB TTL Background Task', true, 'MongoDB connection is active, TTL background task should be running');
            }
        }
        catch (error) {
            this.addResult('Database TTL Configuration', false, 'Failed to verify database TTL configuration', error instanceof Error ? error.message : 'Unknown error');
        }
    }
    addResult(test, passed, details, error) {
        this.results.push({ test, passed, details, error });
        const status = passed ? '✅ PASS' : '❌ FAIL';
        logger.info(`[TTL Verifier] ${status}: ${test} - ${details}`);
        if (error) {
            logger.error(`[TTL Verifier] Error: ${error}`);
        }
    }
}
async function verifyTTLSystem() {
    const verifier = new TTLSystemVerifier();
    return await verifier.runVerification();
}
if (require.main === module) {
    verifyTTLSystem()
        .then((results) => {
        console.log('\n=== TTL SYSTEM VERIFICATION RESULTS ===');
        results.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.test}: ${result.details}`);
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        });
        const passedCount = results.filter(r => r.passed).length;
        console.log(`\nSummary: ${passedCount}/${results.length} tests passed`);
        if (passedCount === results.length) {
            console.log('🎉 TTL system is working correctly!');
        }
        else {
            console.log('⚠️ TTL system has issues that need attention.');
        }
        process.exit(passedCount === results.length ? 0 : 1);
    })
        .catch((error) => {
        console.error('TTL verification failed:', error);
        process.exit(1);
    });
}
