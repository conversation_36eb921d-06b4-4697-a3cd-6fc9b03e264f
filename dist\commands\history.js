"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const economyService_1 = require("../services/economyService");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
function getTransactionEmoji(type, amount) {
    switch (type) {
        case 'pay':
            return amount > 0 ? embedBuilder_1.EMOJIS.ECONOMY.MONEY : embedBuilder_1.EMOJIS.ACTIONS.LIGHTNING;
        case 'buyrole':
            return embedBuilder_1.EMOJIS.ROLES.MEDAL;
        case 'give':
            return embedBuilder_1.EMOJIS.SUCCESS.STAR;
        case 'fine':
            return embedBuilder_1.EMOJIS.ADMIN.WARNING;
        case 'reaction':
            return embedBuilder_1.EMOJIS.SUCCESS.THUMBS_UP;
        default:
            return embedBuilder_1.EMOJIS.ECONOMY.COINS;
    }
}
function capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('history')
        .setDescription('Show your recent transaction history'),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        try {
            const discordId = interaction.user.id;
            const guildId = interaction.guild?.id;
            if (!guildId) {
                throw new Error('This command can only be used in a server');
            }
            const transactions = await (0, economyService_1.getTransactionHistory)(discordId, guildId, 10);
            if (transactions.length === 0) {
                const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Transaction History');
                embed.setDescription(`${embedBuilder_1.EMOJIS.MISC.SCROLL} No transaction history found.\n\nStart earning coins to see your transactions here!`)
                    .setFooter({ text: 'Use /help to see ways to earn coins' });
                (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            const transactionFields = await Promise.all(transactions.map(async (tx, index) => {
                const sign = tx.amount > 0 ? '+' : '';
                const emoji = getTransactionEmoji(tx.type, tx.amount);
                const timestamp = `<t:${Math.floor(new Date(tx.timestamp).getTime() / 1000)}:R>`;
                const formattedAmount = await (0, embedBuilder_1.formatServerCoins)(guildId, Math.abs(tx.amount));
                return {
                    name: `${emoji} ${capitalizeFirst(tx.type)} Transaction`,
                    value: `**Amount:** ${sign}${formattedAmount}\n` +
                        `**Details:** ${tx.details || 'No details'}\n` +
                        `**Time:** ${timestamp}`,
                    inline: index % 2 === 0
                };
            }));
            const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Your Transaction History');
            embed.setDescription(`${embedBuilder_1.EMOJIS.MISC.BOOK} **Recent Activity** (Last 10 transactions)\n\n${embedBuilder_1.EMOJIS.ECONOMY.CHART} Track your coin earnings and spending below:`)
                .addFields(...transactionFields)
                .setFooter({
                text: `Showing ${transactions.length} most recent transactions • Private to you`
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });
        }
        catch (error) {
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            throw new errorHandler_1.DatabaseError('Failed to fetch transaction history.');
        }
    })
};
