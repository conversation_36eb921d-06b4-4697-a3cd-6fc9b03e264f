"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getIncomeGuideText = getIncomeGuideText;
exports.setIncomeGuideText = setIncomeGuideText;
exports.hasCustomIncomeGuide = hasCustomIncomeGuide;
exports.removeCustomIncomeGuide = removeCustomIncomeGuide;
exports.getDefaultIncomeGuideText = getDefaultIncomeGuideText;
const IncomeGuide_1 = __importDefault(require("../models/IncomeGuide"));
const errorHandler_1 = require("../utils/errorHandler");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
function formatTextForDiscord(text) {
    let formatted = text.replace(/\\n/g, '\n');
    formatted = formatted.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    formatted = formatted.replace(/\n{3,}/g, '\n\n');
    formatted = formatted.trim();
    return formatted;
}
function processUserInput(rawText) {
    let processed = rawText.replace(/\\n/g, '\n');
    processed = processed.replace(/\\\\n/g, '\n');
    processed = processed.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    processed = processed.replace(/[ \t]+$/gm, '');
    processed = processed.replace(/\n{4,}/g, '\n\n\n');
    return processed.trim();
}
async function generateDefaultIncomeGuide(guildId) {
    const coinName = await configurableConstants_1.default.getCoinName(guildId);
    return [
        `💰 **Ways to Earn ${coinName}**`,
        '⏰ **Playing Time**',
        '• 10 hours/week = 150 Coins',
        '🔨 **Material Contributions**',
        '• Stack of Iron Ingots = 20 Coins',
        '• Stack of Gold = 10 Coins',
        '• Stack of Diamonds = 100 Coins',
        '🏰 **Territory Capturing**',
        '• Capture 1 node = 100 Coins',
        '• Help conquer a town = 300 Coins',
        '⚔️ **War Achievements**',
        '• Each kill during war = 15 Coins',
        '🤝 **Recruitment**',
        '• Active invite for 1 week = 500 Coins',
        '📝 **Important Note**',
        'To claim your earnings, you must open a ticket in #tickets channel!'
    ].join('\n');
}
async function getIncomeGuideText(guildId) {
    try {
        const incomeGuide = await IncomeGuide_1.default.findOne({ guildId });
        if (incomeGuide?.customText) {
            return formatTextForDiscord(incomeGuide.customText);
        }
        return await generateDefaultIncomeGuide(guildId);
    }
    catch (error) {
        console.error(`[IncomeGuideService] Error fetching income guide for guild ${guildId}:`, error);
        return await generateDefaultIncomeGuide(guildId);
    }
}
async function setIncomeGuideText(guildId, customText) {
    try {
        const processedText = processUserInput(customText);
        const incomeGuide = await IncomeGuide_1.default.findOneAndUpdate({ guildId }, { customText: processedText }, {
            new: true,
            upsert: true,
            runValidators: true
        });
        if (!incomeGuide) {
            throw new Error('Failed to create or update income guide');
        }
        console.log(`[IncomeGuideService] Updated income guide for guild ${guildId}`);
        console.log(`[IncomeGuideService] Processed text length: ${processedText.length} characters`);
        return incomeGuide;
    }
    catch (error) {
        console.error(`[IncomeGuideService] Error setting income guide for guild ${guildId}:`, error);
        throw new errorHandler_1.DatabaseError('Failed to update income guide text', error);
    }
}
async function hasCustomIncomeGuide(guildId) {
    try {
        const incomeGuide = await IncomeGuide_1.default.findOne({ guildId });
        return !!incomeGuide;
    }
    catch (error) {
        console.error(`[IncomeGuideService] Error checking custom income guide for guild ${guildId}:`, error);
        return false;
    }
}
async function removeCustomIncomeGuide(guildId) {
    try {
        const result = await IncomeGuide_1.default.deleteOne({ guildId });
        console.log(`[IncomeGuideService] Removed custom income guide for guild ${guildId}`);
        return result.deletedCount > 0;
    }
    catch (error) {
        console.error(`[IncomeGuideService] Error removing income guide for guild ${guildId}:`, error);
        throw new errorHandler_1.DatabaseError('Failed to remove custom income guide text', error);
    }
}
async function getDefaultIncomeGuideText(guildId) {
    return await generateDefaultIncomeGuide(guildId);
}
