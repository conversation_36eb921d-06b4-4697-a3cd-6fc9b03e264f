"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuggestionConfig = void 0;
const mongoose_1 = require("mongoose");
const suggestionConfigSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        unique: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    enabled: {
        type: Boolean,
        default: false,
        required: true
    },
    channelId: {
        type: String,
        required: function () {
            return this.enabled;
        },
        validate: {
            validator: function (v) {
                return !v || (v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Channel ID must be a valid Discord snowflake'
        }
    },
    allowedRoles: {
        type: [String],
        default: [],
        validate: {
            validator: function (roles) {
                return roles.every(roleId => /^\d{17,20}$/.test(roleId));
            },
            message: 'All role IDs must be valid Discord snowflakes'
        }
    }
}, {
    timestamps: true
});
suggestionConfigSchema.index({ guildId: 1 });
exports.SuggestionConfig = (0, mongoose_1.model)('SuggestionConfig', suggestionConfigSchema);
exports.default = exports.SuggestionConfig;
