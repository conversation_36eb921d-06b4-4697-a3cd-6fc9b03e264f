"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeSecurityService = void 0;
const BaseService_1 = require("../base/BaseService");
const constants_1 = require("../../config/constants");
const models_1 = require("../../models");
class TradeSecurityService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'TradeSecurityService';
        this.rateLimitCache = new Map();
        this.suspiciousActivityCache = new Map();
        this.MAX_CACHE_SIZE = 1000;
        this.CACHE_CLEANUP_INTERVAL = 15 * 60 * 1000;
    }
    async onInitialize() {
        this.logger.info('[TradeSecurityService] Trade security service initialized');
        this.cacheCleanupTimer = setInterval(() => {
            this.performCacheCleanup();
        }, this.CACHE_CLEANUP_INTERVAL);
    }
    async onShutdown() {
        if (this.cacheCleanupTimer) {
            clearInterval(this.cacheCleanupTimer);
            this.cacheCleanupTimer = undefined;
        }
        this.rateLimitCache.clear();
        this.suspiciousActivityCache.clear();
        this.logger.info('[TradeSecurityService] Trade security service shutdown complete');
    }
    async checkTradeRateLimit(discordId, guildId) {
        this.logOperation('Checking trade rate limit', { discordId, guildId });
        try {
            const userStats = await models_1.UserTradeStats.findOne({ discordId, guildId });
            if (userStats) {
                userStats.resetDailyCountIfNeeded();
                if (userStats.dailyTradeCount >= constants_1.TRADE.MAX_TRADE_PROPOSALS_PER_DAY) {
                    return {
                        allowed: false,
                        reason: `Daily trade limit of ${constants_1.TRADE.MAX_TRADE_PROPOSALS_PER_DAY} reached`,
                        resetTime: this.getNextMidnight()
                    };
                }
                if (userStats.activeTrades >= constants_1.TRADE.MAX_ACTIVE_TRADES_PER_USER) {
                    return {
                        allowed: false,
                        reason: `Maximum of ${constants_1.TRADE.MAX_ACTIVE_TRADES_PER_USER} active trades allowed`
                    };
                }
            }
            const rateLimitKey = `${discordId}:${guildId}`;
            const rateLimitData = this.rateLimitCache.get(rateLimitKey);
            if (rateLimitData) {
                const now = new Date();
                const timeSinceLastRequest = now.getTime() - rateLimitData.lastRequest.getTime();
                if (timeSinceLastRequest < constants_1.TRADE.TRADE_COOLDOWN_SECONDS * 1000) {
                    const remainingSeconds = Math.ceil((constants_1.TRADE.TRADE_COOLDOWN_SECONDS * 1000 - timeSinceLastRequest) / 1000);
                    return {
                        allowed: false,
                        reason: `Please wait ${remainingSeconds} seconds before creating another trade`,
                        resetTime: new Date(rateLimitData.lastRequest.getTime() + constants_1.TRADE.TRADE_COOLDOWN_SECONDS * 1000)
                    };
                }
            }
            this.rateLimitCache.set(rateLimitKey, {
                count: (rateLimitData?.count || 0) + 1,
                resetTime: this.getNextMidnight(),
                lastRequest: new Date()
            });
            return { allowed: true };
        }
        catch (error) {
            this.handleError(error, { operation: 'check_trade_rate_limit', discordId, guildId });
            throw error;
        }
    }
    async performSecurityCheck(trade) {
        this.logOperation('Performing security check', { tradeId: trade.tradeId });
        try {
            const violations = [];
            const recommendations = [];
            let riskScore = 0;
            const sellerRisk = await this.getUserRiskProfile(trade.sellerId, trade.guildId);
            const buyerRisk = await this.getUserRiskProfile(trade.buyerId, trade.guildId);
            if (sellerRisk.riskLevel === 'HIGH' || sellerRisk.riskLevel === 'CRITICAL') {
                violations.push(`Seller has ${sellerRisk.riskLevel} risk profile`);
                riskScore += sellerRisk.riskLevel === 'CRITICAL' ? 40 : 25;
            }
            if (buyerRisk.riskLevel === 'HIGH' || buyerRisk.riskLevel === 'CRITICAL') {
                violations.push(`Buyer has ${buyerRisk.riskLevel} risk profile`);
                riskScore += buyerRisk.riskLevel === 'CRITICAL' ? 40 : 25;
            }
            const suspiciousPatterns = await this.detectSuspiciousPatterns(trade);
            violations.push(...suspiciousPatterns.violations);
            riskScore += suspiciousPatterns.riskScore;
            const amountCheck = this.checkTradeAmount(trade);
            violations.push(...amountCheck.violations);
            riskScore += amountCheck.riskScore;
            const rapidTradingCheck = await this.checkRapidTrading(trade);
            violations.push(...rapidTradingCheck.violations);
            riskScore += rapidTradingCheck.riskScore;
            if (riskScore > 30) {
                recommendations.push('Consider manual review before approval');
            }
            if (riskScore > 50) {
                recommendations.push('Require additional verification');
            }
            if (riskScore > 70) {
                recommendations.push('Flag for immediate admin attention');
            }
            let riskLevel;
            if (riskScore >= 80)
                riskLevel = 'CRITICAL';
            else if (riskScore >= 60)
                riskLevel = 'HIGH';
            else if (riskScore >= 30)
                riskLevel = 'MEDIUM';
            else
                riskLevel = 'LOW';
            const passed = riskLevel !== 'CRITICAL' && violations.length === 0;
            this.logOperation('Security check completed', {
                tradeId: trade.tradeId,
                riskLevel,
                riskScore,
                violationCount: violations.length,
                passed
            });
            return {
                passed,
                violations,
                riskLevel,
                recommendations
            };
        }
        catch (error) {
            this.handleError(error, { operation: 'perform_security_check', tradeId: trade.tradeId });
            throw error;
        }
    }
    async getUserRiskProfile(discordId, guildId) {
        try {
            const userStats = await models_1.UserTradeStats.findOne({ discordId, guildId });
            if (!userStats) {
                return {
                    discordId,
                    riskLevel: 'LOW',
                    factors: ['New user'],
                    score: 10,
                    lastUpdated: new Date()
                };
            }
            const factors = [];
            let score = 0;
            if (userStats.disputeRatio > constants_1.TRADE.DISPUTE_RATIO_THRESHOLD) {
                factors.push(`High dispute ratio: ${(userStats.disputeRatio * 100).toFixed(1)}%`);
                score += 30;
            }
            if (userStats.completionRate < 0.8 && userStats.totalTrades >= 5) {
                factors.push(`Low completion rate: ${(userStats.completionRate * 100).toFixed(1)}%`);
                score += 20;
            }
            const recentViolations = userStats.violationHistory.filter(v => {
                const violationDate = new Date(v.split(']')[0].substring(1));
                const daysSince = (Date.now() - violationDate.getTime()) / (1000 * 60 * 60 * 24);
                return daysSince <= 30;
            });
            if (recentViolations.length > 0) {
                factors.push(`${recentViolations.length} violations in last 30 days`);
                score += recentViolations.length * 10;
            }
            if (userStats.isRestricted) {
                factors.push('Currently restricted');
                score += 50;
            }
            let riskLevel;
            if (score >= 80)
                riskLevel = 'CRITICAL';
            else if (score >= 60)
                riskLevel = 'HIGH';
            else if (score >= 30)
                riskLevel = 'MEDIUM';
            else
                riskLevel = 'LOW';
            return {
                discordId,
                riskLevel,
                factors,
                score,
                lastUpdated: new Date()
            };
        }
        catch (error) {
            this.handleError(error, { operation: 'get_user_risk_profile', discordId, guildId });
            throw error;
        }
    }
    async applyAutomaticRestrictions(discordId, guildId) {
        this.logOperation('Checking for automatic restrictions', { discordId, guildId });
        try {
            const userStats = await models_1.UserTradeStats.findOne({ discordId, guildId });
            if (!userStats || userStats.isRestricted) {
                return false;
            }
            let shouldRestrict = false;
            let restrictionReason = '';
            if (userStats.totalTrades >= constants_1.TRADE.MIN_TRADES_FOR_REPUTATION &&
                userStats.disputeRatio > constants_1.TRADE.DISPUTE_RATIO_THRESHOLD) {
                shouldRestrict = true;
                restrictionReason = `High dispute ratio: ${(userStats.disputeRatio * 100).toFixed(1)}%`;
            }
            const recentViolations = userStats.violationHistory.filter(v => {
                const violationDate = new Date(v.split(']')[0].substring(1));
                const daysSince = (Date.now() - violationDate.getTime()) / (1000 * 60 * 60 * 24);
                return daysSince <= 7;
            });
            if (recentViolations.length >= 3) {
                shouldRestrict = true;
                restrictionReason = `Multiple recent violations: ${recentViolations.length} in 7 days`;
            }
            if (shouldRestrict) {
                const restrictedUntil = new Date();
                restrictedUntil.setDate(restrictedUntil.getDate() + constants_1.TRADE.TRADE_RESTRICTION_DAYS);
                userStats.isRestricted = true;
                userStats.restrictionReason = `Automatic restriction: ${restrictionReason}`;
                userStats.restrictedUntil = restrictedUntil;
                userStats.violationHistory.push(`[${new Date().toISOString()}] Automatic restriction applied: ${restrictionReason}`);
                await userStats.save();
                this.logOperation('Automatic restriction applied', {
                    discordId,
                    guildId,
                    reason: restrictionReason,
                    until: restrictedUntil
                });
                return true;
            }
            return false;
        }
        catch (error) {
            this.handleError(error, { operation: 'apply_automatic_restrictions', discordId, guildId });
            throw error;
        }
    }
    async detectSuspiciousPatterns(trade) {
        const violations = [];
        let riskScore = 0;
        try {
            const recentTrades = await models_1.Trade.find({
                $or: [
                    { sellerId: trade.sellerId, buyerId: trade.buyerId },
                    { sellerId: trade.buyerId, buyerId: trade.sellerId }
                ],
                guildId: trade.guildId,
                createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
                tradeId: { $ne: trade.tradeId }
            });
            if (recentTrades.length > 2) {
                violations.push(`Multiple trades between same parties: ${recentTrades.length} in 24h`);
                riskScore += 20;
            }
            if (trade.amount % 1000 === 0 && trade.amount >= 5000) {
                violations.push('Suspiciously round trade amount');
                riskScore += 10;
            }
            if (trade.amount > 50000) {
                violations.push('Very high trade amount');
                riskScore += 15;
            }
        }
        catch (error) {
            this.logger.warn('Error detecting suspicious patterns', { error, tradeId: trade.tradeId });
        }
        return { violations, riskScore };
    }
    checkTradeAmount(trade) {
        const violations = [];
        let riskScore = 0;
        if (trade.amount < 10) {
            violations.push('Extremely low trade amount');
            riskScore += 5;
        }
        if (trade.amount >= 1000 && trade.amount % 1000 === 0) {
            riskScore += 5;
        }
        return { violations, riskScore };
    }
    async checkRapidTrading(trade) {
        const violations = [];
        let riskScore = 0;
        try {
            const sellerRecentTrades = await models_1.Trade.find({
                sellerId: trade.sellerId,
                guildId: trade.guildId,
                createdAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) },
                tradeId: { $ne: trade.tradeId }
            });
            if (sellerRecentTrades.length > 5) {
                violations.push(`Seller created ${sellerRecentTrades.length} trades in the last hour`);
                riskScore += 20;
            }
            const buyerRecentTrades = await models_1.Trade.find({
                buyerId: trade.buyerId,
                guildId: trade.guildId,
                createdAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) },
                tradeId: { $ne: trade.tradeId }
            });
            if (buyerRecentTrades.length > 5) {
                violations.push(`Buyer created ${buyerRecentTrades.length} trades in the last hour`);
                riskScore += 20;
            }
        }
        catch (error) {
            this.logger.warn('Error checking rapid trading', { error, tradeId: trade.tradeId });
        }
        return { violations, riskScore };
    }
    performCacheCleanup() {
        const now = new Date();
        let rateLimitCleaned = 0;
        let suspiciousCleaned = 0;
        const rateLimitEntries = Array.from(this.rateLimitCache.entries());
        for (const [key, data] of rateLimitEntries) {
            if (now > data.resetTime) {
                this.rateLimitCache.delete(key);
                rateLimitCleaned++;
            }
        }
        const suspiciousEntries = Array.from(this.suspiciousActivityCache.entries());
        for (const [key, data] of suspiciousEntries) {
            const hoursSinceLastActivity = (now.getTime() - data.lastActivity.getTime()) / (1000 * 60 * 60);
            if (hoursSinceLastActivity > 24) {
                this.suspiciousActivityCache.delete(key);
                suspiciousCleaned++;
            }
        }
        if (this.rateLimitCache.size > this.MAX_CACHE_SIZE) {
            const entries = Array.from(this.rateLimitCache.entries());
            entries.sort((a, b) => a[1].lastRequest.getTime() - b[1].lastRequest.getTime());
            const toDelete = entries.slice(0, entries.length - this.MAX_CACHE_SIZE);
            toDelete.forEach(([key]) => this.rateLimitCache.delete(key));
            rateLimitCleaned += toDelete.length;
        }
        if (this.suspiciousActivityCache.size > this.MAX_CACHE_SIZE) {
            const entries = Array.from(this.suspiciousActivityCache.entries());
            entries.sort((a, b) => a[1].lastActivity.getTime() - b[1].lastActivity.getTime());
            const toDelete = entries.slice(0, entries.length - this.MAX_CACHE_SIZE);
            toDelete.forEach(([key]) => this.suspiciousActivityCache.delete(key));
            suspiciousCleaned += toDelete.length;
        }
        if (rateLimitCleaned > 0 || suspiciousCleaned > 0) {
            this.logger.debug('[TradeSecurityService] Cache cleanup completed', {
                rateLimitCleaned,
                suspiciousCleaned,
                rateLimitCacheSize: this.rateLimitCache.size,
                suspiciousCacheSize: this.suspiciousActivityCache.size
            });
        }
    }
    getNextMidnight() {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        return tomorrow;
    }
}
exports.TradeSecurityService = TradeSecurityService;
