/**
 * Direct Candidate Specification System Test
 * Tests the new robust candidate specification approach that replaces modal-based registration
 */

require('dotenv').config();

class DirectCandidateSpecificationTestSuite {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : '🔍';
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  recordTest(testName, passed, details = '') {
    this.testResults.total++;
    if (passed) {
      this.testResults.passed++;
      this.log(`${testName}: PASSED ${details}`, 'success');
    } else {
      this.testResults.failed++;
      this.log(`${testName}: FAILED ${details}`, 'error');
    }
    this.testResults.details.push({ testName, passed, details });
  }

  async testCandidateRegistrationRemoval() {
    this.log('🧪 Testing Candidate Registration System Removal...');
    
    try {
      const fs = require('fs');
      
      // Test 1: Become Candidate button removed from embed builder
      const embedBuilderContent = fs.readFileSync('./dist/utils/electionEmbedBuilder.js', 'utf8');
      const noBecomeButton = !embedBuilderContent.includes('Become Candidate') &&
                            !embedBuilderContent.includes('election_candidate_');
      this.recordTest('Become Candidate Button Removed', noBecomeButton,
        'Election embeds no longer include Become Candidate button');

      // Test 2: Modal submission routing removed
      const interactionCreateContent = fs.readFileSync('./dist/events/interactionCreate.js', 'utf8');
      const noModalRouting = !interactionCreateContent.includes('candidate_register_') ||
                            !interactionCreateContent.includes('handleCandidateRegistrationModal');
      this.recordTest('Modal Submission Routing Removed', noModalRouting,
        'InteractionCreate no longer routes candidate registration modals');

      // Test 3: Button handler methods removed
      const buttonHandlerContent = fs.readFileSync('./dist/handlers/electionButtonHandler.js', 'utf8');
      const noHandlerMethods = !buttonHandlerContent.includes('handleBecomeCandidate') &&
                              !buttonHandlerContent.includes('handleCandidateRegistrationModal');
      this.recordTest('Button Handler Methods Removed', noHandlerMethods,
        'ElectionButtonHandler no longer has candidate registration methods');

      // Test 4: Updated voting instructions
      const updatedInstructions = embedBuilderContent.includes('How to Vote') &&
                                 !embedBuilderContent.includes('Become Candidate');
      this.recordTest('Updated Voting Instructions', updatedInstructions,
        'Election embeds show updated instructions without candidate registration');

      return true;
    } catch (error) {
      this.recordTest('Candidate Registration Removal', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testCandidatesParameterImplementation() {
    this.log('🧪 Testing Candidates Parameter Implementation...');
    
    try {
      const fs = require('fs');
      const electionsCommandContent = fs.readFileSync('./dist/commands/election/ElectionsCommand.js', 'utf8');
      
      // Test 1: Candidates parameter added to command
      const hasCandidatesParameter = electionsCommandContent.includes('candidates') &&
                                    electionsCommandContent.includes('Specify candidates by mentioning users');
      this.recordTest('Candidates Parameter Added', hasCandidatesParameter,
        'Elections command includes candidates parameter with proper description');

      // Test 2: Candidates string extraction
      const hasCandidatesExtraction = electionsCommandContent.includes('getString') &&
                                     electionsCommandContent.includes('candidates');
      this.recordTest('Candidates String Extraction', hasCandidatesExtraction,
        'Command extracts candidates parameter from interaction options');

      // Test 3: User mention parsing logic
      const hasUserMentionParsing = electionsCommandContent.includes('userMentionRegex') ||
                                   electionsCommandContent.includes('<@!?');
      this.recordTest('User Mention Parsing Logic', hasUserMentionParsing,
        'Command includes logic to parse user mentions from candidates string');

      // Test 4: Candidate processing method
      const hasCandidateProcessing = electionsCommandContent.includes('processCandidates');
      this.recordTest('Candidate Processing Method', hasCandidateProcessing,
        'Command includes processCandidates method for handling candidate specification');

      // Test 5: Maximum candidate limit
      const hasMaxCandidateLimit = electionsCommandContent.includes('20') &&
                                  electionsCommandContent.includes('Maximum');
      this.recordTest('Maximum Candidate Limit', hasMaxCandidateLimit,
        'Command enforces maximum of 20 candidates per election');

      return true;
    } catch (error) {
      this.recordTest('Candidates Parameter Implementation', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testElectionCreationIntegration() {
    this.log('🧪 Testing Election Creation Integration...');
    
    try {
      const fs = require('fs');
      const electionsCommandContent = fs.readFileSync('./dist/commands/election/ElectionsCommand.js', 'utf8');
      
      // Test 1: Candidate processing after election creation
      const hasPostCreationProcessing = electionsCommandContent.includes('processCandidates') &&
                                       electionsCommandContent.includes('electionId');
      this.recordTest('Post-Creation Candidate Processing', hasPostCreationProcessing,
        'Candidates are processed after election creation with election ID');

      // Test 2: Candidate eligibility validation
      const hasEligibilityValidation = electionsCommandContent.includes('canUserBeCandidate');
      this.recordTest('Candidate Eligibility Validation', hasEligibilityValidation,
        'Command validates candidate eligibility before adding to election');

      // Test 3: Guild member fetching
      const hasGuildMemberFetch = electionsCommandContent.includes('guild.members.fetch') ||
                                 electionsCommandContent.includes('members.fetch');
      this.recordTest('Guild Member Fetching', hasGuildMemberFetch,
        'Command fetches guild members to validate candidate existence');

      // Test 4: Error handling for invalid candidates
      const hasErrorHandling = electionsCommandContent.includes('errors') &&
                              electionsCommandContent.includes('Failed to add');
      this.recordTest('Invalid Candidate Error Handling', hasErrorHandling,
        'Command handles errors for invalid or ineligible candidates');

      // Test 5: Successful candidate addition
      const hasSuccessfulAddition = electionsCommandContent.includes('addCandidate') &&
                                   electionsCommandContent.includes('displayName');
      this.recordTest('Successful Candidate Addition', hasSuccessfulAddition,
        'Command calls ElectionService.addCandidate for valid candidates');

      return true;
    } catch (error) {
      this.recordTest('Election Creation Integration', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testEmbedDisplayUpdates() {
    this.log('🧪 Testing Embed Display Updates...');
    
    try {
      const fs = require('fs');
      
      // Test 1: Candidates passed to embed creation
      const electionsCommandContent = fs.readFileSync('./dist/commands/election/ElectionsCommand.js', 'utf8');
      const hasCandidatesInEmbed = electionsCommandContent.includes('createElectionEmbed') &&
                                  electionsCommandContent.includes('candidates');
      this.recordTest('Candidates Passed to Embed', hasCandidatesInEmbed,
        'Election embed creation receives candidates array');

      // Test 2: Button creation with candidates
      const hasButtonsWithCandidates = electionsCommandContent.includes('createElectionButtons') &&
                                      electionsCommandContent.includes('candidates');
      this.recordTest('Buttons Created with Candidates', hasButtonsWithCandidates,
        'Election buttons are created with candidates for voting');

      // Test 3: User candidate status check
      const hasUserCandidateCheck = electionsCommandContent.includes('isUserCandidate') ||
                                   electionsCommandContent.includes('some(c => c.discordId');
      this.recordTest('User Candidate Status Check', hasUserCandidateCheck,
        'Command checks if command user is among the candidates');

      // Test 4: Statistics with candidate count
      const hasStatsWithCandidates = electionsCommandContent.includes('totalCandidates') &&
                                    electionsCommandContent.includes('candidates.length');
      this.recordTest('Statistics with Candidate Count', hasStatsWithCandidates,
        'Election statistics include accurate candidate counts');

      // Test 5: Updated embed messaging
      const embedBuilderContent = fs.readFileSync('./dist/utils/electionEmbedBuilder.js', 'utf8');
      const hasUpdatedMessaging = embedBuilderContent.includes('No candidates specified') ||
                                 !embedBuilderContent.includes('Be the first to join');
      this.recordTest('Updated Embed Messaging', hasUpdatedMessaging,
        'Election embeds show appropriate messaging for direct candidate specification');

      return true;
    } catch (error) {
      this.recordTest('Embed Display Updates', false, `Error: ${error.message}`);
      return false;
    }
  }

  async testBackwardCompatibility() {
    this.log('🧪 Testing Backward Compatibility...');
    
    try {
      const fs = require('fs');
      const electionsCommandContent = fs.readFileSync('./dist/commands/election/ElectionsCommand.js', 'utf8');
      
      // Test 1: Optional candidates parameter
      const hasOptionalParameter = electionsCommandContent.includes('setRequired(false)') &&
                                   electionsCommandContent.includes('candidates');
      this.recordTest('Optional Candidates Parameter', hasOptionalParameter,
        'Candidates parameter is optional for backward compatibility');

      // Test 2: Empty candidates handling
      const hasEmptyHandling = electionsCommandContent.includes('if (candidatesString)') ||
                              electionsCommandContent.includes('candidatesString ?');
      this.recordTest('Empty Candidates Handling', hasEmptyHandling,
        'Command handles elections without specified candidates');

      // Test 3: Existing election features preserved
      const electionServiceContent = fs.readFileSync('./dist/services/election/ElectionService.js', 'utf8');
      const hasExistingFeatures = electionServiceContent.includes('castVote') &&
                                 electionServiceContent.includes('endElection') &&
                                 electionServiceContent.includes('addCandidate');
      this.recordTest('Existing Election Features Preserved', hasExistingFeatures,
        'All existing election functionality remains available');

      // Test 4: Voting system unchanged
      const buttonHandlerContent = fs.readFileSync('./dist/handlers/electionButtonHandler.js', 'utf8');
      const hasVotingSystem = buttonHandlerContent.includes('handleVote') &&
                             buttonHandlerContent.includes('castVote');
      this.recordTest('Voting System Unchanged', hasVotingSystem,
        'Vote casting and handling remains fully functional');

      // Test 5: Election ending preserved
      const hasElectionEnding = buttonHandlerContent.includes('handleEndElection') &&
                               buttonHandlerContent.includes('endElection');
      this.recordTest('Election Ending Preserved', hasElectionEnding,
        'Election ending functionality remains intact');

      return true;
    } catch (error) {
      this.recordTest('Backward Compatibility', false, `Error: ${error.message}`);
      return false;
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Direct Candidate Specification System Testing\n');
    
    const testSuites = [
      { name: 'Candidate Registration Removal', test: () => this.testCandidateRegistrationRemoval() },
      { name: 'Candidates Parameter Implementation', test: () => this.testCandidatesParameterImplementation() },
      { name: 'Election Creation Integration', test: () => this.testElectionCreationIntegration() },
      { name: 'Embed Display Updates', test: () => this.testEmbedDisplayUpdates() },
      { name: 'Backward Compatibility', test: () => this.testBackwardCompatibility() }
    ];

    for (const suite of testSuites) {
      try {
        await suite.test();
      } catch (error) {
        this.recordTest(suite.name, false, `Suite error: ${error.message}`);
      }
      console.log(''); // Add spacing between test suites
    }

    this.generateFinalReport();
  }

  generateFinalReport() {
    console.log('📊 DIRECT CANDIDATE SPECIFICATION SYSTEM TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.testResults.total}`);
    console.log(`Passed: ${this.testResults.passed}`);
    console.log(`Failed: ${this.testResults.failed}`);
    console.log(`Success Rate: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);
    console.log('');

    if (this.testResults.failed > 0) {
      console.log('❌ FAILED TESTS:');
      this.testResults.details
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`  - ${test.testName}: ${test.details}`);
        });
      console.log('');
    }

    const isSystemReady = this.testResults.failed === 0;
    
    if (isSystemReady) {
      console.log('🎉 DIRECT CANDIDATE SPECIFICATION SYSTEM: FULLY OPERATIONAL');
      console.log('✅ Modal-based registration system completely removed');
      console.log('✅ Candidates parameter added to /elections command');
      console.log('✅ User mention parsing and validation implemented');
      console.log('✅ Automatic candidate registration during election creation');
      console.log('✅ Election embeds updated for direct specification');
      console.log('✅ Backward compatibility maintained');
      console.log('');
      console.log('🚀 The new robust candidate specification system is ready for production!');
      console.log('');
      console.log('📝 USAGE EXAMPLE:');
      console.log('/elections title:"Server Admin Election" roles_to_ping:@everyone eligible_voter_roles:@Members eligible_candidate_roles:@Trusted candidates:@user1 @user2 @user3');
    } else {
      console.log('⚠️  DIRECT CANDIDATE SPECIFICATION SYSTEM: REQUIRES ATTENTION');
      console.log('Some components of the new system still need fixes.');
    }

    return isSystemReady;
  }
}

// Run the test suite
const testSuite = new DirectCandidateSpecificationTestSuite();
testSuite.runAllTests().catch(error => {
  console.error('💥 Direct candidate specification test suite execution failed:', error);
  process.exit(1);
});
