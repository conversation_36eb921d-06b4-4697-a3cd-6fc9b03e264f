"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkAndProcessMilestones = checkAndProcessMilestones;
exports.getUserMilestoneStats = getUserMilestoneStats;
exports.getMilestoneLeaderboard = getMilestoneLeaderboard;
exports.createDefaultMilestoneConfigurations = createDefaultMilestoneConfigurations;
exports.getUserSecurityReport = getUserSecurityReport;
const MilestoneConfiguration_1 = __importDefault(require("../models/MilestoneConfiguration"));
const UserActivity_1 = __importDefault(require("../models/UserActivity"));
const MilestoneAchievement_1 = __importDefault(require("../models/MilestoneAchievement"));
const MilestoneRateLimit_1 = __importDefault(require("../models/MilestoneRateLimit"));
const economyService_1 = require("./economyService");
const errorHandler_1 = require("../utils/errorHandler");
const milestoneAuditService_1 = require("./milestoneAuditService");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
const MAX_WEEKLY_MILESTONE_REWARDS = 50;
const MAX_DAILY_MILESTONE_REWARDS = 10;
const SUSPICIOUS_ACTIVITY_THRESHOLD = 5;
const BLACKLIST_DURATION_HOURS = 24;
const MIN_MESSAGE_LENGTH = 10;
const MIN_VOICE_SESSION_MINUTES = 5;
const MAX_ACHIEVEMENTS_PER_HOUR = 3;
const SPAM_DETECTION_WINDOW_MINUTES = 5;
const DUPLICATE_MESSAGE_THRESHOLD = 3;
const RAPID_ACTIVITY_THRESHOLD = 10;
async function checkAndProcessMilestones(client, discordId, guildId, activityType, activityData) {
    try {
        const userActivity = await ensureUserActivity(discordId, guildId);
        await updateUserActivity(userActivity, activityType, activityData);
        const milestoneConfigs = await MilestoneConfiguration_1.default.find({
            guildId,
            enabled: true
        });
        if (milestoneConfigs.length === 0) {
            return [];
        }
        const achievements = [];
        for (const config of milestoneConfigs) {
            const result = await checkSpecificMilestone(client, discordId, guildId, userActivity, config);
            if (result && result.achieved) {
                achievements.push(result);
            }
        }
        return achievements;
    }
    catch (error) {
        console.error('[Milestone Service] Error checking milestones:', error);
        throw new errorHandler_1.DatabaseError('Failed to check milestones');
    }
}
async function ensureUserActivity(discordId, guildId) {
    let userActivity = await UserActivity_1.default.findOne({ discordId, guildId });
    if (!userActivity) {
        userActivity = await UserActivity_1.default.create({
            discordId,
            guildId,
            serverJoinDate: new Date(),
            lastSeen: new Date(),
            lastLoginDate: new Date(),
            lastDailyReset: new Date(),
            lastWeeklyReset: new Date()
        });
    }
    return userActivity;
}
async function updateUserActivity(userActivity, activityType, activityData) {
    const now = new Date();
    await resetCountersIfNeeded(userActivity, now);
    userActivity.lastSeen = now;
    switch (activityType) {
        case 'message':
            await updateMessageActivity(userActivity, activityData, now);
            break;
        case 'voice':
            await updateVoiceActivity(userActivity, activityData, now);
            break;
        case 'reaction':
            await updateReactionActivity(userActivity, activityData, now);
            break;
        case 'login':
            await updateLoginActivity(userActivity, now);
            break;
    }
    await userActivity.save();
}
async function resetCountersIfNeeded(userActivity, now) {
    const lastDailyReset = new Date(userActivity.lastDailyReset);
    const lastWeeklyReset = new Date(userActivity.lastWeeklyReset);
    if (now.getDate() !== lastDailyReset.getDate() ||
        now.getMonth() !== lastDailyReset.getMonth() ||
        now.getFullYear() !== lastDailyReset.getFullYear()) {
        userActivity.dailyMessageCount = 0;
        userActivity.dailyVoiceMinutes = 0;
        userActivity.dailyReactionCount = 0;
        userActivity.uniqueChannelsToday = [];
        userActivity.uniqueVoiceChannelsToday = [];
        userActivity.uniqueReactionTypesToday = [];
        userActivity.lastDailyReset = now;
    }
    const currentWeek = getWeekNumber(now);
    const lastResetWeek = getWeekNumber(lastWeeklyReset);
    if (currentWeek !== lastResetWeek || now.getFullYear() !== lastWeeklyReset.getFullYear()) {
        userActivity.weeklyMessageCount = 0;
        userActivity.weeklyVoiceMinutes = 0;
        userActivity.weeklyReactionCount = 0;
        userActivity.uniqueChannelsThisWeek = [];
        userActivity.uniqueVoiceChannelsThisWeek = [];
        userActivity.uniqueReactionTypesThisWeek = [];
        userActivity.lastWeeklyReset = now;
    }
}
async function updateMessageActivity(userActivity, activityData, now) {
    if (activityData?.messageContent && activityData?.channelId) {
        const qualityCheck = await validateMessageQuality(userActivity.discordId, userActivity.guildId, activityData.messageContent, activityData.channelId);
        if (!qualityCheck.valid) {
            console.log(`[Milestone Service] Message quality check failed for ${userActivity.discordId}: ${qualityCheck.reason}`);
            return;
        }
    }
    userActivity.dailyMessageCount += 1;
    userActivity.weeklyMessageCount += 1;
    userActivity.totalMessages += 1;
    userActivity.lastMessageDate = now;
    if (activityData?.channelId) {
        if (!userActivity.uniqueChannelsToday.includes(activityData.channelId)) {
            userActivity.uniqueChannelsToday.push(activityData.channelId);
        }
        if (!userActivity.uniqueChannelsThisWeek.includes(activityData.channelId)) {
            userActivity.uniqueChannelsThisWeek.push(activityData.channelId);
        }
    }
}
async function updateVoiceActivity(userActivity, activityData, now) {
    const minutes = activityData?.minutes || 1;
    if (activityData?.channelId) {
        const voiceCheck = await validateVoiceActivity(userActivity.discordId, userActivity.guildId, activityData.channelId, minutes);
        if (!voiceCheck.valid) {
            console.log(`[Milestone Service] Voice activity check failed for ${userActivity.discordId}: ${voiceCheck.reason}`);
            return;
        }
    }
    userActivity.dailyVoiceMinutes += minutes;
    userActivity.weeklyVoiceMinutes += minutes;
    userActivity.totalVoiceMinutes += minutes;
    userActivity.lastVoiceDate = now;
    if (activityData?.channelId) {
        if (!userActivity.uniqueVoiceChannelsToday.includes(activityData.channelId)) {
            userActivity.uniqueVoiceChannelsToday.push(activityData.channelId);
        }
        if (!userActivity.uniqueVoiceChannelsThisWeek.includes(activityData.channelId)) {
            userActivity.uniqueVoiceChannelsThisWeek.push(activityData.channelId);
        }
    }
}
async function updateReactionActivity(userActivity, activityData, now) {
    userActivity.dailyReactionCount += 1;
    userActivity.weeklyReactionCount += 1;
    userActivity.totalReactions += 1;
    userActivity.lastReactionDate = now;
    if (activityData?.emoji) {
        const emojiId = activityData.emoji.id || activityData.emoji.name;
        if (!userActivity.uniqueReactionTypesToday.includes(emojiId)) {
            userActivity.uniqueReactionTypesToday.push(emojiId);
        }
        if (!userActivity.uniqueReactionTypesThisWeek.includes(emojiId)) {
            userActivity.uniqueReactionTypesThisWeek.push(emojiId);
        }
    }
}
async function updateLoginActivity(userActivity, now) {
    const lastLogin = new Date(userActivity.lastLoginDate);
    const daysDiff = Math.floor((now.getTime() - lastLogin.getTime()) / (1000 * 60 * 60 * 24));
    if (daysDiff === 1) {
        userActivity.loginStreak += 1;
        userActivity.totalDaysActive += 1;
    }
    else if (daysDiff === 0) {
        return;
    }
    else {
        userActivity.loginStreak = 1;
        userActivity.totalDaysActive += 1;
    }
    if (userActivity.loginStreak > userActivity.longestLoginStreak) {
        userActivity.longestLoginStreak = userActivity.loginStreak;
    }
    userActivity.lastLoginDate = now;
}
function getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
}
function getDayOfYear(date) {
    const start = new Date(date.getFullYear(), 0, 0);
    const diff = date.getTime() - start.getTime();
    return Math.floor(diff / (1000 * 60 * 60 * 24));
}
async function checkSpecificMilestone(client, discordId, guildId, userActivity, config) {
    try {
        const securityCheck = await checkUserSecurity(discordId, guildId, config.milestoneType);
        if (!securityCheck.allowed) {
            return null;
        }
        const rateLimitCheck = await checkRateLimit(discordId, guildId, config.milestoneType, config);
        if (!rateLimitCheck.allowed) {
            return null;
        }
        const achievementValue = await getMilestoneAchievementValue(userActivity, config);
        if (achievementValue < (config.requirements.threshold || 1)) {
            return null;
        }
        if (await isOnCooldown(discordId, guildId, config.milestoneType, config.requirements.cooldownHours || 24)) {
            return null;
        }
        const achievementCount = await getAchievementCount(discordId, config.milestoneType);
        const diminishingFactor = config.diminishingReturns ?
            Math.pow(config.diminishingFactor, achievementCount) : 1.0;
        const rewardAmount = Math.max(1, Math.floor(config.rewardAmount * diminishingFactor));
        await processAchievement(client, discordId, guildId, config, achievementValue, rewardAmount, diminishingFactor, achievementCount + 1);
        return {
            achieved: true,
            milestoneType: config.milestoneType,
            category: config.category,
            rewardAmount,
            details: generateAchievementDetails(config.milestoneType, achievementValue),
            achievementValue,
            diminishingFactor,
            achievementCount: achievementCount + 1
        };
    }
    catch (error) {
        console.error(`[Milestone Service] Error checking milestone ${config.milestoneType}:`, error);
        return null;
    }
}
async function checkRateLimit(discordId, guildId, milestoneType, config) {
    const now = new Date();
    const currentWeek = getWeekNumber(now);
    const currentDay = getDayOfYear(now);
    const currentYear = now.getFullYear();
    const weeklyCount = await MilestoneAchievement_1.default.countDocuments({
        discordId,
        year: currentYear,
        weekNumber: currentWeek
    });
    if (weeklyCount >= MAX_WEEKLY_MILESTONE_REWARDS) {
        await milestoneAuditService_1.MilestoneAuditService.logRateLimitHit(guildId, discordId, 'weekly_global_limit', `Weekly milestone limit reached: ${weeklyCount}/${MAX_WEEKLY_MILESTONE_REWARDS}`);
        return { allowed: false, reason: 'Weekly milestone limit reached' };
    }
    const dailyCount = await MilestoneAchievement_1.default.countDocuments({
        discordId,
        year: currentYear,
        dayOfYear: currentDay
    });
    if (dailyCount >= MAX_DAILY_MILESTONE_REWARDS) {
        await milestoneAuditService_1.MilestoneAuditService.logRateLimitHit(guildId, discordId, 'daily_global_limit', `Daily milestone limit reached: ${dailyCount}/${MAX_DAILY_MILESTONE_REWARDS}`);
        return { allowed: false, reason: 'Daily milestone limit reached' };
    }
    const rateLimit = await MilestoneRateLimit_1.default.findOne({
        discordId,
        guildId,
        milestoneType
    });
    if (rateLimit) {
        if (rateLimit.isBlacklisted && rateLimit.blacklistUntil && rateLimit.blacklistUntil > now) {
            return { allowed: false, reason: 'User is temporarily blacklisted' };
        }
        await resetRateLimitCounters(rateLimit, now);
        if (rateLimit.dailyCount >= config.maxRewardsPerDay) {
            await milestoneAuditService_1.MilestoneAuditService.logRateLimitHit(guildId, discordId, 'daily_milestone_limit', `Daily limit reached for ${milestoneType}: ${rateLimit.dailyCount}/${config.maxRewardsPerDay}`);
            return { allowed: false, reason: 'Daily limit reached for this milestone type' };
        }
        if (rateLimit.weeklyCount >= config.maxRewardsPerWeek) {
            await milestoneAuditService_1.MilestoneAuditService.logRateLimitHit(guildId, discordId, 'weekly_milestone_limit', `Weekly limit reached for ${milestoneType}: ${rateLimit.weeklyCount}/${config.maxRewardsPerWeek}`);
            return { allowed: false, reason: 'Weekly limit reached for this milestone type' };
        }
    }
    return { allowed: true };
}
async function resetRateLimitCounters(rateLimit, now) {
    const lastDailyReset = new Date(rateLimit.lastDailyReset);
    const lastWeeklyReset = new Date(rateLimit.lastWeeklyReset);
    let needsSave = false;
    if (now.getDate() !== lastDailyReset.getDate() ||
        now.getMonth() !== lastDailyReset.getMonth() ||
        now.getFullYear() !== lastDailyReset.getFullYear()) {
        rateLimit.dailyCount = 0;
        rateLimit.lastDailyReset = now;
        needsSave = true;
    }
    const currentWeek = getWeekNumber(now);
    const lastResetWeek = getWeekNumber(lastWeeklyReset);
    if (currentWeek !== lastResetWeek || now.getFullYear() !== lastWeeklyReset.getFullYear()) {
        rateLimit.weeklyCount = 0;
        rateLimit.lastWeeklyReset = now;
        needsSave = true;
    }
    if (needsSave) {
        await rateLimit.save();
    }
}
async function getMilestoneAchievementValue(userActivity, config) {
    switch (config.milestoneType) {
        case 'login_streak':
            return userActivity.loginStreak;
        case 'channel_diversity_daily':
            return userActivity.uniqueChannelsToday.length;
        case 'channel_diversity_weekly':
            return userActivity.uniqueChannelsThisWeek.length;
        case 'voice_diversity_daily':
            return userActivity.uniqueVoiceChannelsToday.length;
        case 'voice_diversity_weekly':
            return userActivity.uniqueVoiceChannelsThisWeek.length;
        case 'reaction_diversity_daily':
            return userActivity.uniqueReactionTypesToday.length;
        case 'reaction_diversity_weekly':
            return userActivity.uniqueReactionTypesThisWeek.length;
        case 'server_anniversary':
            const daysSinceJoin = Math.floor((Date.now() - userActivity.serverJoinDate.getTime()) / (1000 * 60 * 60 * 24));
            return Math.floor(daysSinceJoin / 30);
        case 'total_activity_milestone':
            return userActivity.totalDaysActive;
        case 'voice_time_daily':
            return userActivity.dailyVoiceMinutes;
        case 'voice_time_weekly':
            return userActivity.weeklyVoiceMinutes;
        case 'message_count_daily':
            return userActivity.dailyMessageCount;
        case 'message_count_weekly':
            return userActivity.weeklyMessageCount;
        default:
            return 0;
    }
}
async function isOnCooldown(discordId, guildId, milestoneType, cooldownHours) {
    const rateLimit = await MilestoneRateLimit_1.default.findOne({
        discordId,
        guildId,
        milestoneType
    });
    if (!rateLimit) {
        return false;
    }
    const now = new Date();
    return rateLimit.cooldownUntil > now;
}
async function getAchievementCount(discordId, milestoneType) {
    return await MilestoneAchievement_1.default.countDocuments({
        discordId,
        milestoneType
    });
}
async function processAchievement(client, discordId, guildId, config, achievementValue, rewardAmount, diminishingFactor, achievementCount) {
    const now = new Date();
    const weekNumber = getWeekNumber(now);
    const dayOfYear = getDayOfYear(now);
    const year = now.getFullYear();
    await MilestoneAchievement_1.default.create({
        discordId,
        guildId,
        milestoneType: config.milestoneType,
        category: config.category,
        achievementValue,
        rewardAmount,
        diminishingFactor,
        achievementCount,
        details: generateAchievementDetails(config.milestoneType, achievementValue),
        timestamp: now,
        weekNumber,
        dayOfYear,
        year
    });
    await updateRateLimit(discordId, guildId, config.milestoneType, config.requirements.cooldownHours || 24);
    await (0, economyService_1.adjustBalance)(discordId, guildId, rewardAmount, 'milestone', generateAchievementDetails(config.milestoneType, achievementValue), client);
    await milestoneAuditService_1.MilestoneAuditService.logMilestoneAchievement(guildId, discordId, config.milestoneType, rewardAmount, generateAchievementDetails(config.milestoneType, achievementValue), client);
    const coinSymbol = await configurableConstants_1.default.getCoinSymbol(guildId);
    console.log(`[Milestone Service] User ${discordId} achieved ${config.milestoneType} milestone: ${rewardAmount} ${coinSymbol}`);
}
async function updateRateLimit(discordId, guildId, milestoneType, cooldownHours) {
    const now = new Date();
    const cooldownUntil = new Date(now.getTime() + (cooldownHours * 60 * 60 * 1000));
    await MilestoneRateLimit_1.default.findOneAndUpdate({ discordId, guildId, milestoneType }, {
        $inc: { dailyCount: 1, weeklyCount: 1 },
        $set: {
            lastAchievement: now,
            cooldownUntil
        },
        $setOnInsert: {
            discordId,
            guildId,
            milestoneType,
            suspiciousActivityCount: 0,
            isBlacklisted: false,
            lastDailyReset: now,
            lastWeeklyReset: now
        }
    }, { upsert: true, new: true });
}
function generateAchievementDetails(milestoneType, achievementValue) {
    switch (milestoneType) {
        case 'login_streak':
            return `${achievementValue} day login streak milestone`;
        case 'channel_diversity_daily':
            return `Active in ${achievementValue} different channels today`;
        case 'channel_diversity_weekly':
            return `Active in ${achievementValue} different channels this week`;
        case 'voice_diversity_daily':
            return `Joined ${achievementValue} different voice channels today`;
        case 'voice_diversity_weekly':
            return `Joined ${achievementValue} different voice channels this week`;
        case 'reaction_diversity_daily':
            return `Used ${achievementValue} different reaction types today`;
        case 'reaction_diversity_weekly':
            return `Used ${achievementValue} different reaction types this week`;
        case 'server_anniversary':
            return `${achievementValue} month server anniversary milestone`;
        case 'total_activity_milestone':
            return `${achievementValue} total active days milestone`;
        case 'voice_time_daily':
            return `${achievementValue} minutes in voice chat today`;
        case 'voice_time_weekly':
            return `${achievementValue} minutes in voice chat this week`;
        case 'message_count_daily':
            return `${achievementValue} messages sent today`;
        case 'message_count_weekly':
            return `${achievementValue} messages sent this week`;
        default:
            return `${milestoneType} milestone achieved`;
    }
}
async function getUserMilestoneStats(discordId, guildId) {
    try {
        const [achievements, userActivity] = await Promise.all([
            MilestoneAchievement_1.default.find({ discordId, guildId })
                .sort({ timestamp: -1 })
                .limit(10),
            UserActivity_1.default.findOne({ discordId, guildId })
        ]);
        const totalAchievements = await MilestoneAchievement_1.default.countDocuments({ discordId, guildId });
        const totalRewards = await MilestoneAchievement_1.default.aggregate([
            { $match: { discordId, guildId } },
            { $group: { _id: null, total: { $sum: '$rewardAmount' } } }
        ]);
        const currentWeek = getWeekNumber(new Date());
        const currentYear = new Date().getFullYear();
        const weeklyProgress = await MilestoneAchievement_1.default.countDocuments({
            discordId,
            guildId,
            year: currentYear,
            weekNumber: currentWeek
        });
        return {
            totalAchievements,
            totalRewards: totalRewards[0]?.total || 0,
            recentAchievements: achievements,
            currentStreaks: {
                loginStreak: userActivity?.loginStreak || 0,
                longestLoginStreak: userActivity?.longestLoginStreak || 0
            },
            weeklyProgress: {
                achievementsThisWeek: weeklyProgress,
                maxWeeklyAchievements: MAX_WEEKLY_MILESTONE_REWARDS
            }
        };
    }
    catch (error) {
        console.error('[Milestone Service] Error getting user stats:', error);
        throw new errorHandler_1.DatabaseError('Failed to get milestone statistics');
    }
}
async function getMilestoneLeaderboard(guildId, limit = 10) {
    try {
        return await MilestoneAchievement_1.default.aggregate([
            { $match: { guildId } },
            {
                $group: {
                    _id: '$discordId',
                    totalAchievements: { $sum: 1 },
                    totalRewards: { $sum: '$rewardAmount' },
                    lastAchievement: { $max: '$timestamp' }
                }
            },
            { $sort: { totalRewards: -1, totalAchievements: -1 } },
            { $limit: limit }
        ]);
    }
    catch (error) {
        console.error('[Milestone Service] Error getting leaderboard:', error);
        throw new errorHandler_1.DatabaseError('Failed to get milestone leaderboard');
    }
}
async function createDefaultMilestoneConfigurations(guildId) {
    const defaultConfigs = [
        {
            guildId,
            category: 'time_based',
            milestoneType: 'login_streak',
            enabled: true,
            rewardAmount: 10,
            maxRewardsPerWeek: 7,
            maxRewardsPerDay: 1,
            diminishingReturns: true,
            diminishingFactor: 0.9,
            requirements: { threshold: 3, cooldownHours: 24 }
        },
        {
            guildId,
            category: 'time_based',
            milestoneType: 'server_anniversary',
            enabled: true,
            rewardAmount: 50,
            maxRewardsPerWeek: 1,
            maxRewardsPerDay: 1,
            diminishingReturns: false,
            diminishingFactor: 1.0,
            requirements: { threshold: 1, cooldownHours: 720 }
        },
        {
            guildId,
            category: 'participation_diversity',
            milestoneType: 'channel_diversity_daily',
            enabled: true,
            rewardAmount: 5,
            maxRewardsPerWeek: 7,
            maxRewardsPerDay: 1,
            diminishingReturns: true,
            diminishingFactor: 0.8,
            requirements: { threshold: 5, cooldownHours: 24 }
        },
        {
            guildId,
            category: 'participation_diversity',
            milestoneType: 'reaction_diversity_weekly',
            enabled: true,
            rewardAmount: 15,
            maxRewardsPerWeek: 1,
            maxRewardsPerDay: 1,
            diminishingReturns: true,
            diminishingFactor: 0.85,
            requirements: { threshold: 10, cooldownHours: 168 }
        },
        {
            guildId,
            category: 'engagement',
            milestoneType: 'voice_time_weekly',
            enabled: true,
            rewardAmount: 20,
            maxRewardsPerWeek: 1,
            maxRewardsPerDay: 1,
            diminishingReturns: true,
            diminishingFactor: 0.9,
            requirements: { threshold: 60, cooldownHours: 168 }
        }
    ];
    for (const config of defaultConfigs) {
        await MilestoneConfiguration_1.default.findOneAndUpdate({ guildId: config.guildId, milestoneType: config.milestoneType }, config, { upsert: true, new: true });
    }
    console.log(`[Milestone Service] Created default configurations for guild ${guildId}`);
}
async function validateMessageQuality(discordId, guildId, messageContent, channelId) {
    try {
        if (messageContent.length < MIN_MESSAGE_LENGTH) {
            return { valid: false, reason: 'Message too short' };
        }
        const spamCheck = await detectSpamPatterns(discordId, guildId, messageContent);
        if (!spamCheck.valid) {
            await milestoneAuditService_1.MilestoneAuditService.logSuspiciousActivity(guildId, discordId, 'spam_detection', `Spam pattern detected: ${spamCheck.reason}`, { messageContent: messageContent.substring(0, 100), channelId });
            await incrementSuspiciousActivity(discordId, guildId, 'message_spam', spamCheck.reason || 'Spam detected');
            return spamCheck;
        }
        const rapidCheck = await detectRapidActivity(discordId, guildId, 'message');
        if (!rapidCheck.valid) {
            await milestoneAuditService_1.MilestoneAuditService.logSuspiciousActivity(guildId, discordId, 'rapid_activity', `Rapid messaging detected: ${rapidCheck.reason}`, { activityType: 'message', channelId });
            await incrementSuspiciousActivity(discordId, guildId, 'rapid_messaging', rapidCheck.reason || 'Rapid activity');
            return rapidCheck;
        }
        return { valid: true };
    }
    catch (error) {
        console.error('[Milestone Service] Error validating message quality:', error);
        return { valid: false, reason: 'Validation error' };
    }
}
async function validateVoiceActivity(discordId, guildId, channelId, minutes) {
    try {
        if (minutes < MIN_VOICE_SESSION_MINUTES) {
            return { valid: false, reason: 'Voice session too short' };
        }
        const rapidCheck = await detectRapidActivity(discordId, guildId, 'voice');
        if (!rapidCheck.valid) {
            await milestoneAuditService_1.MilestoneAuditService.logSuspiciousActivity(guildId, discordId, 'rapid_voice_switching', `Rapid voice activity detected: ${rapidCheck.reason}`, { activityType: 'voice', channelId, minutes });
            await incrementSuspiciousActivity(discordId, guildId, 'rapid_voice', rapidCheck.reason || 'Rapid voice activity');
            return rapidCheck;
        }
        return { valid: true };
    }
    catch (error) {
        console.error('[Milestone Service] Error validating voice activity:', error);
        return { valid: false, reason: 'Validation error' };
    }
}
async function detectSpamPatterns(discordId, guildId, messageContent) {
    try {
        const repeatedChars = messageContent.match(/(.)\1{2,}/g);
        if (repeatedChars && repeatedChars.join('').length > messageContent.length * 0.5) {
            return { valid: false, reason: 'Excessive repeated characters' };
        }
        const upperCaseCount = (messageContent.match(/[A-Z]/g) || []).length;
        const letterCount = (messageContent.match(/[A-Za-z]/g) || []).length;
        if (letterCount > 0 && upperCaseCount / letterCount > 0.7) {
            return { valid: false, reason: 'Excessive uppercase' };
        }
        const recentMessages = await getRecentUserMessages(discordId, guildId, 5);
        const duplicateCount = recentMessages.filter(msg => msg === messageContent).length;
        if (duplicateCount >= DUPLICATE_MESSAGE_THRESHOLD) {
            return { valid: false, reason: 'Duplicate message spam' };
        }
        return { valid: true };
    }
    catch (error) {
        console.error('[Milestone Service] Error detecting spam patterns:', error);
        return { valid: true };
    }
}
async function detectRapidActivity(discordId, guildId, activityType) {
    try {
        const now = new Date();
        const oneMinuteAgo = new Date(now.getTime() - (60 * 1000));
        const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));
        const recentAchievements = await MilestoneAchievement_1.default.countDocuments({
            discordId,
            guildId,
            timestamp: { $gte: oneHourAgo }
        });
        if (recentAchievements >= MAX_ACHIEVEMENTS_PER_HOUR) {
            return { valid: false, reason: `Too many achievements in last hour: ${recentAchievements}` };
        }
        const rapidActivityCount = await getRapidActivityCount(discordId, guildId, activityType, oneMinuteAgo);
        if (rapidActivityCount >= RAPID_ACTIVITY_THRESHOLD) {
            return { valid: false, reason: `Rapid ${activityType} activity: ${rapidActivityCount} in 1 minute` };
        }
        return { valid: true };
    }
    catch (error) {
        console.error('[Milestone Service] Error detecting rapid activity:', error);
        return { valid: true };
    }
}
async function getRecentUserMessages(discordId, guildId, limit) {
    try {
        return [];
    }
    catch (error) {
        console.error('[Milestone Service] Error getting recent messages:', error);
        return [];
    }
}
async function getRapidActivityCount(discordId, guildId, activityType, since) {
    try {
        return await MilestoneAchievement_1.default.countDocuments({
            discordId,
            guildId,
            timestamp: { $gte: since },
            details: { $regex: activityType, $options: 'i' }
        });
    }
    catch (error) {
        console.error('[Milestone Service] Error getting rapid activity count:', error);
        return 0;
    }
}
async function checkUserSecurity(discordId, guildId, milestoneType) {
    try {
        const rateLimit = await MilestoneRateLimit_1.default.findOne({
            discordId,
            guildId,
            milestoneType
        });
        if (rateLimit) {
            if (rateLimit.isBlacklisted) {
                const now = new Date();
                if (rateLimit.blacklistUntil && rateLimit.blacklistUntil > now) {
                    return { allowed: false, reason: 'User is temporarily blacklisted' };
                }
                else if (rateLimit.blacklistUntil && rateLimit.blacklistUntil <= now) {
                    await MilestoneRateLimit_1.default.updateOne({ _id: rateLimit._id }, {
                        $unset: { blacklistUntil: 1, blacklistReason: 1 },
                        $set: { isBlacklisted: false }
                    });
                    await milestoneAuditService_1.MilestoneAuditService.logBlacklistAction(guildId, discordId, 'blacklist_removed', 'Blacklist expired automatically');
                }
            }
            if (rateLimit.suspiciousActivityCount >= SUSPICIOUS_ACTIVITY_THRESHOLD) {
                const lastSuspicious = rateLimit.lastSuspiciousActivity;
                const hoursSinceLastSuspicious = lastSuspicious ?
                    (Date.now() - lastSuspicious.getTime()) / (1000 * 60 * 60) : 24;
                if (hoursSinceLastSuspicious < 24) {
                    const blacklistUntil = new Date(Date.now() + (BLACKLIST_DURATION_HOURS * 60 * 60 * 1000));
                    await MilestoneRateLimit_1.default.updateOne({ _id: rateLimit._id }, {
                        $set: {
                            isBlacklisted: true,
                            blacklistUntil,
                            blacklistReason: 'Automatic blacklist due to suspicious activity'
                        }
                    });
                    await milestoneAuditService_1.MilestoneAuditService.logBlacklistAction(guildId, discordId, 'blacklist_added', `Automatic blacklist: ${rateLimit.suspiciousActivityCount} suspicious activities`);
                    return { allowed: false, reason: 'User automatically blacklisted due to suspicious activity' };
                }
            }
        }
        return { allowed: true };
    }
    catch (error) {
        console.error('[Milestone Service] Error checking user security:', error);
        return { allowed: true };
    }
}
async function incrementSuspiciousActivity(discordId, guildId, milestoneType, reason) {
    try {
        const now = new Date();
        await MilestoneRateLimit_1.default.findOneAndUpdate({ discordId, guildId, milestoneType }, {
            $inc: { suspiciousActivityCount: 1 },
            $set: { lastSuspiciousActivity: now },
            $setOnInsert: {
                discordId,
                guildId,
                milestoneType,
                dailyCount: 0,
                weeklyCount: 0,
                isBlacklisted: false,
                lastDailyReset: now,
                lastWeeklyReset: now,
                lastAchievement: now,
                cooldownUntil: now
            }
        }, { upsert: true, new: true });
        console.warn(`[Milestone Service] Incremented suspicious activity for ${discordId}: ${reason}`);
    }
    catch (error) {
        console.error('[Milestone Service] Error incrementing suspicious activity:', error);
    }
}
async function getUserSecurityReport(discordId, guildId) {
    try {
        const [rateLimits, auditSummary] = await Promise.all([
            MilestoneRateLimit_1.default.find({ discordId, guildId }),
            milestoneAuditService_1.MilestoneAuditService.getUserActivitySummary(guildId, discordId, 7)
        ]);
        let totalSuspiciousActivity = 0;
        let isBlacklisted = false;
        let blacklistReason;
        let blacklistUntil;
        let lastSuspiciousActivity;
        for (const rateLimit of rateLimits) {
            totalSuspiciousActivity += rateLimit.suspiciousActivityCount;
            if (rateLimit.isBlacklisted) {
                isBlacklisted = true;
                blacklistReason = rateLimit.blacklistReason;
                blacklistUntil = rateLimit.blacklistUntil;
            }
            if (rateLimit.lastSuspiciousActivity) {
                if (!lastSuspiciousActivity || rateLimit.lastSuspiciousActivity > lastSuspiciousActivity) {
                    lastSuspiciousActivity = rateLimit.lastSuspiciousActivity;
                }
            }
        }
        let securityScore = 'low';
        const recommendations = [];
        if (isBlacklisted) {
            securityScore = 'critical';
            recommendations.push('User is currently blacklisted');
        }
        else if (totalSuspiciousActivity >= SUSPICIOUS_ACTIVITY_THRESHOLD) {
            securityScore = 'high';
            recommendations.push('High suspicious activity count - monitor closely');
        }
        else if (auditSummary.rateLimitHits > 5) {
            securityScore = 'medium';
            recommendations.push('Frequent rate limit hits - possible automation');
        }
        else if (totalSuspiciousActivity > 0) {
            securityScore = 'medium';
            recommendations.push('Some suspicious activity detected');
        }
        if (auditSummary.totalAchievements > 20) {
            recommendations.push('High achievement rate - verify legitimacy');
        }
        return {
            isBlacklisted,
            blacklistReason,
            blacklistUntil,
            suspiciousActivityCount: totalSuspiciousActivity,
            lastSuspiciousActivity,
            recentRateLimits: auditSummary.rateLimitHits,
            securityScore,
            recommendations
        };
    }
    catch (error) {
        console.error('[Milestone Service] Error generating security report:', error);
        return {
            isBlacklisted: false,
            suspiciousActivityCount: 0,
            recentRateLimits: 0,
            securityScore: 'low',
            recommendations: ['Error generating report']
        };
    }
}
