"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SalaryService = void 0;
const discord_js_1 = require("discord.js");
const mongoose_1 = __importDefault(require("mongoose"));
const BaseService_1 = require("../base/BaseService");
const features_1 = require("../../config/features");
const RoleSalary_1 = require("../../models/RoleSalary");
const Transaction_1 = __importDefault(require("../../models/Transaction"));
const User_1 = __importDefault(require("../../models/User"));
const errorHandler_1 = require("../../utils/errorHandler");
const embedBuilder_1 = require("../../utils/embedBuilder");
class SalaryService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'SalaryService';
    }
    async onInitialize() {
        if (!this.isFeatureEnabled('SALARY_SYSTEM')) {
            throw new Error('Salary system is not enabled');
        }
        this.logger.info('[SalaryService] Salary system initialized');
    }
    async addSalaryConfig(config) {
        this.validateSalaryConfig(config);
        try {
            this.logOperation('Adding salary configuration', config);
            const salaryConfig = await RoleSalary_1.RoleSalary.findOneAndUpdate({ roleId: config.roleId, guildId: config.guildId }, {
                amount: config.amount,
                frequency: config.frequency,
                updatedAt: new Date()
            }, {
                upsert: true,
                new: true,
                runValidators: true
            });
            this.logOperation('Salary configuration added/updated', {
                id: salaryConfig._id,
                roleId: config.roleId,
                amount: config.amount,
                frequency: config.frequency
            });
            return salaryConfig;
        }
        catch (error) {
            this.handleError(error, { operation: 'add_salary_config', config });
            throw error;
        }
    }
    async removeSalaryConfig(roleId, guildId) {
        try {
            this.logOperation('Removing salary configuration', { roleId, guildId });
            const result = await RoleSalary_1.RoleSalary.deleteOne({ roleId, guildId });
            if (result.deletedCount === 0) {
                return false;
            }
            this.logOperation('Salary configuration removed', { roleId, guildId });
            return true;
        }
        catch (error) {
            this.handleError(error, { operation: 'remove_salary_config', roleId, guildId });
            throw error;
        }
    }
    async getSalaryConfig(roleId, guildId) {
        try {
            return await RoleSalary_1.RoleSalary.findOne({ roleId, guildId });
        }
        catch (error) {
            this.handleError(error, { operation: 'get_salary_config', roleId, guildId });
            throw error;
        }
    }
    async getGuildSalaryConfigs(guildId) {
        try {
            return await RoleSalary_1.RoleSalary.find({ guildId }).sort({ amount: -1 });
        }
        catch (error) {
            this.handleError(error, { operation: 'get_guild_salary_configs', guildId });
            throw error;
        }
    }
    async processSalaryDistribution(client, frequency) {
        this.logOperation('Starting salary distribution', { frequency });
        const result = {
            totalProcessed: 0,
            totalPaid: 0,
            totalAmount: 0,
            errors: [],
            guildResults: new Map()
        };
        try {
            const cutoffTime = this.calculateCutoffTime(frequency);
            const salaryConfigs = await RoleSalary_1.RoleSalary.find({
                frequency,
                $or: [
                    { lastProcessed: { $lt: cutoffTime } },
                    { lastProcessed: null }
                ]
            });
            this.logOperation('Found salary configurations to process', {
                count: salaryConfigs.length,
                frequency,
                cutoffTime
            });
            for (const config of salaryConfigs) {
                try {
                    const guildResult = await this.processSalaryForRole(client, config);
                    const existing = result.guildResults.get(config.guildId) || {
                        processed: 0,
                        paid: 0,
                        amount: 0,
                        errors: []
                    };
                    existing.processed += guildResult.processed;
                    existing.paid += guildResult.paid;
                    existing.amount += guildResult.amount;
                    existing.errors.push(...guildResult.errors);
                    result.guildResults.set(config.guildId, existing);
                    result.totalProcessed += guildResult.processed;
                    result.totalPaid += guildResult.paid;
                    result.totalAmount += guildResult.amount;
                    result.errors.push(...guildResult.errors);
                    await RoleSalary_1.RoleSalary.updateOne({ _id: config._id }, { lastProcessed: new Date() });
                }
                catch (error) {
                    const errorMsg = `Failed to process salary for role ${config.roleId} in guild ${config.guildId}: ${error}`;
                    result.errors.push(errorMsg);
                    this.handleError(error, {
                        operation: 'process_salary_for_role',
                        roleId: config.roleId,
                        guildId: config.guildId
                    });
                }
            }
            this.logOperation('Salary distribution completed', {
                frequency,
                totalProcessed: result.totalProcessed,
                totalPaid: result.totalPaid,
                totalAmount: result.totalAmount,
                errorCount: result.errors.length
            });
            return result;
        }
        catch (error) {
            this.handleError(error, { operation: 'process_salary_distribution', frequency });
            throw error;
        }
    }
    async processSalaryForRole(client, config) {
        const result = { processed: 0, paid: 0, amount: 0, errors: [] };
        try {
            const guild = await client.guilds.fetch(config.guildId);
            if (!guild) {
                throw new Error(`Guild not found: ${config.guildId}`);
            }
            const role = await guild.roles.fetch(config.roleId);
            if (!role) {
                throw new Error(`Role not found: ${config.roleId}`);
            }
            await guild.members.fetch();
            const membersWithRole = role.members;
            this.logOperation('Processing salary for role', {
                roleId: config.roleId,
                roleName: role.name,
                memberCount: membersWithRole.size,
                amount: config.amount
            });
            for (const [memberId, member] of membersWithRole) {
                try {
                    result.processed++;
                    await this.paySalaryToMember(member, config, role);
                    result.paid++;
                    result.amount += config.amount;
                    await this.sendSalaryNotification(member, config, role);
                }
                catch (error) {
                    const errorMsg = `Failed to pay salary to ${member.user.username}: ${error}`;
                    result.errors.push(errorMsg);
                    this.logger.error('Failed to pay salary to member', {
                        memberId,
                        username: member.user.username,
                        error
                    });
                }
            }
            return result;
        }
        catch (error) {
            result.errors.push(`Failed to process role ${config.roleId}: ${error}`);
            throw error;
        }
    }
    async paySalaryToMember(member, config, role) {
        const session = await mongoose_1.default.startSession();
        try {
            await session.withTransaction(async () => {
                const user = await User_1.default.findOneAndUpdate({ discordId: member.id, guildId: config.guildId }, { $inc: { balance: config.amount } }, {
                    upsert: true,
                    new: true,
                    session,
                    runValidators: true
                });
                await Transaction_1.default.create([{
                        discordId: member.id,
                        guildId: config.guildId,
                        type: 'salary',
                        amount: config.amount,
                        details: `${config.frequency} salary from role: ${role.name}`,
                        roleId: config.roleId,
                        timestamp: new Date()
                    }], { session });
                this.logOperation('Salary paid to member', {
                    memberId: member.id,
                    username: member.user.username,
                    amount: config.amount,
                    newBalance: user?.balance,
                    roleId: config.roleId,
                    roleName: role.name
                });
            });
        }
        finally {
            await session.endSession();
        }
    }
    async sendSalaryNotification(member, config, role) {
        try {
            const formattedAmount = await (0, embedBuilder_1.formatServerCoins)(member.guild.id, config.amount);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(member.guild.id, 'Salary Received!')
                .then(embed => embed.setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.MONEY} You received your ${config.frequency} salary!\n\n` +
                `**Amount:** ${formattedAmount}\n` +
                `**Role:** ${role.name}\n` +
                `**Server:** ${member.guild.name}`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Payment Schedule`,
                value: `This is your **${config.frequency}** salary payment.`,
                inline: true
            }));
            const actionRow = (0, embedBuilder_1.createSalaryActionButtons)();
            await member.send({
                embeds: [embed],
                components: [actionRow]
            });
            this.logOperation('Salary notification sent', {
                memberId: member.id,
                username: member.user.username,
                roleId: config.roleId
            });
        }
        catch (error) {
            this.logger.debug('Failed to send salary notification DM', {
                memberId: member.id,
                username: member.user.username,
                error
            });
        }
    }
    calculateCutoffTime(frequency) {
        const now = new Date();
        const cutoff = new Date(now);
        if (frequency === RoleSalary_1.SalaryFrequency.DAILY) {
            cutoff.setDate(cutoff.getDate() - 1);
        }
        else if (frequency === RoleSalary_1.SalaryFrequency.WEEKLY) {
            cutoff.setDate(cutoff.getDate() - 7);
        }
        return cutoff;
    }
    validateSalaryConfig(config) {
        if (!config.roleId || config.roleId.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Role ID is required');
        }
        if (!config.guildId || config.guildId.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Guild ID is required');
        }
        if (!Number.isInteger(config.amount) || config.amount <= 0) {
            throw new errorHandler_1.ValidationError('Amount must be a positive integer');
        }
        if (config.amount > 1000000) {
            throw new errorHandler_1.ValidationError('Amount cannot exceed 1,000,000');
        }
        if (!Object.values(RoleSalary_1.SalaryFrequency).includes(config.frequency)) {
            throw new errorHandler_1.ValidationError('Frequency must be either daily or weekly');
        }
    }
}
exports.SalaryService = SalaryService;
__decorate([
    (0, features_1.requireFeature)('SALARY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SalaryService.prototype, "addSalaryConfig", null);
__decorate([
    (0, features_1.requireFeature)('SALARY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SalaryService.prototype, "removeSalaryConfig", null);
__decorate([
    (0, features_1.requireFeature)('SALARY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SalaryService.prototype, "getSalaryConfig", null);
__decorate([
    (0, features_1.requireFeature)('SALARY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SalaryService.prototype, "getGuildSalaryConfigs", null);
__decorate([
    (0, features_1.requireFeature)('SALARY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [discord_js_1.Client, String]),
    __metadata("design:returntype", Promise)
], SalaryService.prototype, "processSalaryDistribution", null);
