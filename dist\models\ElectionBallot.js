"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const electionBallotSchema = new mongoose_1.Schema({
    ballotId: {
        type: String,
        required: [true, 'Ballot ID is required'],
        unique: true,
        index: true
    },
    electionId: {
        type: String,
        required: [true, 'Election ID is required'],
        validate: {
            validator: function (v) {
                return v && v.trim().length > 0;
            },
            message: 'Election ID cannot be empty'
        },
        index: true
    },
    voterUserId: {
        type: String,
        required: [true, 'Voter user ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Voter user ID must be a valid Discord snowflake'
        },
        index: true
    },
    candidateId: {
        type: String,
        required: [true, 'Candidate ID is required'],
        validate: {
            validator: function (v) {
                return v && v.trim().length > 0;
            },
            message: 'Candidate ID cannot be empty'
        },
        index: true
    },
    weight: {
        type: Number,
        required: [true, 'Vote weight is required'],
        min: [0, 'Vote weight cannot be negative'],
        validate: {
            validator: function (v) {
                return Number.isInteger(v);
            },
            message: 'Vote weight must be an integer'
        }
    },
    timestamp: {
        type: Date,
        required: [true, 'Timestamp is required'],
        default: Date.now,
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    replaced: {
        type: Boolean,
        default: false,
        index: true
    },
    replacedAt: {
        type: Date,
        index: true
    },
    replacedBy: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || (v && v.trim().length > 0);
            },
            message: 'Replaced by ballot ID cannot be empty if provided'
        }
    }
}, {
    timestamps: false
});
electionBallotSchema.index({ electionId: 1, voterUserId: 1, replaced: 1 });
electionBallotSchema.index({ electionId: 1, candidateId: 1, replaced: 1 });
electionBallotSchema.index({ electionId: 1, replaced: 1, timestamp: -1 });
electionBallotSchema.index({ guildId: 1, electionId: 1 });
electionBallotSchema.index({ candidateId: 1, replaced: 1 });
electionBallotSchema.methods.markAsReplaced = function (replacedBy) {
    if (this.replaced) {
        throw new Error('Ballot is already marked as replaced');
    }
    this.replaced = true;
    this.replacedAt = new Date();
    this.replacedBy = replacedBy;
};
electionBallotSchema.methods.isActive = function () {
    return !this.replaced;
};
electionBallotSchema.pre('save', function () {
    if (this.replaced && !this.replacedAt) {
        this.replacedAt = new Date();
    }
    if (this.replaced && !this.replacedBy) {
        this.invalidate('replacedBy', 'Replaced by ballot ID is required when ballot is replaced');
    }
    if (!this.replaced) {
        this.replacedAt = undefined;
        this.replacedBy = undefined;
    }
});
const ElectionBallot = (0, mongoose_1.model)('ElectionBallot', electionBallotSchema);
exports.default = ElectionBallot;
