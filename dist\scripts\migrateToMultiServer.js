"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const User_1 = __importDefault(require("../models/User"));
const Transaction_1 = __importDefault(require("../models/Transaction"));
const User_2 = require("../models/User");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/economy-bot';
const DEFAULT_GUILD_ID = process.env.DEFAULT_GUILD_ID || 'YOUR_GUILD_ID_HERE';
async function migrateToMultiServer() {
    console.log('🚀 Starting migration to multi-server format...');
    if (DEFAULT_GUILD_ID === 'YOUR_GUILD_ID_HERE') {
        console.error('❌ ERROR: Please set DEFAULT_GUILD_ID in the script or environment variable');
        console.error('   Find your Discord server ID and replace YOUR_GUILD_ID_HERE');
        process.exit(1);
    }
    try {
        await mongoose_1.default.connect(MONGODB_URI);
        console.log('✅ Connected to MongoDB');
        console.log('\n📦 Creating backup of existing data...');
        const db = mongoose_1.default.connection.db;
        if (!db) {
            throw new Error('Database connection not established');
        }
        const existingUsers = await db.collection('users').find({}).toArray();
        const existingTransactions = await db.collection('transactions').find({}).toArray();
        const existingRoles = await db.collection('rolesforsales').find({}).toArray();
        const existingPrefixes = await db.collection('roleprefixes').find({}).toArray();
        console.log(`   Found ${existingUsers.length} users`);
        console.log(`   Found ${existingTransactions.length} transactions`);
        console.log(`   Found ${existingRoles.length} roles for sale`);
        console.log(`   Found ${existingPrefixes.length} role prefixes`);
        const existingGuildUsers = await User_1.default.find({ guildId: { $exists: true } }).limit(1);
        if (existingGuildUsers.length > 0) {
            console.log('⚠️  Migration appears to have been run already (found users with guildId)');
            console.log('   If you want to re-run migration, please manually clear the database first');
            return;
        }
        console.log('\n👥 Migrating users...');
        let migratedUsers = 0;
        for (const oldUser of existingUsers) {
            try {
                if (oldUser.guildId) {
                    console.log(`   Skipping user ${oldUser.discordId} (already has guildId)`);
                    continue;
                }
                const newUser = new User_1.default({
                    discordId: oldUser.discordId,
                    guildId: DEFAULT_GUILD_ID,
                    balance: oldUser.balance || 0,
                    lastDaily: oldUser.lastDaily || null
                });
                await newUser.save();
                migratedUsers++;
                if (migratedUsers % 10 === 0) {
                    console.log(`   Migrated ${migratedUsers} users...`);
                }
            }
            catch (error) {
                console.error(`   Error migrating user ${oldUser.discordId}:`, error);
            }
        }
        console.log(`✅ Migrated ${migratedUsers} users`);
        console.log('\n💰 Migrating transactions...');
        let migratedTransactions = 0;
        for (const oldTransaction of existingTransactions) {
            try {
                if (oldTransaction.guildId) {
                    console.log(`   Skipping transaction ${oldTransaction._id} (already has guildId)`);
                    continue;
                }
                const newTransaction = new Transaction_1.default({
                    guildId: DEFAULT_GUILD_ID,
                    fromUserId: oldTransaction.fromUserId || null,
                    toUserId: oldTransaction.toUserId || null,
                    amount: oldTransaction.amount,
                    type: oldTransaction.type,
                    description: oldTransaction.description || null
                });
                await newTransaction.save();
                migratedTransactions++;
                if (migratedTransactions % 50 === 0) {
                    console.log(`   Migrated ${migratedTransactions} transactions...`);
                }
            }
            catch (error) {
                console.error(`   Error migrating transaction ${oldTransaction._id}:`, error);
            }
        }
        console.log(`✅ Migrated ${migratedTransactions} transactions`);
        console.log('\n🎭 Migrating roles for sale...');
        let migratedRoles = 0;
        for (const oldRole of existingRoles) {
            try {
                if (oldRole.guildId) {
                    console.log(`   Skipping role ${oldRole.roleId} (already has guildId)`);
                    continue;
                }
                const newRole = new User_2.RoleForSale({
                    guildId: DEFAULT_GUILD_ID,
                    roleId: oldRole.roleId,
                    price: oldRole.price,
                    name: oldRole.name,
                    description: oldRole.description || null
                });
                await newRole.save();
                migratedRoles++;
            }
            catch (error) {
                console.error(`   Error migrating role ${oldRole.roleId}:`, error);
            }
        }
        console.log(`✅ Migrated ${migratedRoles} roles for sale`);
        console.log('\n🏷️  Migrating role prefixes...');
        let migratedPrefixes = 0;
        for (const oldPrefix of existingPrefixes) {
            try {
                if (oldPrefix.guildId) {
                    console.log(`   Skipping prefix ${oldPrefix.roleId} (already has guildId)`);
                    continue;
                }
                const newPrefix = new User_2.RolePrefix({
                    guildId: DEFAULT_GUILD_ID,
                    roleId: oldPrefix.roleId,
                    prefix: oldPrefix.prefix
                });
                await newPrefix.save();
                migratedPrefixes++;
            }
            catch (error) {
                console.error(`   Error migrating prefix ${oldPrefix.roleId}:`, error);
            }
        }
        console.log(`✅ Migrated ${migratedPrefixes} role prefixes`);
        console.log('\n🧹 Cleanup phase...');
        console.log('   Old data is preserved for safety. You can manually remove it later if needed.');
        console.log('\n🎉 Migration completed successfully!');
        console.log(`   Users migrated: ${migratedUsers}`);
        console.log(`   Transactions migrated: ${migratedTransactions}`);
        console.log(`   Roles migrated: ${migratedRoles}`);
        console.log(`   Prefixes migrated: ${migratedPrefixes}`);
        console.log(`   All data assigned to guild: ${DEFAULT_GUILD_ID}`);
    }
    catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    }
    finally {
        await mongoose_1.default.disconnect();
        console.log('📡 Disconnected from MongoDB');
    }
}
if (require.main === module) {
    migrateToMultiServer().catch(console.error);
}
exports.default = migrateToMultiServer;
