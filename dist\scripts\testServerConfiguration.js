"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testServerConfiguration = testServerConfiguration;
const mongoose_1 = __importDefault(require("mongoose"));
const serverConfigurationService_1 = __importDefault(require("../services/serverConfigurationService"));
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
async function testServerConfiguration() {
    console.log('🧪 Testing Server Configuration System...\n');
    try {
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/economy-bot';
        await mongoose_1.default.connect(mongoUri);
        console.log('✅ Connected to MongoDB');
        console.log('\n📝 Test 1: Creating different server configurations...');
        const testServers = [
            {
                guildId: 'test-server-1',
                nationName: 'Alpha Republic',
                coinName: 'Alpha Credits',
                coinSymbol: 'ARC',
                embedColor: '#ff6b6b'
            },
            {
                guildId: 'test-server-2',
                nationName: 'Beta Empire',
                coinName: 'Beta Tokens',
                coinSymbol: 'BET',
                embedColor: '#4ecdc4'
            },
            {
                guildId: 'test-server-3',
                nationName: 'Gamma Federation',
                coinName: 'Gamma Points',
                coinSymbol: 'GAM',
                embedColor: 'purple'
            }
        ];
        for (const server of testServers) {
            await serverConfigurationService_1.default.updateServerConfig(server.guildId, {
                nationName: server.nationName,
                coinName: server.coinName,
                coinSymbol: server.coinSymbol,
                embedColor: server.embedColor
            });
            console.log(`  ✅ Created configuration for ${server.nationName}`);
        }
        console.log('\n🔍 Test 2: Verifying server isolation...');
        for (const server of testServers) {
            const config = await serverConfigurationService_1.default.getServerConfig(server.guildId);
            console.log(`  Server ${server.guildId}:`);
            console.log(`    Nation: ${config.nationName}`);
            console.log(`    Coin: ${config.coinName} (${config.coinSymbol})`);
            console.log(`    Color: ${config.embedColor}`);
            if (config.nationName !== server.nationName) {
                throw new Error(`Nation name mismatch for ${server.guildId}`);
            }
            if (config.coinSymbol !== server.coinSymbol) {
                throw new Error(`Coin symbol mismatch for ${server.guildId}`);
            }
        }
        console.log('  ✅ All configurations are correctly isolated');
        console.log('\n⚙️  Test 3: Testing ConfigurableConstants...');
        for (const server of testServers) {
            const constants = await configurableConstants_1.default.getAllServerConstants(server.guildId);
            const formattedCoins = await configurableConstants_1.default.formatCoins(server.guildId, 1500);
            const formattedSymbol = await configurableConstants_1.default.formatCoinsWithSymbol(server.guildId, 1500);
            const footer = await configurableConstants_1.default.getEmbedFooter(server.guildId);
            console.log(`  ${server.nationName}:`);
            console.log(`    Formatted coins: ${formattedCoins}`);
            console.log(`    Formatted symbol: ${formattedSymbol}`);
            console.log(`    Footer: ${footer}`);
            if (!formattedCoins.includes(server.coinName)) {
                throw new Error(`Coin formatting error for ${server.guildId}`);
            }
            if (!formattedSymbol.includes(server.coinSymbol)) {
                throw new Error(`Symbol formatting error for ${server.guildId}`);
            }
            if (!footer.includes(server.nationName)) {
                throw new Error(`Footer formatting error for ${server.guildId}`);
            }
        }
        console.log('  ✅ ConfigurableConstants working correctly');
        console.log('\n🆕 Test 4: Testing default configuration creation...');
        const newGuildId = 'brand-new-server';
        const defaultConfig = await serverConfigurationService_1.default.getServerConfig(newGuildId);
        console.log(`  New server configuration:`);
        console.log(`    Nation: ${defaultConfig.nationName}`);
        console.log(`    Coin: ${defaultConfig.coinName} (${defaultConfig.coinSymbol})`);
        console.log(`    Color: ${defaultConfig.embedColor}`);
        if (defaultConfig.nationName !== 'Phalanx Order') {
            throw new Error('Default nation name incorrect');
        }
        if (defaultConfig.coinSymbol !== 'PLC') {
            throw new Error('Default coin symbol incorrect');
        }
        console.log('  ✅ Default configuration created correctly');
        console.log('\n🔄 Test 5: Testing configuration updates...');
        const updatedConfig = await serverConfigurationService_1.default.updateServerConfig(newGuildId, {
            nationName: 'Updated Nation',
            embedColor: '#00ff00'
        });
        console.log(`  Updated configuration:`);
        console.log(`    Nation: ${updatedConfig.nationName}`);
        console.log(`    Coin: ${updatedConfig.coinName} (${updatedConfig.coinSymbol})`);
        console.log(`    Color: ${updatedConfig.embedColor}`);
        if (updatedConfig.nationName !== 'Updated Nation') {
            throw new Error('Nation name update failed');
        }
        if (updatedConfig.coinName !== 'Phalanx Loyalty Coins') {
            throw new Error('Coin name should remain unchanged');
        }
        if (updatedConfig.embedColor !== '#00ff00') {
            throw new Error('Embed color update failed');
        }
        console.log('  ✅ Configuration updates working correctly');
        console.log('\n💾 Test 6: Testing configuration caching...');
        const startTime = Date.now();
        await serverConfigurationService_1.default.getServerConfig('test-server-1');
        const firstCallTime = Date.now() - startTime;
        const startTime2 = Date.now();
        await serverConfigurationService_1.default.getServerConfig('test-server-1');
        const secondCallTime = Date.now() - startTime2;
        console.log(`  First call: ${firstCallTime}ms`);
        console.log(`  Second call (cached): ${secondCallTime}ms`);
        if (secondCallTime > firstCallTime) {
            console.log('  ⚠️  Cache may not be working optimally, but this is not critical');
        }
        else {
            console.log('  ✅ Caching appears to be working');
        }
        console.log('\n🧹 Cleaning up test data...');
        const allTestGuilds = [...testServers.map(s => s.guildId), newGuildId];
        for (const guildId of allTestGuilds) {
            await mongoose_1.default.connection.collection('serverconfigurations').deleteOne({ guildId });
        }
        console.log('  ✅ Test data cleaned up');
        console.log('\n🎉 All tests passed! Server configuration system is working correctly.');
        console.log('\n📋 Summary:');
        console.log('  ✅ Server configurations are properly isolated');
        console.log('  ✅ ConfigurableConstants work with server-specific values');
        console.log('  ✅ Default configurations are created automatically');
        console.log('  ✅ Configuration updates work correctly');
        console.log('  ✅ Caching system is functional');
    }
    catch (error) {
        console.error('\n❌ Test failed:', error);
        throw error;
    }
    finally {
        await mongoose_1.default.disconnect();
        console.log('\n🔌 Disconnected from MongoDB');
    }
}
if (require.main === module) {
    testServerConfiguration().catch(error => {
        console.error('Test script failed:', error);
        process.exit(1);
    });
}
