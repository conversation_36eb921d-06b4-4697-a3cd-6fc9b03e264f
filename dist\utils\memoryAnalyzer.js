"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryAnalyzer = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
class MemoryAnalyzer {
    static getInstance() {
        if (!MemoryAnalyzer.instance) {
            MemoryAnalyzer.instance = new MemoryAnalyzer();
        }
        return MemoryAnalyzer.instance;
    }
    getMemoryBreakdown() {
        const memUsage = process.memoryUsage();
        const estimatedComponents = {
            discordjs: this.estimateDiscordJSMemory(),
            mongoose: this.estimateMongooseMemory(),
            v8: memUsage.heapUsed * 0.1,
            buffers: memUsage.external,
            other: 0
        };
        const knownMemory = Object.values(estimatedComponents).reduce((sum, val) => sum + val, 0);
        estimatedComponents.other = Math.max(0, memUsage.heapUsed - knownMemory);
        const percentages = {};
        Object.entries(estimatedComponents).forEach(([key, value]) => {
            percentages[key] = (value / memUsage.heapUsed) * 100;
        });
        return {
            total: memUsage,
            components: estimatedComponents,
            percentages
        };
    }
    estimateDiscordJSMemory() {
        let estimate = 40 * 1024 * 1024;
        if (global.discordClient) {
            const client = global.discordClient;
            estimate += client.guilds.cache.size * 50000;
            estimate += client.users.cache.size * 5000;
            estimate += client.channels.cache.size * 10000;
            let messageCount = 0;
            client.channels.cache.forEach(channel => {
                if ('messages' in channel && channel.messages) {
                    messageCount += channel.messages.cache.size;
                }
            });
            estimate += messageCount * 2000;
        }
        return estimate;
    }
    estimateMongooseMemory() {
        let estimate = 15 * 1024 * 1024;
        if (mongoose_1.default.connection.readyState === 1) {
            estimate += 3 * 5 * 1024 * 1024;
        }
        return estimate;
    }
    analyzeDependencies() {
        return [
            {
                name: 'discord.js',
                size: 45 * 1024 * 1024,
                description: 'Discord API wrapper with extensive caching',
                canOptimize: true,
                alternatives: ['discord.js with minimal cache', 'eris (lighter alternative)']
            },
            {
                name: 'mongoose',
                size: 20 * 1024 * 1024,
                description: 'MongoDB ODM with schema validation',
                canOptimize: true,
                alternatives: ['mongodb driver directly', 'lighter ODM']
            },
            {
                name: 'winston',
                size: 8 * 1024 * 1024,
                description: 'Logging library with multiple transports',
                canOptimize: true,
                alternatives: ['console.log only', 'pino (lighter logger)']
            },
            {
                name: 'node-cron',
                size: 2 * 1024 * 1024,
                description: 'Cron job scheduler',
                canOptimize: false
            },
            {
                name: 'dotenv',
                size: 1 * 1024 * 1024,
                description: 'Environment variable loader',
                canOptimize: false
            }
        ];
    }
    getOptimizationRecommendations() {
        const breakdown = this.getMemoryBreakdown();
        const recommendations = [];
        if (breakdown.percentages.discordjs > 40) {
            recommendations.push('Discord.js cache is consuming >40% of memory - implement aggressive cache limits');
        }
        if (breakdown.percentages.mongoose > 20) {
            recommendations.push('Mongoose is consuming >20% of memory - consider reducing connection pool or switching to native driver');
        }
        if (breakdown.total.external > 50 * 1024 * 1024) {
            recommendations.push('External memory >50MB - check for large buffers or native modules');
        }
        if (breakdown.total.heapUsed > 200 * 1024 * 1024) {
            recommendations.push('Heap usage >200MB - implement more aggressive garbage collection');
        }
        return recommendations;
    }
    logMemoryAnalysis() {
        const breakdown = this.getMemoryBreakdown();
        const dependencies = this.analyzeDependencies();
        const recommendations = this.getOptimizationRecommendations();
        console.log('\n=== MEMORY ANALYSIS REPORT ===');
        console.log('\n📊 Memory Breakdown:');
        console.log(`Total RSS: ${(breakdown.total.rss / 1024 / 1024).toFixed(2)}MB`);
        console.log(`Heap Used: ${(breakdown.total.heapUsed / 1024 / 1024).toFixed(2)}MB`);
        console.log(`Heap Total: ${(breakdown.total.heapTotal / 1024 / 1024).toFixed(2)}MB`);
        console.log(`External: ${(breakdown.total.external / 1024 / 1024).toFixed(2)}MB`);
        console.log('\n🔍 Component Analysis:');
        Object.entries(breakdown.components).forEach(([component, size]) => {
            const sizeMB = (size / 1024 / 1024).toFixed(2);
            const percentage = breakdown.percentages[component].toFixed(1);
            console.log(`${component}: ${sizeMB}MB (${percentage}%)`);
        });
        console.log('\n📦 Heavy Dependencies:');
        dependencies.forEach(dep => {
            const sizeMB = (dep.size / 1024 / 1024).toFixed(2);
            console.log(`${dep.name}: ~${sizeMB}MB - ${dep.description}`);
            if (dep.canOptimize && dep.alternatives) {
                console.log(`  ↳ Alternatives: ${dep.alternatives.join(', ')}`);
            }
        });
        console.log('\n💡 Recommendations:');
        recommendations.forEach((rec, index) => {
            console.log(`${index + 1}. ${rec}`);
        });
        console.log('\n=== END ANALYSIS ===\n');
    }
    startMemoryMonitoring(intervalMs = 30000) {
        console.log('🔍 Starting memory monitoring...');
        return setInterval(() => {
            const breakdown = this.getMemoryBreakdown();
            const heapMB = (breakdown.total.heapUsed / 1024 / 1024).toFixed(2);
            const rssMB = (breakdown.total.rss / 1024 / 1024).toFixed(2);
            console.log(`[Memory Monitor] Heap: ${heapMB}MB | RSS: ${rssMB}MB | External: ${(breakdown.total.external / 1024 / 1024).toFixed(2)}MB`);
            if (breakdown.total.heapUsed > 300 * 1024 * 1024) {
                console.warn(`⚠️  High memory usage detected: ${heapMB}MB heap`);
            }
        }, intervalMs);
    }
    measureGCImpact() {
        const before = process.memoryUsage().heapUsed;
        if (global.gc) {
            global.gc();
        }
        const after = process.memoryUsage().heapUsed;
        const freed = before - after;
        console.log(`🗑️  GC Impact: Freed ${(freed / 1024 / 1024).toFixed(2)}MB`);
        return { before, after, freed };
    }
}
exports.MemoryAnalyzer = MemoryAnalyzer;
global.memoryAnalyzer = MemoryAnalyzer.getInstance();
