"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WelcomeTemplate = void 0;
const mongoose_1 = require("mongoose");
const welcomeFieldSchema = new mongoose_1.Schema({
    name: {
        type: String,
        required: [true, 'Field name is required'],
        maxlength: [256, 'Field name cannot exceed 256 characters']
    },
    value: {
        type: String,
        required: [true, 'Field value is required'],
        maxlength: [1024, 'Field value cannot exceed 1024 characters']
    },
    inline: {
        type: Boolean,
        default: false
    }
});
const welcomeButtonSchema = new mongoose_1.Schema({
    label: {
        type: String,
        required: [true, 'Button label is required'],
        maxlength: [80, 'Button label cannot exceed 80 characters']
    },
    style: {
        type: String,
        enum: ['Link'],
        required: [true, 'Button style is required'],
        default: 'Link'
    },
    url: {
        type: String,
        required: [true, 'URL is required for link buttons'],
        validate: {
            validator: function (v) {
                try {
                    new URL(v);
                    return true;
                }
                catch {
                    return false;
                }
            },
            message: 'URL must be a valid URL'
        }
    }
});
const welcomeTemplateSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    name: {
        type: String,
        required: [true, 'Template name is required'],
        maxlength: [100, 'Template name cannot exceed 100 characters'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Template name cannot be empty'
        }
    },
    triggerType: {
        type: String,
        enum: ['join', 'role_add', 'role_remove'],
        required: [true, 'Trigger type is required']
    },
    triggerRoleId: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || (v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Trigger role ID must be a valid Discord snowflake'
        }
    },
    deliveryType: {
        type: String,
        enum: ['dm', 'channel', 'both'],
        required: [true, 'Delivery type is required']
    },
    channelId: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || (v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Channel ID must be a valid Discord snowflake'
        }
    },
    useEmbed: {
        type: Boolean,
        default: true
    },
    title: {
        type: String,
        maxlength: [256, 'Title cannot exceed 256 characters']
    },
    description: {
        type: String,
        maxlength: [4096, 'Description cannot exceed 4096 characters']
    },
    color: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^#[0-9A-Fa-f]{6}$/.test(v);
            },
            message: 'Color must be a valid hex color code (e.g., #dd7d00)'
        }
    },
    thumbnailUrl: {
        type: String,
        validate: {
            validator: function (v) {
                if (!v)
                    return true;
                try {
                    new URL(v);
                    return true;
                }
                catch {
                    return false;
                }
            },
            message: 'Thumbnail URL must be a valid URL'
        }
    },
    imageUrl: {
        type: String,
        validate: {
            validator: function (v) {
                if (!v)
                    return true;
                try {
                    new URL(v);
                    return true;
                }
                catch {
                    return false;
                }
            },
            message: 'Image URL must be a valid URL'
        }
    },
    footerText: {
        type: String,
        maxlength: [2048, 'Footer text cannot exceed 2048 characters']
    },
    showTimestamp: {
        type: Boolean,
        default: true
    },
    fields: [welcomeFieldSchema],
    buttons: [welcomeButtonSchema],
    delaySeconds: {
        type: Number,
        default: 0,
        min: [0, 'Delay cannot be negative'],
        max: [3600, 'Delay cannot exceed 1 hour (3600 seconds)']
    },
    enabled: {
        type: Boolean,
        default: true
    },
    priority: {
        type: Number,
        default: 0,
        min: [0, 'Priority cannot be negative']
    },
    conditions: {
        minAccountAge: {
            type: Number,
            min: [0, 'Minimum account age cannot be negative'],
            max: [365, 'Minimum account age cannot exceed 365 days']
        },
        requireVerification: {
            type: Boolean,
            default: false
        },
        excludeRoles: [{
                type: String,
                validate: {
                    validator: function (v) {
                        return /^\d{17,20}$/.test(v);
                    },
                    message: 'Role ID must be a valid Discord snowflake'
                }
            }],
        includeRoles: [{
                type: String,
                validate: {
                    validator: function (v) {
                        return /^\d{17,20}$/.test(v);
                    },
                    message: 'Role ID must be a valid Discord snowflake'
                }
            }]
    }
}, {
    timestamps: true
});
welcomeTemplateSchema.index({ guildId: 1, triggerType: 1, enabled: 1 });
welcomeTemplateSchema.index({ guildId: 1, triggerRoleId: 1 });
welcomeTemplateSchema.index({ guildId: 1, name: 1 }, { unique: true });
exports.WelcomeTemplate = (0, mongoose_1.model)('WelcomeTemplate', welcomeTemplateSchema);
