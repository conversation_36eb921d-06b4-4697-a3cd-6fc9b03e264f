"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DynamicPricingService = void 0;
const LeaderboardManager_1 = require("./economy/managers/LeaderboardManager");
const logger_1 = require("../core/logger");
class DynamicPricingService {
    constructor() {
        this.economyCache = {};
        this.CACHE_TTL = 5 * 60 * 1000;
        this.MIN_ECONOMY_VALUE = 1000;
        this.MIN_PERCENTAGE_PRICE = 10;
    }
    static getInstance() {
        if (!DynamicPricingService.instance) {
            DynamicPricingService.instance = new DynamicPricingService();
        }
        return DynamicPricingService.instance;
    }
    parsePriceInput(priceInput) {
        if (typeof priceInput === 'number') {
            return {
                priceType: 'fixed',
                value: priceInput
            };
        }
        const stringInput = priceInput.toString().trim();
        if (stringInput.endsWith('%')) {
            const percentageStr = stringInput.slice(0, -1);
            const percentageValue = parseFloat(percentageStr);
            if (isNaN(percentageValue) || percentageValue < 0.1 || percentageValue > 50) {
                throw new Error('Percentage must be between 0.1% and 50%');
            }
            return {
                priceType: 'percentage',
                value: 0,
                percentageValue
            };
        }
        const fixedValue = parseInt(stringInput);
        if (isNaN(fixedValue) || fixedValue < 0) {
            throw new Error('Fixed price must be a non-negative integer');
        }
        return {
            priceType: 'fixed',
            value: fixedValue
        };
    }
    async getTotalEconomyValue(guildId) {
        const now = Date.now();
        const cached = this.economyCache[guildId];
        if (cached && (now - cached.timestamp) < cached.ttl) {
            return cached.totalEconomyValue;
        }
        try {
            const logger = (0, logger_1.createLogger)('DynamicPricingService');
            const leaderboardManager = new LeaderboardManager_1.LeaderboardManager(logger);
            const stats = await leaderboardManager.getLeaderboardStats(guildId);
            const totalEconomyValue = Math.max(stats.totalEconomyValue, this.MIN_ECONOMY_VALUE);
            this.economyCache[guildId] = {
                totalEconomyValue,
                timestamp: now,
                ttl: this.CACHE_TTL
            };
            return totalEconomyValue;
        }
        catch (error) {
            console.error(`Failed to get economy stats for guild ${guildId}:`, error);
            if (cached) {
                return cached.totalEconomyValue;
            }
            return this.MIN_ECONOMY_VALUE;
        }
    }
    async calculatePrice(role, guildId) {
        if (role.priceType === 'fixed') {
            return {
                displayPrice: role.price,
                actualPrice: role.price,
                isPercentageBased: false
            };
        }
        if (role.priceType === 'percentage' && role.percentageValue) {
            const economyTotal = await this.getTotalEconomyValue(guildId);
            const calculatedPrice = Math.floor((role.percentageValue / 100) * economyTotal);
            const actualPrice = Math.max(calculatedPrice, this.MIN_PERCENTAGE_PRICE);
            return {
                displayPrice: actualPrice,
                actualPrice,
                isPercentageBased: true,
                percentageValue: role.percentageValue,
                economyTotal
            };
        }
        return {
            displayPrice: role.price,
            actualPrice: role.price,
            isPercentageBased: false
        };
    }
    async calculatePricesForRoles(roles, guildId) {
        const results = new Map();
        const needsEconomyData = roles.some(role => role.priceType === 'percentage');
        let economyTotal = 0;
        if (needsEconomyData) {
            economyTotal = await this.getTotalEconomyValue(guildId);
        }
        for (const role of roles) {
            if (role.priceType === 'fixed') {
                results.set(role.roleId, {
                    displayPrice: role.price,
                    actualPrice: role.price,
                    isPercentageBased: false
                });
            }
            else if (role.priceType === 'percentage' && role.percentageValue) {
                const calculatedPrice = Math.floor((role.percentageValue / 100) * economyTotal);
                const actualPrice = Math.max(calculatedPrice, this.MIN_PERCENTAGE_PRICE);
                results.set(role.roleId, {
                    displayPrice: actualPrice,
                    actualPrice,
                    isPercentageBased: true,
                    percentageValue: role.percentageValue,
                    economyTotal
                });
            }
            else {
                results.set(role.roleId, {
                    displayPrice: role.price,
                    actualPrice: role.price,
                    isPercentageBased: false
                });
            }
        }
        return results;
    }
    clearGuildCache(guildId) {
        delete this.economyCache[guildId];
    }
    clearAllCache() {
        this.economyCache = {};
    }
    getCacheStats() {
        const guilds = Object.keys(this.economyCache);
        const now = Date.now();
        let oldestEntry = null;
        for (const guildId of guilds) {
            const entry = this.economyCache[guildId];
            if (oldestEntry === null || entry.timestamp < oldestEntry) {
                oldestEntry = entry.timestamp;
            }
        }
        return {
            totalCached: guilds.length,
            oldestEntry: oldestEntry ? now - oldestEntry : null
        };
    }
}
exports.DynamicPricingService = DynamicPricingService;
exports.default = DynamicPricingService.getInstance();
