"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserManager = void 0;
const errorHandler_1 = require("../../../utils/errorHandler");
const features_1 = require("../../../config/features");
const User_1 = __importDefault(require("../../../models/User"));
class UserManager {
    constructor(logger) {
        this.logger = logger;
    }
    async ensureUser(discordId, guildId) {
        try {
            const trimmedDiscordId = this.validateAndSanitizeDiscordId(discordId);
            const trimmedGuildId = guildId.trim();
            if (!trimmedGuildId) {
                throw new errorHandler_1.DatabaseError('Guild ID is required');
            }
            const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId, guildId: trimmedGuildId }, { $setOnInsert: { discordId: trimmedDiscordId, guildId: trimmedGuildId, balance: 0 } }, { new: true, upsert: true, runValidators: true });
            this.logOperation('User ensured', { discordId: trimmedDiscordId, guildId: trimmedGuildId, userId: user._id });
            return user;
        }
        catch (error) {
            this.handleError(error, { discordId, guildId });
            throw new errorHandler_1.DatabaseError(`Failed to ensure user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getUser(discordId, guildId) {
        try {
            const trimmedDiscordId = this.validateAndSanitizeDiscordId(discordId);
            const trimmedGuildId = guildId.trim();
            if (!trimmedGuildId) {
                throw new errorHandler_1.DatabaseError('Guild ID is required');
            }
            const user = await User_1.default.findOne({ discordId: trimmedDiscordId, guildId: trimmedGuildId }).lean();
            this.logOperation('User retrieved', { discordId: trimmedDiscordId, guildId: trimmedGuildId, found: !!user });
            return user;
        }
        catch (error) {
            this.handleError(error, { discordId, guildId });
            throw new errorHandler_1.DatabaseError(`Failed to get user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async createUser(discordId, initialBalance = 0) {
        try {
            const trimmedDiscordId = this.validateAndSanitizeDiscordId(discordId);
            const user = new User_1.default({
                discordId: trimmedDiscordId,
                balance: initialBalance
            });
            await user.save();
            this.logOperation('User created', {
                discordId: trimmedDiscordId,
                userId: user._id,
                initialBalance
            });
            return user;
        }
        catch (error) {
            this.handleError(error, { discordId, initialBalance });
            throw new errorHandler_1.DatabaseError(`Failed to create user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async updateUser(discordId, guildId, updateData) {
        try {
            const trimmedDiscordId = this.validateAndSanitizeDiscordId(discordId);
            const trimmedGuildId = guildId.trim();
            if (!trimmedGuildId) {
                throw new errorHandler_1.DatabaseError('Guild ID is required');
            }
            const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId, guildId: trimmedGuildId }, updateData, { new: true, runValidators: true });
            if (!user) {
                throw new Error(`User not found in guild: ${trimmedDiscordId}`);
            }
            this.logOperation('User updated', {
                discordId: trimmedDiscordId,
                guildId: trimmedGuildId,
                userId: user._id,
                updateData
            });
            return user;
        }
        catch (error) {
            this.handleError(error, { discordId, guildId, updateData });
            throw new errorHandler_1.DatabaseError(`Failed to update user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async deleteUser(discordId) {
        try {
            const trimmedDiscordId = this.validateAndSanitizeDiscordId(discordId);
            const result = await User_1.default.deleteOne({ discordId: trimmedDiscordId });
            this.logOperation('User deletion attempted', {
                discordId: trimmedDiscordId,
                deleted: result.deletedCount > 0
            });
            return result.deletedCount > 0;
        }
        catch (error) {
            this.handleError(error, { discordId });
            throw new errorHandler_1.DatabaseError(`Failed to delete user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    validateAndSanitizeDiscordId(discordId) {
        if (!discordId || typeof discordId !== 'string') {
            throw new errorHandler_1.DatabaseError('Discord ID must be a non-empty string');
        }
        const trimmedId = discordId.trim();
        if (!trimmedId) {
            throw new errorHandler_1.DatabaseError('Discord ID cannot be empty');
        }
        if (!/^\d{17,20}$/.test(trimmedId)) {
            throw new errorHandler_1.DatabaseError('Invalid Discord ID format');
        }
        return trimmedId;
    }
    logOperation(operation, details) {
        this.logger.debug(`[UserManager] ${operation}`, details);
    }
    handleError(error, context) {
        this.logger.error('[UserManager] Error', {
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
            } : error,
            context,
        });
    }
}
exports.UserManager = UserManager;
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UserManager.prototype, "ensureUser", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UserManager.prototype, "getUser", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], UserManager.prototype, "createUser", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], UserManager.prototype, "updateUser", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserManager.prototype, "deleteUser", null);
