"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class MemoryManager {
    constructor() {
        this.memoryStats = [];
        this.MAX_MEMORY_STATS = 50;
        this.MEMORY_CHECK_INTERVAL = 10 * 60 * 1000;
        this.MAX_HEAP_USAGE_MB = 120;
        this.CRITICAL_HEAP_USAGE_MB = 200;
        this.startMemoryMonitoring();
    }
    static getInstance() {
        if (!MemoryManager.instance) {
            MemoryManager.instance = new MemoryManager();
        }
        return MemoryManager.instance;
    }
    startMemoryMonitoring() {
        this.memoryCheckTimer = setInterval(() => {
            this.recordMemoryStats();
            this.checkMemoryUsage();
        }, this.MEMORY_CHECK_INTERVAL);
        console.log('[Memory Manager] Started memory monitoring');
    }
    stopMemoryMonitoring() {
        if (this.memoryCheckTimer) {
            clearInterval(this.memoryCheckTimer);
            this.memoryCheckTimer = undefined;
        }
        console.log('[Memory Manager] Stopped memory monitoring');
    }
    recordMemoryStats() {
        const memUsage = process.memoryUsage();
        const stats = {
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            external: memUsage.external,
            rss: memUsage.rss,
            timestamp: Date.now()
        };
        this.memoryStats.push(stats);
        if (this.memoryStats.length > this.MAX_MEMORY_STATS) {
            this.memoryStats = this.memoryStats.slice(-this.MAX_MEMORY_STATS);
        }
    }
    checkMemoryUsage() {
        const current = process.memoryUsage();
        const heapUsedMB = current.heapUsed / 1024 / 1024;
        const rssMB = current.rss / 1024 / 1024;
        if (heapUsedMB > this.CRITICAL_HEAP_USAGE_MB) {
            console.error(`[Memory Manager] CRITICAL heap usage detected: ${heapUsedMB.toFixed(2)}MB - performing emergency cleanup`);
            this.performEmergencyCleanup();
        }
        else if (heapUsedMB > this.MAX_HEAP_USAGE_MB) {
            console.warn(`[Memory Manager] High heap usage detected: ${heapUsedMB.toFixed(2)}MB`);
            this.performGentleCleanup();
        }
        if (this.memoryStats.length > 0 && this.memoryStats.length % 6 === 0) {
            console.log(`[Memory Manager] Memory stats - Heap: ${heapUsedMB.toFixed(2)}MB, RSS: ${rssMB.toFixed(2)}MB`);
        }
    }
    performGentleCleanup() {
        console.log('[Memory Manager] Performing gentle cleanup');
        if (this.memoryStats.length > this.MAX_MEMORY_STATS / 2) {
            this.memoryStats = this.memoryStats.slice(-Math.floor(this.MAX_MEMORY_STATS / 2));
        }
        if (global.gc && typeof global.gc === 'function') {
            setImmediate(() => {
                try {
                    global.gc();
                }
                catch (error) {
                }
            });
        }
        console.log('[Memory Manager] Gentle cleanup completed');
    }
    performEmergencyCleanup() {
        console.log('[Memory Manager] Performing EMERGENCY cleanup');
        this.memoryStats = this.memoryStats.slice(-10);
        if (global.gc && typeof global.gc === 'function') {
            try {
                global.gc();
                setTimeout(() => {
                    try {
                        global.gc();
                    }
                    catch (error) {
                    }
                }, 100);
            }
            catch (error) {
                console.warn('[Memory Manager] Failed to run garbage collection:', error.message);
            }
        }
        console.log('[Memory Manager] Emergency cleanup completed');
    }
    getMemoryStats() {
        const current = process.memoryUsage();
        const currentStats = {
            heapUsed: current.heapUsed,
            heapTotal: current.heapTotal,
            external: current.external,
            rss: current.rss,
            timestamp: Date.now()
        };
        const recentStats = this.memoryStats.slice(-10);
        const average = {};
        if (recentStats.length > 0) {
            average.heapUsed = recentStats.reduce((sum, stat) => sum + stat.heapUsed, 0) / recentStats.length;
            average.heapTotal = recentStats.reduce((sum, stat) => sum + stat.heapTotal, 0) / recentStats.length;
            average.rss = recentStats.reduce((sum, stat) => sum + stat.rss, 0) / recentStats.length;
        }
        return {
            current: currentStats,
            average
        };
    }
}
exports.default = MemoryManager;
