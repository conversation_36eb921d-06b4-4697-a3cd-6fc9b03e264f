"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BalanceCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const embedBuilder_1 = require("../../utils/embedBuilder");
const economyService_1 = require("../../services/economyService");
class BalanceCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'balance',
            description: 'Check your Phalanx Loyalty Coin balance',
            category: BaseCommand_1.CommandCategory.ECONOMY,
            requiredFeatures: ['ECONOMY_SYSTEM'],
        });
    }
    async executeCommand(context) {
        const { interaction } = context;
        const discordId = interaction.user.id;
        const guildId = interaction.guild?.id;
        if (!guildId) {
            throw new Error('This command can only be used in a server');
        }
        try {
            const user = await (0, economyService_1.ensureUser)(discordId, guildId);
            const formattedBalance = await (0, embedBuilder_1.formatServerCoins)(guildId, user.balance);
            const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Your Balance');
            embed.setDescription(`${embedBuilder_1.EMOJIS.ECONOMY.COINS} **${formattedBalance}**`);
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            const buttons = (0, embedBuilder_1.createQuickActionButtons)();
            await interaction.reply({
                embeds: [embed],
                components: [buttons],
                ephemeral: false
            });
            this.logger.info(`Balance checked for user ${interaction.user.username}: ${user.balance} PLC`);
        }
        catch (error) {
            this.logger.error('Error executing balance command', { error, userId: discordId });
            throw error;
        }
    }
}
exports.BalanceCommand = BalanceCommand;
