"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeAdminCommand = void 0;
const discord_js_1 = require("discord.js");
const BaseCommand_1 = require("../base/BaseCommand");
const embedBuilder_1 = require("../../utils/embedBuilder");
const errorHandler_1 = require("../../utils/errorHandler");
const models_1 = require("../../models");
const constants_1 = require("../../config/constants");
class TradeAdminCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'tradeadmin',
            description: 'Administrative commands for the trade system',
            category: BaseCommand_1.CommandCategory.ADMIN,
            requiredFeatures: ['TRADE_SYSTEM'],
            adminOnly: true,
            cooldown: 3,
        });
        this.tradeService = null;
        this.disputeService = null;
    }
    setTradeServices(tradeService, disputeService) {
        this.tradeService = tradeService;
        this.disputeService = disputeService;
    }
    customizeCommand(command) {
        command
            .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator)
            .addSubcommand(subcommand => subcommand
            .setName('disputes')
            .setDescription('View active disputes')
            .addIntegerOption(option => option.setName('limit')
            .setDescription('Number of disputes to show (default: 10)')
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(50)))
            .addSubcommand(subcommand => subcommand
            .setName('resolve')
            .setDescription('Resolve a dispute')
            .addStringOption(option => option.setName('dispute_id')
            .setDescription('Dispute ID to resolve')
            .setRequired(true))
            .addStringOption(option => option.setName('resolution')
            .setDescription('Resolution type')
            .setRequired(true)
            .addChoices({ name: 'Favor Initiator', value: 'FAVOR_INITIATOR' }, { name: 'Favor Respondent', value: 'FAVOR_RESPONDENT' }, { name: 'Split Escrow 50/50', value: 'SPLIT_ESCROW' }, { name: 'Full Refund', value: 'FULL_REFUND' }, { name: 'Custom Amount', value: 'CUSTOM' }))
            .addStringOption(option => option.setName('details')
            .setDescription('Resolution details/explanation')
            .setRequired(true)
            .setMaxLength(500))
            .addIntegerOption(option => option.setName('amount')
            .setDescription('Custom amount for seller (required for custom resolution)')
            .setRequired(false)
            .setMinValue(0))
            .addStringOption(option => option.setName('notes')
            .setDescription('Admin notes (optional)')
            .setRequired(false)
            .setMaxLength(200)))
            .addSubcommand(subcommand => subcommand
            .setName('cancel')
            .setDescription('Cancel a trade (emergency use)')
            .addStringOption(option => option.setName('trade_id')
            .setDescription('Trade ID to cancel')
            .setRequired(true))
            .addStringOption(option => option.setName('reason')
            .setDescription('Reason for cancellation')
            .setRequired(true)
            .setMaxLength(200)))
            .addSubcommand(subcommand => subcommand
            .setName('stats')
            .setDescription('View trade statistics for a user')
            .addUserOption(option => option.setName('user')
            .setDescription('User to view stats for')
            .setRequired(true)))
            .addSubcommand(subcommand => subcommand
            .setName('restrict')
            .setDescription('Restrict a user from trading')
            .addUserOption(option => option.setName('user')
            .setDescription('User to restrict')
            .setRequired(true))
            .addStringOption(option => option.setName('reason')
            .setDescription('Reason for restriction')
            .setRequired(true)
            .setMaxLength(200))
            .addIntegerOption(option => option.setName('days')
            .setDescription('Number of days to restrict (default: 7)')
            .setRequired(false)
            .setMinValue(1)
            .setMaxValue(365)))
            .addSubcommand(subcommand => subcommand
            .setName('unrestrict')
            .setDescription('Remove trading restriction from a user')
            .addUserOption(option => option.setName('user')
            .setDescription('User to unrestrict')
            .setRequired(true)))
            .addSubcommand(subcommand => subcommand
            .setName('overview')
            .setDescription('Get trade system overview and statistics'));
    }
    async executeCommand(context) {
        const { interaction } = context;
        if (!this.tradeService || !this.disputeService) {
            throw new Error('Trade services not initialized');
        }
        const subcommand = interaction.options.getSubcommand();
        try {
            switch (subcommand) {
                case 'disputes':
                    await this.handleDisputes(interaction);
                    break;
                case 'resolve':
                    await this.handleResolve(interaction);
                    break;
                case 'cancel':
                    await this.handleCancel(interaction);
                    break;
                case 'stats':
                    await this.handleStats(interaction);
                    break;
                case 'restrict':
                    await this.handleRestrict(interaction);
                    break;
                case 'unrestrict':
                    await this.handleUnrestrict(interaction);
                    break;
                case 'overview':
                    await this.handleOverview(interaction);
                    break;
                default:
                    throw new errorHandler_1.ValidationError(`Unknown subcommand: ${subcommand}`);
            }
        }
        catch (error) {
            this.logger.error(`Error executing tradeadmin ${subcommand}`, { error, userId: interaction.user.id });
            throw error;
        }
    }
    async handleDisputes(interaction) {
        const limit = interaction.options.getInteger('limit') || 10;
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server');
        }
        const disputes = await this.disputeService.getActiveDisputes(interaction.guild.id, limit);
        if (disputes.length === 0) {
            const embed = await (0, embedBuilder_1.createServerAdminEmbed)(interaction.guild.id, 'No Active Disputes');
            embed.setDescription('There are currently no active disputes to review.');
            await interaction.reply({ embeds: [embed] });
            return;
        }
        const embed = await (0, embedBuilder_1.createServerAdminEmbed)(interaction.guild.id, `${embedBuilder_1.EMOJIS.TRADE.DISPUTED} Active Disputes`);
        embed.setDescription(`Found ${disputes.length} active dispute(s)`);
        for (const dispute of disputes.slice(0, 10)) {
            const statusEmoji = dispute.status === 'EVIDENCE_COLLECTION' ? '📝' :
                dispute.status === 'UNDER_REVIEW' ? '👁️' : '🔍';
            const priorityEmoji = dispute.priority === 'URGENT' ? '🔴' :
                dispute.priority === 'HIGH' ? '🟠' :
                    dispute.priority === 'MEDIUM' ? '🟡' : '🟢';
            embed.addFields([{
                    name: `${statusEmoji} ${dispute.disputeId}`,
                    value: `**Trade:** \`${dispute.tradeId}\`\n` +
                        `**Priority:** ${priorityEmoji} ${dispute.priority}\n` +
                        `**Category:** ${dispute.category}\n` +
                        `**Created:** <t:${Math.floor(dispute.createdAt.getTime() / 1000)}:R>\n` +
                        `**Reason:** ${dispute.reason.substring(0, 100)}${dispute.reason.length > 100 ? '...' : ''}`,
                    inline: true
                }]);
        }
        if (disputes.length > 10) {
            embed.setFooter({ text: `Showing first 10 of ${disputes.length} disputes` });
        }
        await interaction.reply({ embeds: [embed] });
    }
    async handleResolve(interaction) {
        const disputeId = interaction.options.getString('dispute_id', true);
        const resolution = interaction.options.getString('resolution', true);
        const details = interaction.options.getString('details', true);
        const customAmount = interaction.options.getInteger('amount');
        const adminNotes = interaction.options.getString('notes');
        if (resolution === 'CUSTOM' && customAmount === null) {
            throw new errorHandler_1.ValidationError('Custom resolution requires an amount');
        }
        await interaction.deferReply();
        const resolvedDispute = await this.disputeService.resolveDispute({
            disputeId,
            adminId: interaction.user.id,
            resolution,
            resolutionDetails: details,
            resolutionAmount: customAmount || undefined,
            adminNotes: adminNotes || undefined
        }, interaction.client);
        const guildId = interaction.guild.id;
        const formattedAmount = customAmount ? await (0, embedBuilder_1.formatServerCoins)(guildId, customAmount) : '';
        const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Dispute Resolved');
        embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.SCALES} **Dispute Successfully Resolved**\n\n` +
            `**Dispute ID:** \`${disputeId}\`\n` +
            `**Resolution:** ${resolution.replace('_', ' ')}\n` +
            `**Details:** ${details}\n` +
            `${customAmount ? `**Amount:** ${formattedAmount}\n` : ''}` +
            `**Resolved by:** ${interaction.user.displayName}`);
        await interaction.editReply({ embeds: [embed] });
    }
    async handleCancel(interaction) {
        const tradeId = interaction.options.getString('trade_id', true);
        const reason = interaction.options.getString('reason', true);
        await interaction.deferReply();
        const trade = await this.tradeService.getTrade(tradeId);
        if (!trade) {
            throw new errorHandler_1.ValidationError('Trade not found');
        }
        await this.tradeService.cancelTrade(tradeId, interaction.user.id, `Admin cancellation: ${reason}`, interaction.client);
        const guildId = interaction.guild.id;
        const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Trade Cancelled');
        embed.setDescription(`${embedBuilder_1.EMOJIS.TRADE.CANCELLED} **Trade Cancelled by Admin**\n\n` +
            `**Trade ID:** \`${tradeId}\`\n` +
            `**Reason:** ${reason}\n` +
            `**Cancelled by:** ${interaction.user.displayName}\n\n` +
            `Any escrowed funds have been refunded to the buyer.`);
        await interaction.editReply({ embeds: [embed] });
    }
    async handleStats(interaction) {
        const user = interaction.options.getUser('user', true);
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server');
        }
        const userStats = await models_1.UserTradeStats.findOne({
            discordId: user.id,
            guildId: interaction.guild.id
        });
        if (!userStats) {
            const embed = await (0, embedBuilder_1.createServerAdminEmbed)(interaction.guild.id, 'No Trade Data');
            embed.setDescription(`${user.displayName} has no trade history.`);
            await interaction.reply({ embeds: [embed] });
            return;
        }
        const { createUserTradeStatsEmbed } = require('../../utils/tradeEmbedBuilder');
        const embed = createUserTradeStatsEmbed(user, userStats);
        embed.addFields([{
                name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Admin Information`,
                value: `**User ID:** \`${user.id}\`\n` +
                    `**Warnings:** ${userStats.warningsReceived}\n` +
                    `**Violations:** ${userStats.violationHistory.length}\n` +
                    `**Last Updated:** <t:${Math.floor(userStats.lastUpdated.getTime() / 1000)}:R>`,
                inline: false
            }]);
        await interaction.reply({ embeds: [embed] });
    }
    async handleRestrict(interaction) {
        const user = interaction.options.getUser('user', true);
        const reason = interaction.options.getString('reason', true);
        const days = interaction.options.getInteger('days') || 7;
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server');
        }
        let userStats = await models_1.UserTradeStats.findOne({
            discordId: user.id,
            guildId: interaction.guild.id
        });
        if (!userStats) {
            userStats = new models_1.UserTradeStats({
                discordId: user.id,
                guildId: interaction.guild.id,
            });
        }
        const restrictedUntil = new Date();
        restrictedUntil.setDate(restrictedUntil.getDate() + days);
        userStats.isRestricted = true;
        userStats.restrictionReason = reason;
        userStats.restrictedUntil = restrictedUntil;
        userStats.warningsReceived += 1;
        userStats.violationHistory.push(`[${new Date().toISOString()}] Restricted by ${interaction.user.displayName}: ${reason}`);
        await userStats.save();
        const guildId = interaction.guild.id;
        const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'User Restricted');
        embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.LOCK} **User Trading Restricted**\n\n` +
            `**User:** ${user.displayName}\n` +
            `**Reason:** ${reason}\n` +
            `**Duration:** ${days} day(s)\n` +
            `**Until:** <t:${Math.floor(restrictedUntil.getTime() / 1000)}:F>\n` +
            `**Restricted by:** ${interaction.user.displayName}`);
        await interaction.reply({ embeds: [embed] });
    }
    async handleUnrestrict(interaction) {
        const user = interaction.options.getUser('user', true);
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server');
        }
        const userStats = await models_1.UserTradeStats.findOne({
            discordId: user.id,
            guildId: interaction.guild.id
        });
        if (!userStats || !userStats.isRestricted) {
            throw new errorHandler_1.ValidationError('User is not currently restricted from trading');
        }
        userStats.isRestricted = false;
        userStats.restrictionReason = undefined;
        userStats.restrictedUntil = undefined;
        userStats.violationHistory.push(`[${new Date().toISOString()}] Restriction removed by ${interaction.user.displayName}`);
        await userStats.save();
        const guildId = interaction.guild.id;
        const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'User Unrestricted');
        embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.KEY} **Trading Restriction Removed**\n\n` +
            `**User:** ${user.displayName}\n` +
            `**Unrestricted by:** ${interaction.user.displayName}\n\n` +
            `The user can now participate in trades again.`);
        await interaction.reply({ embeds: [embed] });
    }
    async handleOverview(interaction) {
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server');
        }
        await interaction.deferReply();
        try {
            const [totalTrades, activeTrades, completedTrades, disputedTrades, totalVolume, activeDisputes] = await Promise.all([
                models_1.Trade.countDocuments({ guildId: interaction.guild.id }),
                models_1.Trade.countDocuments({
                    guildId: interaction.guild.id,
                    state: { $in: ['PROPOSED', 'ACCEPTED', 'ACTIVE'] }
                }),
                models_1.Trade.countDocuments({
                    guildId: interaction.guild.id,
                    state: 'COMPLETED'
                }),
                models_1.Trade.countDocuments({
                    guildId: interaction.guild.id,
                    state: 'DISPUTED'
                }),
                models_1.Trade.aggregate([
                    { $match: { guildId: interaction.guild.id, state: 'COMPLETED' } },
                    { $group: { _id: null, total: { $sum: '$amount' } } }
                ]),
                this.disputeService.getActiveDisputes(interaction.guild.id)
            ]);
            let backgroundStats = null;
            try {
                const backgroundService = interaction.client.app?.getService('TradeBackgroundService');
                if (backgroundService && typeof backgroundService.getTaskStats === 'function') {
                    backgroundStats = backgroundService.getTaskStats();
                }
            }
            catch (error) {
            }
            const topTraders = await models_1.UserTradeStats.find({
                guildId: interaction.guild.id,
                totalTrades: { $gt: 0 }
            })
                .sort({ totalVolumeTraded: -1 })
                .limit(5)
                .lean();
            const embed = await (0, embedBuilder_1.createServerAdminEmbed)(interaction.guild.id, `${embedBuilder_1.EMOJIS.ECONOMY.CHART} Trade System Overview`);
            embed.setDescription(`Statistics for ${interaction.guild.name}`);
            embed.addFields([
                {
                    name: `${embedBuilder_1.EMOJIS.TRADE.HANDSHAKE} Trade Statistics`,
                    value: `**Total Trades:** ${totalTrades}\n` +
                        `**Active Trades:** ${activeTrades}\n` +
                        `**Completed:** ${completedTrades}\n` +
                        `**Disputed:** ${disputedTrades}`,
                    inline: true
                },
                {
                    name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Financial`,
                    value: `**Total Volume:** ${await (0, embedBuilder_1.formatServerCoins)(interaction.guild.id, totalVolume[0]?.total || 0)}\n` +
                        `**Avg Trade Value:** ${totalTrades > 0 ? await (0, embedBuilder_1.formatServerCoins)(interaction.guild.id, Math.round((totalVolume[0]?.total || 0) / totalTrades)) : '0 coins'}\n` +
                        `**Success Rate:** ${totalTrades > 0 ? ((completedTrades / totalTrades) * 100).toFixed(1) : '0'}%`,
                    inline: true
                },
                {
                    name: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} System Health`,
                    value: `**Active Disputes:** ${activeDisputes.length}\n` +
                        `**Dispute Rate:** ${totalTrades > 0 ? ((disputedTrades / totalTrades) * 100).toFixed(1) : '0'}%\n` +
                        `**System Status:** ${activeTrades < 100 ? '🟢 Healthy' : activeTrades < 200 ? '🟡 Busy' : '🔴 Overloaded'}`,
                    inline: true
                }
            ]);
            if (backgroundStats) {
                embed.addFields([{
                        name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Background Tasks`,
                        value: `**Expired Trades:** ${backgroundStats.expiredTrades}\n` +
                            `**Warnings Sent:** ${backgroundStats.warningsSent}\n` +
                            `**Cleanup Ops:** ${backgroundStats.cleanupOperations}\n` +
                            `**Errors:** ${backgroundStats.errors}\n` +
                            `**Last Run:** <t:${Math.floor(backgroundStats.lastRun.getTime() / 1000)}:R>`,
                        inline: true
                    }]);
            }
            if (topTraders.length > 0) {
                const topTradersPromises = topTraders.map(async (trader, index) => {
                    const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                    const formattedVolume = await (0, embedBuilder_1.formatServerCoins)(interaction.guild.id, trader.totalVolumeTraded);
                    return `${medal} <@${trader.discordId}> - ${formattedVolume} (${trader.totalTrades} trades)`;
                });
                const topTradersText = (await Promise.all(topTradersPromises)).join('\n');
                embed.addFields([{
                        name: `${embedBuilder_1.EMOJIS.SUCCESS.TROPHY} Top Traders`,
                        value: topTradersText,
                        inline: false
                    }]);
            }
            const minTradeFormatted = await (0, embedBuilder_1.formatServerCoins)(interaction.guild.id, constants_1.TRADE.MIN_TRADE_AMOUNT);
            const maxTradeFormatted = await (0, embedBuilder_1.formatServerCoins)(interaction.guild.id, constants_1.TRADE.MAX_TRADE_AMOUNT);
            embed.addFields([{
                    name: `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} System Configuration`,
                    value: `**Min Trade Amount:** ${minTradeFormatted}\n` +
                        `**Max Trade Amount:** ${maxTradeFormatted}\n` +
                        `**Max Active Trades:** ${constants_1.TRADE.MAX_ACTIVE_TRADES_PER_USER}\n` +
                        `**Daily Proposal Limit:** ${constants_1.TRADE.MAX_TRADE_PROPOSALS_PER_DAY}\n` +
                        `**Trade Expiration:** ${constants_1.TRADE.TRADE_EXPIRATION_HOURS} hours`,
                    inline: false
                }]);
            await interaction.editReply({ embeds: [embed] });
        }
        catch (error) {
            this.logger.error('Error generating trade overview', { error, guildId: interaction.guild.id });
            const errorEmbed = (0, embedBuilder_1.createErrorEmbed)('Overview Error', 'Failed to generate trade system overview. Please try again later.');
            await interaction.editReply({ embeds: [errorEmbed] });
        }
    }
}
exports.TradeAdminCommand = TradeAdminCommand;
