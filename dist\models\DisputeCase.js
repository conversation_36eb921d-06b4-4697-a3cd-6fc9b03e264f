"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const disputeCaseSchema = new mongoose_1.Schema({
    disputeId: {
        type: String,
        required: [true, 'Dispute ID is required'],
        unique: true,
        index: true
    },
    tradeId: {
        type: String,
        required: [true, 'Trade ID is required'],
        unique: true,
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    initiatorId: {
        type: String,
        required: [true, 'Initiator ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Initiator ID must be a valid Discord snowflake'
        },
        index: true
    },
    respondentId: {
        type: String,
        required: [true, 'Respondent ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Respondent ID must be a valid Discord snowflake'
        },
        index: true
    },
    assignedAdminId: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Assigned admin ID must be a valid Discord snowflake'
        },
        index: true
    },
    reason: {
        type: String,
        required: [true, 'Dispute reason is required'],
        maxlength: [200, 'Dispute reason cannot exceed 200 characters'],
        validate: {
            validator: function (v) {
                return v.trim().length > 0;
            },
            message: 'Dispute reason cannot be empty'
        }
    },
    description: {
        type: String,
        maxlength: [1000, 'Description cannot exceed 1000 characters']
    },
    category: {
        type: String,
        enum: ['ITEM_NOT_RECEIVED', 'ITEM_NOT_AS_DESCRIBED', 'PAYMENT_ISSUE', 'COMMUNICATION_ISSUE', 'OTHER'],
        required: [true, 'Dispute category is required'],
        index: true
    },
    initiatorEvidence: {
        type: [String],
        default: [],
        validate: {
            validator: function (v) {
                return v.length <= 5;
            },
            message: 'Cannot have more than 5 evidence items'
        }
    },
    respondentEvidence: {
        type: [String],
        default: [],
        validate: {
            validator: function (v) {
                return v.length <= 5;
            },
            message: 'Cannot have more than 5 evidence items'
        }
    },
    evidenceDeadline: {
        type: Date,
        required: [true, 'Evidence deadline is required'],
        index: true
    },
    status: {
        type: String,
        enum: ['OPEN', 'EVIDENCE_COLLECTION', 'UNDER_REVIEW', 'RESOLVED', 'APPEALED', 'CLOSED'],
        required: [true, 'Status is required'],
        default: 'OPEN',
        index: true
    },
    priority: {
        type: String,
        enum: ['LOW', 'MEDIUM', 'HIGH', 'URGENT'],
        required: [true, 'Priority is required'],
        default: 'MEDIUM',
        index: true
    },
    resolution: {
        type: String,
        enum: ['FAVOR_INITIATOR', 'FAVOR_RESPONDENT', 'SPLIT_ESCROW', 'FULL_REFUND', 'CUSTOM']
    },
    resolutionDetails: {
        type: String,
        maxlength: [500, 'Resolution details cannot exceed 500 characters']
    },
    resolutionAmount: {
        type: Number,
        min: [0, 'Resolution amount cannot be negative']
    },
    resolvedAt: {
        type: Date,
        index: true
    },
    appealedBy: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Appealed by must be a valid Discord snowflake'
        }
    },
    appealReason: {
        type: String,
        maxlength: [500, 'Appeal reason cannot exceed 500 characters']
    },
    appealedAt: {
        type: Date
    },
    appealDeadline: {
        type: Date,
        index: true
    },
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    lastUpdatedAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    adminNotes: {
        type: [String],
        default: []
    },
    internalComments: {
        type: String,
        maxlength: [1000, 'Internal comments cannot exceed 1000 characters']
    },
    escalationLevel: {
        type: Number,
        default: 0,
        min: [0, 'Escalation level cannot be negative'],
        index: true
    },
    tags: {
        type: [String],
        default: [],
        validate: {
            validator: function (v) {
                return v.length <= 10;
            },
            message: 'Cannot have more than 10 tags'
        }
    }
}, {
    timestamps: false
});
disputeCaseSchema.index({ status: 1, priority: -1, createdAt: -1 });
disputeCaseSchema.index({ assignedAdminId: 1, status: 1 });
disputeCaseSchema.index({ guildId: 1, status: 1 });
disputeCaseSchema.index({ createdAt: -1 });
disputeCaseSchema.pre('save', function (next) {
    this.lastUpdatedAt = new Date();
    next();
});
disputeCaseSchema.methods.isActive = function () {
    return ['OPEN', 'EVIDENCE_COLLECTION', 'UNDER_REVIEW', 'APPEALED'].includes(this.status);
};
disputeCaseSchema.methods.isEvidenceDeadlinePassed = function () {
    return new Date() > this.evidenceDeadline;
};
disputeCaseSchema.methods.isAppealDeadlinePassed = function () {
    return this.appealDeadline ? new Date() > this.appealDeadline : false;
};
disputeCaseSchema.methods.addAdminNote = function (note, adminId) {
    const timestamp = new Date().toISOString();
    this.adminNotes.push(`[${timestamp}] Admin ${adminId}: ${note}`);
    this.lastUpdatedAt = new Date();
};
exports.default = (0, mongoose_1.model)('DisputeCase', disputeCaseSchema);
