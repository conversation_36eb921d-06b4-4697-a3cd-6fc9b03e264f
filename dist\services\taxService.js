"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processTaxCollection = processTaxCollection;
exports.getNextTaxDate = getNextTaxDate;
exports.isTaxEnabled = isTaxEnabled;
const TaxConfiguration_1 = require("../models/TaxConfiguration");
const User_1 = require("../models/User");
const economyService_1 = require("./economyService");
const User_2 = __importDefault(require("../models/User"));
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
async function processTaxCollection(client, guildId) {
    const result = {
        totalProcessed: 0,
        totalTaxed: 0,
        totalRolesRemoved: 0,
        errors: []
    };
    try {
        const taxConfig = await TaxConfiguration_1.TaxConfiguration.findOne({ guildId, enabled: true });
        if (!taxConfig) {
            return result;
        }
        const now = new Date();
        if (!taxConfig.nextTaxDate || now < taxConfig.nextTaxDate) {
            return result;
        }
        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
            result.errors.push(`Guild ${guildId} not found`);
            return result;
        }
        const taxedRole = guild.roles.cache.get(taxConfig.roleId);
        if (!taxedRole) {
            result.errors.push(`Taxed role ${taxConfig.roleId} not found in guild ${guild.name}`);
            return result;
        }
        await guild.members.fetch();
        const membersWithRole = taxedRole.members;
        console.log(`[Tax Collection] Processing ${membersWithRole.size} members with role ${taxedRole.name} in guild ${guild.name}`);
        for (const [memberId, member] of membersWithRole) {
            try {
                result.totalProcessed++;
                const user = await User_2.default.findOne({ discordId: memberId, guildId: guild.id });
                const currentBalance = user?.balance || 0;
                let taxAmount;
                if (taxConfig.taxType === 'percentage') {
                    taxAmount = Math.floor((currentBalance * (taxConfig.percentageValue || 0)) / 100);
                    if (currentBalance > 0 && (taxConfig.percentageValue || 0) > 0 && taxAmount === 0) {
                        taxAmount = 1;
                    }
                }
                else {
                    taxAmount = taxConfig.amount;
                }
                if (currentBalance >= taxAmount && taxAmount > 0) {
                    const coinSymbol = await configurableConstants_1.default.getCoinSymbol(guild.id);
                    const taxDescription = taxConfig.taxType === 'percentage'
                        ? `Tax collection: ${taxConfig.percentageValue}% (${taxAmount} ${coinSymbol}) for role ${taxedRole.name}`
                        : `Tax collection: ${taxAmount} ${coinSymbol} for role ${taxedRole.name}`;
                    await (0, economyService_1.adjustBalance)(memberId, guild.id, -taxAmount, 'tax', taxDescription, client);
                    result.totalTaxed++;
                    console.log(`[Tax Collection] Taxed ${taxAmount} ${coinSymbol} from ${member.displayName}`);
                }
                else {
                    await handleInsufficientFundsForTax(member, taxAmount, currentBalance, taxConfig);
                    result.totalRolesRemoved++;
                    console.log(`[Tax Collection] Removed roles from ${member.displayName} due to insufficient funds`);
                }
            }
            catch (error) {
                const errorMsg = `Failed to process tax for member ${member.displayName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                result.errors.push(errorMsg);
                console.error(`[Tax Collection] ${errorMsg}`);
            }
        }
        taxConfig.lastTaxDate = now;
        taxConfig.nextTaxDate = new Date(now.getTime() + (taxConfig.frequency * 7 * 24 * 60 * 60 * 1000));
        await taxConfig.save();
        console.log(`[Tax Collection] Completed for guild ${guild.name}. Processed: ${result.totalProcessed}, Taxed: ${result.totalTaxed}, Roles Removed: ${result.totalRolesRemoved}`);
    }
    catch (error) {
        const errorMsg = `Tax collection failed for guild ${guildId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMsg);
        console.error(`[Tax Collection] ${errorMsg}`);
    }
    return result;
}
async function handleInsufficientFundsForTax(member, taxAmount, currentBalance, taxConfig) {
    try {
        const purchasableRoles = await User_1.RoleForSale.find({ guildId: member.guild.id });
        const purchasableRoleIds = new Set(purchasableRoles.map(role => role.roleId));
        const rolesToRemove = member.roles.cache.filter(role => purchasableRoleIds.has(role.id));
        if (rolesToRemove.size > 0) {
            await member.roles.remove(rolesToRemove);
            try {
                const coinSymbol = await configurableConstants_1.default.getCoinSymbol(member.guild.id);
                const taxDescription = taxConfig.taxType === 'percentage'
                    ? `**${taxConfig.percentageValue}%** of your balance (**${taxAmount} ${coinSymbol}**)`
                    : `**${taxAmount} ${coinSymbol}**`;
                const embed = await (0, embedBuilder_1.createServerErrorEmbed)(member.guild.id, 'Tax Payment Failed - Roles Removed')
                    .then(embed => embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Insufficient Funds for Tax Payment**\n\n` +
                    `You were unable to pay the required tax of ${taxDescription} ` +
                    `(you only had **${currentBalance} ${coinSymbol}**).\n\n` +
                    `As a result, all your purchasable roles have been removed:\n` +
                    `${rolesToRemove.map(role => `• ${role.name}`).join('\n')}\n\n` +
                    `${embedBuilder_1.EMOJIS.ECONOMY.COINS} You can earn more ${coinSymbol} and repurchase these roles when you have sufficient funds.`));
                await member.send({ embeds: [embed] });
            }
            catch (dmError) {
                console.error(`[Tax Collection] Failed to send DM to ${member.displayName}:`, dmError);
            }
        }
        if (currentBalance < 0) {
            await (0, economyService_1.adjustBalance)(member.id, member.guild.id, -currentBalance, 'tax', 'Balance reset to 0 after tax enforcement', member.client);
        }
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError(`Failed to handle insufficient funds for tax: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function getNextTaxDate(guildId) {
    try {
        const taxConfig = await TaxConfiguration_1.TaxConfiguration.findOne({ guildId, enabled: true });
        return taxConfig?.nextTaxDate || null;
    }
    catch (error) {
        console.error(`[Tax Service] Failed to get next tax date for guild ${guildId}:`, error);
        return null;
    }
}
async function isTaxEnabled(guildId) {
    try {
        const taxConfig = await TaxConfiguration_1.TaxConfiguration.findOne({ guildId, enabled: true });
        return !!taxConfig;
    }
    catch (error) {
        console.error(`[Tax Service] Failed to check tax status for guild ${guildId}:`, error);
        return false;
    }
}
