# 🎯 Dynamic Pricing System Guide

## 📋 Overview

The Dynamic Pricing System allows Discord server administrators to set role prices that automatically scale with their server's economy. Roles can now be priced as either:

- **Fixed Price**: Traditional static pricing (e.g., 1000 coins)
- **Percentage Price**: Dynamic pricing based on total server economy (e.g., 5% of all coins)

## 🚀 Quick Start

### Fixed Pricing (Traditional)
```bash
/setroleforsale @VIP 1000 "Premium VIP role with exclusive perks"
```

### Percentage Pricing (New!)
```bash
/setroleforsale @Elite 5% "Elite role - costs 5% of server's total economy"
/setroleforsale @Whale 10% "Whale role for the biggest contributors"
/setroleforsale @Exclusive 0.5% "Affordable exclusive role"
```

## 💡 How Dynamic Pricing Works

### Calculation Method
1. **Total Economy Calculation**: Sum of all user balances in the guild
2. **Percentage Application**: `actualPrice = (percentage / 100) * totalEconomy`
3. **Minimum Price Protection**: Ensures roles never cost less than 10 coins
4. **Real-time Updates**: Prices recalculate when displayed in `/shop`

### Example Scenarios

**Server with 100,000 total coins:**
- `5%` role = 5,000 coins
- `1%` role = 1,000 coins  
- `0.5%` role = 500 coins

**Server with 10,000 total coins:**
- `5%` role = 500 coins
- `1%` role = 100 coins
- `0.5%` role = 50 coins

**Server with 500 total coins:**
- `5%` role = 25 coins → **Protected to 10 coins minimum**
- `1%` role = 5 coins → **Protected to 10 coins minimum**

## 🛠️ Command Reference

### `/setroleforsale` - Enhanced Syntax

**Fixed Pricing:**
```bash
/setroleforsale role:@RoleName price:"1000" description:"Role description"
```

**Percentage Pricing:**
```bash
/setroleforsale role:@RoleName price:"5%" description:"Role description"
```

**Validation Rules:**
- Fixed prices: Non-negative integers (0, 100, 1000, etc.)
- Percentage prices: 0.1% to 50% (prevents extreme values)
- Percentage format: Must end with `%` symbol

### `/shop` - Enhanced Display

The shop now shows dynamic pricing information:

**Fixed Price Roles:**
```
💰 VIP Role - 1,000 PLC
```

**Percentage Price Roles:**
```
💰 Elite Role - 2,500 PLC (5% of economy)
```

## 🔧 Technical Implementation

### Database Schema
```javascript
{
  // Existing fields
  guildId: String,
  roleId: String, 
  name: String,
  price: Number,        // For fixed pricing or calculated amount
  description: String,
  roleType: String,     // 'balance_threshold' | 'shop_purchase'
  
  // New dynamic pricing fields
  priceType: String,    // 'fixed' | 'percentage'
  percentageValue: Number // 0.1 to 50 (for percentage pricing)
}
```

### Caching System
- **Cache Duration**: 5 minutes per guild
- **Cache Key**: Guild ID
- **Cache Content**: Total economy value and timestamp
- **Performance**: Batch calculations for multiple roles
- **Fallback**: Uses cached value if fresh calculation fails

### Edge Cases Handled

1. **Low Economy Protection**
   - Minimum economy value: 1,000 coins
   - Prevents division by zero or extremely low prices

2. **Minimum Price Protection**
   - Percentage roles never cost less than 10 coins
   - Protects against micro-transactions in small servers

3. **Cache Failures**
   - Falls back to cached values if available
   - Uses minimum economy value as last resort
   - Logs errors for monitoring

4. **Invalid Percentage Values**
   - Validates 0.1% to 50% range
   - Prevents negative or extreme percentages
   - Clear error messages for invalid input

## 📊 Migration Instructions

### For Existing Deployments

1. **Deploy Code Changes**
   ```bash
   npm run build
   npm run deploy-commands  # Updates /setroleforsale to accept string input
   ```

2. **Run Database Migration**
   ```bash
   node scripts/migrate-dynamic-pricing-direct.js
   ```

3. **Validate Migration**
   ```bash
   node scripts/migrate-dynamic-pricing-direct.js --validate
   ```

4. **Restart Bot**
   - Restart the bot process to load new code
   - Existing fixed-price roles continue working normally
   - New percentage pricing becomes available

### Migration Results
- ✅ All existing roles converted to `priceType: 'fixed'`
- ✅ Backward compatibility maintained
- ✅ No price changes for existing roles
- ✅ New percentage pricing available immediately

## 🧪 Testing Guide

### Test Fixed Pricing
```bash
# Create fixed price role
/setroleforsale @TestFixed 500 "Fixed price test role"

# Verify in shop
/shop
# Should show: "TestFixed - 500 PLC"

# Test purchase
# User with 500+ coins should be able to buy
```

### Test Percentage Pricing  
```bash
# Create percentage price role
/setroleforsale @TestPercent 2% "Percentage price test role"

# Verify in shop
/shop  
# Should show: "TestPercent - [calculated amount] PLC (2% of economy)"

# Test purchase
# Price should match calculated percentage of total economy
```

### Test Edge Cases
```bash
# Test minimum percentage
/setroleforsale @MinTest 0.1% "Minimum percentage test"

# Test maximum percentage  
/setroleforsale @MaxTest 50% "Maximum percentage test"

# Test invalid percentages (should fail)
/setroleforsale @Invalid 0.05% "Too low - should fail"
/setroleforsale @Invalid 51% "Too high - should fail"
```

## 🎯 Use Cases

### Small Servers (< 10,000 total coins)
- Use lower percentages: 0.5%, 1%, 2%
- Keeps roles affordable as economy grows
- Encourages participation and growth

### Medium Servers (10,000 - 100,000 total coins)  
- Use moderate percentages: 2%, 5%, 10%
- Balances accessibility with exclusivity
- Scales naturally with server activity

### Large Servers (> 100,000 total coins)
- Use higher percentages: 5%, 10%, 20%
- Maintains role exclusivity
- Rewards major contributors appropriately

### Mixed Strategy
- **Entry roles**: Fixed pricing (100, 500 coins)
- **Mid-tier roles**: Low percentage (1%, 2%)  
- **Premium roles**: High percentage (10%, 20%)
- **Ultra-exclusive**: Very high percentage (30%, 50%)

## 🔍 Monitoring & Maintenance

### Cache Performance
- Monitor cache hit rates in logs
- Clear cache if economy calculations seem stale
- Adjust cache TTL if needed (currently 5 minutes)

### Price Monitoring
- Check percentage role prices periodically
- Ensure minimum price protection is working
- Monitor for extreme price fluctuations

### Database Health
- Validate all roles have `priceType` field
- Check for orphaned percentage roles without `percentageValue`
- Monitor for invalid percentage values

## 🚨 Troubleshooting

### Common Issues

**"Invalid price format" Error**
- Ensure percentage includes `%` symbol
- Check percentage is between 0.1% and 50%
- Verify fixed prices are non-negative integers

**Percentage Prices Not Updating**
- Cache may be stale (wait 5 minutes)
- Check if economy calculation is failing
- Verify LeaderboardManager is working

**Roles Showing Wrong Prices**
- Run migration validation script
- Check database for missing `priceType` fields
- Verify bot has latest compiled code

### Support Commands
```bash
# Validate database state
node scripts/migrate-dynamic-pricing-direct.js --validate

# Check cache statistics (in bot logs)
# Look for "DynamicPricingService" log entries
```

The Dynamic Pricing System provides powerful flexibility for server economies while maintaining backward compatibility and robust error handling.
