"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyApplication = void 0;
const DatabaseInitializer_1 = require("./database/DatabaseInitializer");
const ClientManager_1 = require("./client/ClientManager");
const CronManager_1 = require("./cron/CronManager");
const InteractionHandler_1 = require("./events/InteractionHandler");
const MessageHandler_1 = require("./events/MessageHandler");
const ReactionHandler_1 = require("./events/ReactionHandler");
const MemberHandler_1 = require("./events/MemberHandler");
class LegacyApplication {
    constructor() {
        this.isInitialized = false;
        this.clientManager = new ClientManager_1.LegacyClientManager();
        this.client = this.clientManager.getClient();
        this.cronManager = new CronManager_1.LegacyCronManager(this.client);
        this.interactionHandler = new InteractionHandler_1.LegacyInteractionHandler(this.client);
        this.messageHandler = new MessageHandler_1.LegacyMessageHandler(this.client);
        this.reactionHandler = new ReactionHandler_1.LegacyReactionHandler(this.client);
        this.memberHandler = new MemberHandler_1.LegacyMemberHandler(this.client);
    }
    async initialize() {
        try {
            console.log('[Legacy App] Initializing Economy Bot (Legacy Mode)...');
            await DatabaseInitializer_1.LegacyDatabaseInitializer.initialize();
            this.clientManager.loadCommands();
            this.setupEventHandlers();
            await this.clientManager.login();
            this.isInitialized = true;
            console.log('[Legacy App] Economy Bot initialized successfully in legacy mode');
        }
        catch (error) {
            console.error('[Legacy App] Failed to initialize:', error);
            throw error;
        }
    }
    setupEventHandlers() {
        this.client.once('ready', () => {
            console.log(`[Legacy App] Logged in as ${this.client.user?.tag}`);
            this.cronManager.initializeJobs();
        });
        this.client.on('interactionCreate', async (interaction) => {
            await this.interactionHandler.handleInteraction(interaction);
        });
        this.client.on('messageCreate', async (message) => {
            await this.messageHandler.handleMessageCreate(message);
        });
        this.client.on('messageReactionAdd', async (reaction, user) => {
            await this.reactionHandler.handleReactionAdd(reaction, user);
        });
        this.client.on('guildMemberAdd', async (member) => {
            await this.memberHandler.handleMemberAdd(member);
        });
        this.client.on('guildMemberRemove', async (member) => {
            await this.memberHandler.handleMemberRemove(member);
        });
        this.client.on('guildMemberUpdate', async (oldMember, newMember) => {
            await this.memberHandler.handleMemberUpdate(oldMember, newMember);
        });
        this.client.on('voiceStateUpdate', async (oldState, newState) => {
            await this.memberHandler.handleVoiceStateUpdate(oldState, newState);
        });
        console.log('[Legacy App] Event handlers registered');
    }
    async shutdown() {
        try {
            console.log('[Legacy App] Shutting down...');
            this.cronManager.destroyAllJobs();
            this.clientManager.destroy();
            await DatabaseInitializer_1.LegacyDatabaseInitializer.close();
            this.isInitialized = false;
            console.log('[Legacy App] Shutdown complete');
        }
        catch (error) {
            console.error('[Legacy App] Error during shutdown:', error);
        }
    }
    getStatus() {
        return {
            initialized: this.isInitialized,
            clientReady: this.clientManager.isReady(),
            uptime: this.clientManager.getUptime(),
            guildCount: this.clientManager.getGuildCount(),
            userCount: this.clientManager.getUserCount(),
            databaseStatus: DatabaseInitializer_1.LegacyDatabaseInitializer.getConnectionStatus(),
            cronJobs: this.cronManager.getAllJobStatuses(),
        };
    }
    getClient() {
        return this.client;
    }
    getManagers() {
        return {
            client: this.clientManager,
            cron: this.cronManager,
            interaction: this.interactionHandler,
            message: this.messageHandler,
            reaction: this.reactionHandler,
            member: this.memberHandler,
        };
    }
    isReady() {
        return this.isInitialized && this.clientManager.isReady();
    }
}
exports.LegacyApplication = LegacyApplication;
exports.default = LegacyApplication;
