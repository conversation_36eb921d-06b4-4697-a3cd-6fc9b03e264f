"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCoinDecayConfiguration = getCoinDecayConfiguration;
exports.updateCoinDecayConfiguration = updateCoinDecayConfiguration;
exports.updateUserActivity = updateUserActivity;
exports.getInactiveUsers = getInactiveUsers;
exports.processUserCoinDecay = processUserCoinDecay;
exports.processAllCoinDecay = processAllCoinDecay;
exports.getCoinDecayStats = getCoinDecayStats;
const CoinDecayConfiguration_1 = __importDefault(require("../models/CoinDecayConfiguration"));
const CoinDecayLog_1 = __importDefault(require("../models/CoinDecayLog"));
const UserActivityTracker_1 = __importDefault(require("../models/UserActivityTracker"));
const User_1 = __importDefault(require("../models/User"));
const economyService_1 = require("./economyService");
const errorHandler_1 = require("../utils/errorHandler");
async function getCoinDecayConfiguration(guildId) {
    try {
        return await CoinDecayConfiguration_1.default.findOne({ guildId });
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError(`Failed to get coin decay configuration: ${error}`);
    }
}
async function updateCoinDecayConfiguration(guildId, enabled, decayPercentage, inactivityThresholdDays) {
    try {
        if (decayPercentage < 1 || decayPercentage > 50 || !Number.isInteger(decayPercentage)) {
            throw new Error('Decay percentage must be an integer between 1 and 50 inclusive');
        }
        if (inactivityThresholdDays < 7 || inactivityThresholdDays > 365 || !Number.isInteger(inactivityThresholdDays)) {
            throw new Error('Inactivity threshold must be an integer between 7 and 365 days inclusive');
        }
        const config = await CoinDecayConfiguration_1.default.findOneAndUpdate({ guildId }, {
            guildId,
            enabled,
            decayPercentage,
            inactivityThresholdDays
        }, {
            new: true,
            upsert: true,
            runValidators: true
        });
        if (!config) {
            throw new errorHandler_1.DatabaseError('Failed to update coin decay configuration');
        }
        return config;
    }
    catch (error) {
        if (error instanceof Error) {
            throw error;
        }
        throw new errorHandler_1.DatabaseError(`Failed to update coin decay configuration: ${error}`);
    }
}
async function updateUserActivity(discordId, guildId) {
    try {
        await UserActivityTracker_1.default.findOneAndUpdate({ discordId, guildId }, {
            discordId,
            guildId,
            lastMessageTimestamp: new Date()
        }, {
            upsert: true,
            runValidators: true
        });
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError(`Failed to update user activity: ${error}`);
    }
}
async function getInactiveUsers(guildId, inactivityThresholdDays) {
    try {
        const thresholdDate = new Date();
        thresholdDate.setDate(thresholdDate.getDate() - inactivityThresholdDays);
        const inactiveUsers = await UserActivityTracker_1.default.find({
            guildId,
            lastMessageTimestamp: { $lt: thresholdDate }
        }).lean();
        const now = new Date();
        return inactiveUsers.map(user => ({
            discordId: user.discordId,
            lastActivity: user.lastMessageTimestamp,
            inactiveDays: Math.floor((now.getTime() - user.lastMessageTimestamp.getTime()) / (1000 * 60 * 60 * 24))
        }));
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError(`Failed to get inactive users: ${error}`);
    }
}
async function processUserCoinDecay(client, discordId, guildId, decayPercentage, lastActivity, inactiveDays) {
    try {
        const user = await User_1.default.findOne({ discordId, guildId });
        if (!user || user.balance <= 0) {
            return { decayed: false, decayAmount: 0, newBalance: 0 };
        }
        const originalBalance = user.balance;
        const decayAmount = Math.floor(originalBalance * (decayPercentage / 100));
        if (decayAmount <= 0) {
            return { decayed: false, decayAmount: 0, newBalance: originalBalance };
        }
        await (0, economyService_1.adjustBalance)(discordId, guildId, -decayAmount, 'coin_decay', `Coin decay: ${decayPercentage}% for ${inactiveDays} days of inactivity`, client);
        const newBalance = originalBalance - decayAmount;
        await CoinDecayLog_1.default.create({
            guildId,
            discordId,
            decayAmount,
            coinsBeforeDecay: originalBalance,
            coinsAfterDecay: newBalance,
            decayPercentage,
            inactivityDays: inactiveDays,
            lastActivityDate: lastActivity,
            decayTimestamp: new Date()
        });
        return { decayed: true, decayAmount, newBalance };
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError(`Failed to process coin decay for user ${discordId}: ${error}`);
    }
}
async function processAllCoinDecay(client) {
    const results = {
        processedGuilds: 0,
        totalUsersProcessed: 0,
        totalDecayAmount: 0,
        errors: []
    };
    try {
        const enabledConfigs = await CoinDecayConfiguration_1.default.find({ enabled: true });
        for (const config of enabledConfigs) {
            try {
                const inactiveUsers = await getInactiveUsers(config.guildId, config.inactivityThresholdDays);
                for (const inactiveUser of inactiveUsers) {
                    try {
                        const result = await processUserCoinDecay(client, inactiveUser.discordId, config.guildId, config.decayPercentage, inactiveUser.lastActivity, inactiveUser.inactiveDays);
                        if (result.decayed) {
                            results.totalUsersProcessed++;
                            results.totalDecayAmount += result.decayAmount;
                        }
                    }
                    catch (error) {
                        results.errors.push(`User ${inactiveUser.discordId} in guild ${config.guildId}: ${error}`);
                    }
                }
                results.processedGuilds++;
            }
            catch (error) {
                results.errors.push(`Guild ${config.guildId}: ${error}`);
            }
        }
    }
    catch (error) {
        results.errors.push(`Failed to get enabled configurations: ${error}`);
    }
    return results;
}
async function getCoinDecayStats(guildId, days = 30) {
    try {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        const logs = await CoinDecayLog_1.default.find({
            guildId,
            decayTimestamp: { $gte: cutoffDate }
        });
        const totalDecayEvents = logs.length;
        const totalDecayAmount = logs.reduce((sum, log) => sum + log.decayAmount, 0);
        const affectedUsers = new Set(logs.map(log => log.discordId)).size;
        const averageDecayPerUser = affectedUsers > 0 ? totalDecayAmount / affectedUsers : 0;
        return {
            totalDecayEvents,
            totalDecayAmount,
            affectedUsers,
            averageDecayPerUser: Math.round(averageDecayPerUser * 100) / 100
        };
    }
    catch (error) {
        throw new errorHandler_1.DatabaseError(`Failed to get coin decay stats: ${error}`);
    }
}
