/**
 * Migration 002: Convert to Multi-Server Architecture
 * Converts existing single-server data to guild-isolated multi-server architecture
 */

import mongoose from 'mongoose';
import { ILogger } from '../src/core/interfaces';

export interface MigrationContext {
  db: mongoose.Connection;
  logger: ILogger;
  dryRun?: boolean;
}

export interface MigrationResult {
  success: boolean;
  message: string;
  changes: string[];
  errors: string[];
}

export interface MigrationStats {
  usersProcessed: number;
  transactionsProcessed: number;
  rolesForSaleProcessed: number;
  backupCollectionsCreated: number;
  indexesCreated: number;
  indexesDropped: number;
}

export class ConvertToMultiServerMigration {
  private context: MigrationContext;
  private stats: MigrationStats = {
    usersProcessed: 0,
    transactionsProcessed: 0,
    rolesForSaleProcessed: 0,
    backupCollectionsCreated: 0,
    indexesCreated: 0,
    indexesDropped: 0
  };

  constructor(context: MigrationContext) {
    this.context = context;
  }

  /**
   * Execute the migration
   */
  async up(): Promise<MigrationResult> {
    const { db, logger, dryRun = false } = this.context;
    const changes: string[] = [];
    const errors: string[] = [];

    try {
      logger.info('[Migration 002] Starting multi-server conversion...');

      // Step 1: Create backup collections
      await this.createBackupCollections(changes, dryRun);

      // Step 2: Get default guild ID for existing data
      const defaultGuildId = await this.getDefaultGuildId(changes, dryRun);

      // Step 3: Convert User collection
      await this.convertUserCollection(defaultGuildId, changes, dryRun);

      // Step 4: Convert Transaction collection
      await this.convertTransactionCollection(defaultGuildId, changes, dryRun);

      // Step 5: Convert RoleForSale collection
      await this.convertRoleForSaleCollection(defaultGuildId, changes, dryRun);

      // Step 6: Update database indexes
      await this.updateDatabaseIndexes(changes, dryRun);

      // Step 7: Verify data integrity
      await this.verifyDataIntegrity(changes, dryRun);

      logger.info(`[Migration 002] Multi-server conversion completed.`, this.stats);

      return {
        success: true,
        message: 'Multi-server conversion completed successfully',
        changes,
        errors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);
      logger.error('[Migration 002] Failed to convert to multi-server', { error, stats: this.stats });

      return {
        success: false,
        message: `Migration failed: ${errorMessage}`,
        changes,
        errors
      };
    }
  }

  /**
   * Rollback the migration
   */
  async down(): Promise<MigrationResult> {
    const { db, logger, dryRun = false } = this.context;
    const changes: string[] = [];
    const errors: string[] = [];

    try {
      logger.info('[Migration 002] Starting multi-server rollback...');

      if (!dryRun) {
        // Step 1: Restore from backup collections
        await this.restoreFromBackups(changes);

        // Step 2: Drop new guild-scoped indexes
        await this.dropGuildScopedIndexes(changes);

        // Step 3: Recreate original indexes
        await this.recreateOriginalIndexes(changes);

        // Step 4: Clean up backup collections
        await this.cleanupBackupCollections(changes);
      } else {
        changes.push('DRY RUN: Would restore from backup collections');
        changes.push('DRY RUN: Would drop guild-scoped indexes');
        changes.push('DRY RUN: Would recreate original indexes');
        changes.push('DRY RUN: Would clean up backup collections');
      }

      logger.info(`[Migration 002] Multi-server rollback completed. Changes: ${changes.length}`);

      return {
        success: true,
        message: 'Multi-server rollback completed successfully',
        changes,
        errors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);
      logger.error('[Migration 002] Failed to rollback multi-server conversion', { error });

      return {
        success: false,
        message: `Rollback failed: ${errorMessage}`,
        changes,
        errors
      };
    }
  }

  /**
   * Create backup collections before migration
   */
  private async createBackupCollections(changes: string[], dryRun: boolean): Promise<void> {
    const { db, logger } = this.context;
    const collectionsToBackup = ['users', 'transactions', 'roleforsales'];

    if (!dryRun) {
      for (const collectionName of collectionsToBackup) {
        try {
          const collection = db.collection(collectionName);
          const backupName = `${collectionName}_backup_pre_multiserver`;
          
          // Check if collection exists
          const collections = await db.listCollections({ name: collectionName }).toArray();
          if (collections.length > 0) {
            // Copy all documents to backup collection
            const documents = await collection.find({}).toArray();
            if (documents.length > 0) {
              await db.collection(backupName).insertMany(documents);
              this.stats.backupCollectionsCreated++;
              changes.push(`Created backup: ${backupName} (${documents.length} documents)`);
              logger.info(`[Migration 002] Backed up ${documents.length} documents from ${collectionName}`);
            } else {
              changes.push(`Collection ${collectionName} is empty, no backup needed`);
            }
          } else {
            changes.push(`Collection ${collectionName} does not exist, skipping backup`);
          }
        } catch (error) {
          const errorMsg = `Failed to backup ${collectionName}: ${error}`;
          changes.push(errorMsg);
          logger.warn(`[Migration 002] ${errorMsg}`);
        }
      }
    } else {
      changes.push(`DRY RUN: Would create backup collections for: ${collectionsToBackup.join(', ')}`);
    }
  }

  /**
   * Get default guild ID for existing data
   * This can be configured via environment variable or use a default
   */
  private async getDefaultGuildId(changes: string[], dryRun: boolean): Promise<string> {
    const defaultGuildId = process.env.MIGRATION_DEFAULT_GUILD_ID || '000000000000000000';
    
    if (defaultGuildId === '000000000000000000') {
      changes.push('WARNING: Using placeholder guild ID. Set MIGRATION_DEFAULT_GUILD_ID environment variable for production');
      this.context.logger.warn('[Migration 002] Using placeholder guild ID. Set MIGRATION_DEFAULT_GUILD_ID for production migration');
    } else {
      changes.push(`Using default guild ID: ${defaultGuildId}`);
    }

    return defaultGuildId;
  }

  /**
   * Convert User collection to include guildId
   */
  private async convertUserCollection(defaultGuildId: string, changes: string[], dryRun: boolean): Promise<void> {
    const { db, logger } = this.context;

    if (!dryRun) {
      const usersCollection = db.collection('users');
      
      // Get all users without guildId
      const usersToUpdate = await usersCollection.find({ guildId: { $exists: false } }).toArray();
      
      if (usersToUpdate.length > 0) {
        logger.info(`[Migration 002] Converting ${usersToUpdate.length} users to multi-server format`);
        
        // Update users in batches to avoid memory issues
        const batchSize = 100;
        for (let i = 0; i < usersToUpdate.length; i += batchSize) {
          const batch = usersToUpdate.slice(i, i + batchSize);
          const bulkOps = batch.map(user => ({
            updateOne: {
              filter: { _id: user._id },
              update: { $set: { guildId: defaultGuildId } }
            }
          }));
          
          const result = await usersCollection.bulkWrite(bulkOps);
          this.stats.usersProcessed += result.modifiedCount;
        }
        
        changes.push(`Converted ${this.stats.usersProcessed} users to include guildId: ${defaultGuildId}`);
        logger.info(`[Migration 002] Converted ${this.stats.usersProcessed} users`);
      } else {
        changes.push('No users found without guildId, skipping user conversion');
      }
    } else {
      const usersCollection = db.collection('users');
      const count = await usersCollection.countDocuments({ guildId: { $exists: false } });
      changes.push(`DRY RUN: Would convert ${count} users to include guildId: ${defaultGuildId}`);
    }
  }

  /**
   * Convert Transaction collection to include guildId
   */
  private async convertTransactionCollection(defaultGuildId: string, changes: string[], dryRun: boolean): Promise<void> {
    const { db, logger } = this.context;

    if (!dryRun) {
      const transactionsCollection = db.collection('transactions');

      // Get all transactions without guildId
      const transactionsToUpdate = await transactionsCollection.find({ guildId: { $exists: false } }).toArray();

      if (transactionsToUpdate.length > 0) {
        logger.info(`[Migration 002] Converting ${transactionsToUpdate.length} transactions to multi-server format`);

        // Update transactions in batches
        const batchSize = 500;
        for (let i = 0; i < transactionsToUpdate.length; i += batchSize) {
          const batch = transactionsToUpdate.slice(i, i + batchSize);
          const bulkOps = batch.map(transaction => ({
            updateOne: {
              filter: { _id: transaction._id },
              update: { $set: { guildId: defaultGuildId } }
            }
          }));

          const result = await transactionsCollection.bulkWrite(bulkOps);
          this.stats.transactionsProcessed += result.modifiedCount;
        }

        changes.push(`Converted ${this.stats.transactionsProcessed} transactions to include guildId: ${defaultGuildId}`);
        logger.info(`[Migration 002] Converted ${this.stats.transactionsProcessed} transactions`);
      } else {
        changes.push('No transactions found without guildId, skipping transaction conversion');
      }
    } else {
      const transactionsCollection = db.collection('transactions');
      const count = await transactionsCollection.countDocuments({ guildId: { $exists: false } });
      changes.push(`DRY RUN: Would convert ${count} transactions to include guildId: ${defaultGuildId}`);
    }
  }

  /**
   * Convert RoleForSale collection to include guildId
   */
  private async convertRoleForSaleCollection(defaultGuildId: string, changes: string[], dryRun: boolean): Promise<void> {
    const { db, logger } = this.context;

    if (!dryRun) {
      const rolesCollection = db.collection('roleforsales');

      // Get all roles without guildId
      const rolesToUpdate = await rolesCollection.find({ guildId: { $exists: false } }).toArray();

      if (rolesToUpdate.length > 0) {
        logger.info(`[Migration 002] Converting ${rolesToUpdate.length} roles for sale to multi-server format`);

        // Update roles in batches
        const batchSize = 100;
        for (let i = 0; i < rolesToUpdate.length; i += batchSize) {
          const batch = rolesToUpdate.slice(i, i + batchSize);
          const bulkOps = batch.map(role => ({
            updateOne: {
              filter: { _id: role._id },
              update: { $set: { guildId: defaultGuildId } }
            }
          }));

          const result = await rolesCollection.bulkWrite(bulkOps);
          this.stats.rolesForSaleProcessed += result.modifiedCount;
        }

        changes.push(`Converted ${this.stats.rolesForSaleProcessed} roles for sale to include guildId: ${defaultGuildId}`);
        logger.info(`[Migration 002] Converted ${this.stats.rolesForSaleProcessed} roles for sale`);
      } else {
        changes.push('No roles for sale found without guildId, skipping role conversion');
      }
    } else {
      const rolesCollection = db.collection('roleforsales');
      const count = await rolesCollection.countDocuments({ guildId: { $exists: false } });
      changes.push(`DRY RUN: Would convert ${count} roles for sale to include guildId: ${defaultGuildId}`);
    }
  }

  /**
   * Update database indexes for guild-scoped queries
   */
  private async updateDatabaseIndexes(changes: string[], dryRun: boolean): Promise<void> {
    const { db, logger } = this.context;

    if (!dryRun) {
      // Drop old indexes that are no longer needed
      await this.dropOldIndexes(changes);

      // Create new guild-scoped indexes
      await this.createGuildScopedIndexes(changes);
    } else {
      changes.push('DRY RUN: Would drop old single-server indexes');
      changes.push('DRY RUN: Would create new guild-scoped indexes');
    }
  }

  /**
   * Drop old single-server indexes
   */
  private async dropOldIndexes(changes: string[]): Promise<void> {
    const { db, logger } = this.context;

    const indexesToDrop = [
      { collection: 'users', index: 'discordId_1' },
      { collection: 'transactions', index: 'discordId_1_timestamp_-1' },
      { collection: 'roleforsales', index: 'roleId_1' }
    ];

    for (const { collection, index } of indexesToDrop) {
      try {
        await db.collection(collection).dropIndex(index);
        this.stats.indexesDropped++;
        changes.push(`Dropped old index: ${collection}.${index}`);
        logger.info(`[Migration 002] Dropped index ${index} from ${collection}`);
      } catch (error) {
        if (error instanceof Error && error.message.includes('index not found')) {
          changes.push(`Index ${collection}.${index} did not exist`);
        } else {
          changes.push(`Failed to drop index ${collection}.${index}: ${error}`);
          logger.warn(`[Migration 002] Failed to drop index ${collection}.${index}`, { error });
        }
      }
    }
  }

  /**
   * Create new guild-scoped indexes
   */
  private async createGuildScopedIndexes(changes: string[]): Promise<void> {
    const { db, logger } = this.context;

    const indexesToCreate = [
      // User indexes
      { collection: 'users', key: { discordId: 1, guildId: 1 }, options: { unique: true } },
      { collection: 'users', key: { guildId: 1 }, options: {} },
      { collection: 'users', key: { guildId: 1, balance: -1 }, options: {} },

      // Transaction indexes
      { collection: 'transactions', key: { guildId: 1, discordId: 1, timestamp: -1 }, options: {} },
      { collection: 'transactions', key: { guildId: 1, timestamp: -1 }, options: {} },
      { collection: 'transactions', key: { guildId: 1, type: 1, timestamp: -1 }, options: {} },

      // RoleForSale indexes
      { collection: 'roleforsales', key: { guildId: 1, roleId: 1 }, options: { unique: true } },
      { collection: 'roleforsales', key: { guildId: 1 }, options: {} }
    ];

    for (const { collection, key, options } of indexesToCreate) {
      try {
        await db.collection(collection).createIndex(key, options);
        this.stats.indexesCreated++;
        changes.push(`Created guild-scoped index: ${collection}.${JSON.stringify(key)}`);
        logger.info(`[Migration 002] Created index ${JSON.stringify(key)} on ${collection}`);
      } catch (error) {
        changes.push(`Failed to create index on ${collection}: ${error}`);
        logger.error(`[Migration 002] Failed to create index on ${collection}`, { error, key });
        throw error;
      }
    }
  }

  /**
   * Verify data integrity after migration
   */
  private async verifyDataIntegrity(changes: string[], dryRun: boolean): Promise<void> {
    const { db, logger } = this.context;

    if (!dryRun) {
      // Check that all users have guildId
      const usersWithoutGuildId = await db.collection('users').countDocuments({ guildId: { $exists: false } });
      if (usersWithoutGuildId > 0) {
        throw new Error(`Data integrity check failed: ${usersWithoutGuildId} users still missing guildId`);
      }

      // Check that all transactions have guildId
      const transactionsWithoutGuildId = await db.collection('transactions').countDocuments({ guildId: { $exists: false } });
      if (transactionsWithoutGuildId > 0) {
        throw new Error(`Data integrity check failed: ${transactionsWithoutGuildId} transactions still missing guildId`);
      }

      // Check that all roles for sale have guildId
      const rolesWithoutGuildId = await db.collection('roleforsales').countDocuments({ guildId: { $exists: false } });
      if (rolesWithoutGuildId > 0) {
        throw new Error(`Data integrity check failed: ${rolesWithoutGuildId} roles for sale still missing guildId`);
      }

      changes.push('✅ Data integrity verification passed');
      logger.info('[Migration 002] Data integrity verification completed successfully');
    } else {
      changes.push('DRY RUN: Would verify data integrity');
    }
  }

  /**
   * Restore collections from backup
   */
  private async restoreFromBackups(changes: string[]): Promise<void> {
    const { db, logger } = this.context;
    const collectionsToRestore = ['users', 'transactions', 'roleforsales'];

    for (const collectionName of collectionsToRestore) {
      const backupName = `${collectionName}_backup_pre_multiserver`;

      try {
        // Check if backup exists
        const backupCollections = await db.listCollections({ name: backupName }).toArray();
        if (backupCollections.length > 0) {
          // Drop current collection
          await db.dropCollection(collectionName);

          // Restore from backup
          const backupDocuments = await db.collection(backupName).find({}).toArray();
          if (backupDocuments.length > 0) {
            await db.collection(collectionName).insertMany(backupDocuments);
            changes.push(`Restored ${collectionName} from backup (${backupDocuments.length} documents)`);
            logger.info(`[Migration 002 Rollback] Restored ${backupDocuments.length} documents to ${collectionName}`);
          }
        } else {
          changes.push(`No backup found for ${collectionName}, skipping restore`);
        }
      } catch (error) {
        const errorMsg = `Failed to restore ${collectionName}: ${error}`;
        changes.push(errorMsg);
        logger.error(`[Migration 002 Rollback] ${errorMsg}`);
        throw error;
      }
    }
  }

  /**
   * Drop guild-scoped indexes during rollback
   */
  private async dropGuildScopedIndexes(changes: string[]): Promise<void> {
    const { db, logger } = this.context;

    const indexesToDrop = [
      { collection: 'users', index: 'discordId_1_guildId_1' },
      { collection: 'users', index: 'guildId_1' },
      { collection: 'users', index: 'guildId_1_balance_-1' },
      { collection: 'transactions', index: 'guildId_1_discordId_1_timestamp_-1' },
      { collection: 'transactions', index: 'guildId_1_timestamp_-1' },
      { collection: 'transactions', index: 'guildId_1_type_1_timestamp_-1' },
      { collection: 'roleforsales', index: 'guildId_1_roleId_1' },
      { collection: 'roleforsales', index: 'guildId_1' }
    ];

    for (const { collection, index } of indexesToDrop) {
      try {
        await db.collection(collection).dropIndex(index);
        changes.push(`Dropped guild-scoped index: ${collection}.${index}`);
        logger.info(`[Migration 002 Rollback] Dropped index ${index} from ${collection}`);
      } catch (error) {
        if (error instanceof Error && error.message.includes('index not found')) {
          changes.push(`Index ${collection}.${index} did not exist`);
        } else {
          changes.push(`Failed to drop index ${collection}.${index}: ${error}`);
          logger.warn(`[Migration 002 Rollback] Failed to drop index ${collection}.${index}`, { error });
        }
      }
    }
  }

  /**
   * Recreate original single-server indexes
   */
  private async recreateOriginalIndexes(changes: string[]): Promise<void> {
    const { db, logger } = this.context;

    const indexesToCreate = [
      { collection: 'users', key: { discordId: 1 }, options: { unique: true } },
      { collection: 'transactions', key: { discordId: 1, timestamp: -1 }, options: {} },
      { collection: 'roleforsales', key: { roleId: 1 }, options: { unique: true } }
    ];

    for (const { collection, key, options } of indexesToCreate) {
      try {
        await db.collection(collection).createIndex(key, options);
        changes.push(`Recreated original index: ${collection}.${JSON.stringify(key)}`);
        logger.info(`[Migration 002 Rollback] Recreated index ${JSON.stringify(key)} on ${collection}`);
      } catch (error) {
        changes.push(`Failed to recreate index on ${collection}: ${error}`);
        logger.error(`[Migration 002 Rollback] Failed to recreate index on ${collection}`, { error, key });
        throw error;
      }
    }
  }

  /**
   * Clean up backup collections
   */
  private async cleanupBackupCollections(changes: string[]): Promise<void> {
    const { db, logger } = this.context;
    const backupCollections = ['users_backup_pre_multiserver', 'transactions_backup_pre_multiserver', 'roleforsales_backup_pre_multiserver'];

    for (const backupName of backupCollections) {
      try {
        await db.dropCollection(backupName);
        changes.push(`Cleaned up backup collection: ${backupName}`);
        logger.info(`[Migration 002 Rollback] Cleaned up backup collection ${backupName}`);
      } catch (error) {
        if (error instanceof Error && error.message.includes('ns not found')) {
          changes.push(`Backup collection ${backupName} did not exist`);
        } else {
          changes.push(`Failed to clean up backup collection ${backupName}: ${error}`);
          logger.warn(`[Migration 002 Rollback] Failed to clean up backup collection ${backupName}`, { error });
        }
      }
    }
  }
}
