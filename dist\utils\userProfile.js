"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.findOrCreateUser = findOrCreateUser;
const User_1 = __importDefault(require("../models/User"));
async function findOrCreateUser(userId) {
    const userProfile = await User_1.default.findOneAndUpdate({ userId }, {}, { new: true, upsert: true, setDefaultsOnInsert: true });
    return userProfile;
}
