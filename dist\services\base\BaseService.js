"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.serviceRegistry = exports.ServiceRegistry = exports.BaseService = void 0;
const logger_1 = require("../../core/logger");
class BaseService {
    constructor(app) {
        this.app = app;
        this.logger = (0, logger_1.createLogger)(`service:${this.constructor.name}`);
    }
    async initialize() {
        this.logger.info(`[${this.name}] Service initializing...`);
        await this.onInitialize();
        this.logger.info(`[${this.name}] Service initialized`);
    }
    async shutdown() {
        this.logger.info(`[${this.name}] Service shutting down...`);
        await this.onShutdown();
        this.logger.info(`[${this.name}] Service shutdown complete`);
    }
    async onInitialize() {
    }
    async onShutdown() {
    }
    isFeatureEnabled(featureName) {
        const { isFeatureActive } = require('../../config');
        return isFeatureActive(featureName);
    }
    getService(serviceName) {
        if (!this.app) {
            throw new Error(`Cannot get service ${serviceName}: No application context available`);
        }
        return this.app.getService(serviceName);
    }
    handleError(error, context) {
        this.logger.error(`[${this.name}] Service error`, {
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
            } : error,
            context,
            serviceName: this.name,
        });
    }
    logOperation(operation, details) {
        this.logger.debug(`[${this.name}] ${operation}`, details);
    }
    validateDependencies(dependencies) {
        if (!this.app) {
            throw new Error(`Cannot validate dependencies: No application context available`);
        }
        for (const dependency of dependencies) {
            try {
                this.app.getService(dependency);
            }
            catch (error) {
                throw new Error(`Required dependency not available: ${dependency}`);
            }
        }
    }
    createChildLogger(context) {
        return (0, logger_1.createLogger)(`${this.name}:${context}`);
    }
}
exports.BaseService = BaseService;
class ServiceRegistry {
    constructor() {
        this.services = new Map();
        this.logger = (0, logger_1.createLogger)('service-registry');
    }
    register(service) {
        if (this.services.has(service.name)) {
            this.logger.warn(`[ServiceRegistry] Service already registered: ${service.name}`);
            return;
        }
        this.services.set(service.name, service);
        this.logger.debug(`[ServiceRegistry] Registered service: ${service.name}`);
    }
    get(name) {
        const service = this.services.get(name);
        if (!service) {
            throw new Error(`Service not found: ${name}`);
        }
        return service;
    }
    has(name) {
        return this.services.has(name);
    }
    getAll() {
        return Array.from(this.services.values());
    }
    unregister(name) {
        const removed = this.services.delete(name);
        if (removed) {
            this.logger.debug(`[ServiceRegistry] Unregistered service: ${name}`);
        }
        return removed;
    }
    clear() {
        this.services.clear();
        this.logger.debug('[ServiceRegistry] Cleared all services');
    }
}
exports.ServiceRegistry = ServiceRegistry;
exports.serviceRegistry = new ServiceRegistry();
