"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WealthElectionService = void 0;
const crypto_1 = require("crypto");
const crypto_2 = require("crypto");
const discord_js_1 = require("discord.js");
const mongoose_1 = __importDefault(require("mongoose"));
const BaseService_1 = require("../base/BaseService");
const features_1 = require("../../config/features");
const errorHandler_1 = require("../../utils/errorHandler");
const WealthElection_1 = __importDefault(require("../../models/WealthElection"));
const models_1 = require("../../models");
class WealthElectionService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'WealthElectionService';
    }
    async onInitialize() {
        if (!this.isFeatureEnabled('ECONOMY_SYSTEM')) {
            throw new Error('Wealth election system requires economy system to be enabled');
        }
        this.logger.info('[WealthElectionService] Wealth-weighted election system initialized');
    }
    async createElection(config) {
        this.logOperation('Creating new election', { title: config.title, seats: config.seats });
        this.validateElectionConfig(config);
        const now = new Date();
        const nominationStart = now;
        const nominationEnd = new Date(now.getTime() + config.nominationHours * 60 * 60 * 1000);
        const votingStart = nominationEnd;
        const votingEnd = new Date(votingStart.getTime() + config.votingHours * 60 * 60 * 1000);
        const electionId = (0, crypto_1.randomUUID)();
        const election = new WealthElection_1.default({
            electionId,
            guildId: config.guildId,
            channelId: config.channelId,
            title: config.title,
            description: config.description,
            seats: config.seats,
            candidateCap: config.candidateCap,
            eligibleVoterRoles: config.eligibleVoterRoles,
            eligibleCandidateRoles: config.eligibleCandidateRoles,
            nominationStart,
            nominationEnd,
            votingStart,
            votingEnd,
            status: 'NOMINATING',
            locked: false,
            snapshotHash: '',
            createdBy: config.createdBy,
            createdAt: now
        });
        await election.save();
        await this.logAction(electionId, 'CREATE', config.createdBy, null, {
            title: config.title,
            seats: config.seats,
            nominationHours: config.nominationHours,
            votingHours: config.votingHours,
            candidateCap: config.candidateCap
        }, config.guildId);
        this.logOperation('Election created successfully', { electionId });
        return election;
    }
    async registerCandidate(electionId, candidateData, guildId) {
        this.logOperation('Registering candidate', { electionId, userId: candidateData.userId });
        const session = await mongoose_1.default.startSession();
        session.startTransaction();
        try {
            const election = await WealthElection_1.default.findOne({ electionId, status: 'NOMINATING' }).session(session);
            if (!election) {
                throw new errorHandler_1.ValidationError('Election not found or not accepting nominations');
            }
            if (!election.isNominationOpen()) {
                throw new errorHandler_1.ValidationError('Nomination period has ended');
            }
            if (election.candidateCap) {
                const currentCandidates = await models_1.ElectionCandidate.countDocuments({
                    electionId,
                    withdrawn: false,
                    disqualified: false
                }).session(session);
                if (currentCandidates >= election.candidateCap) {
                    throw new errorHandler_1.ValidationError(`Maximum of ${election.candidateCap} candidates allowed`);
                }
            }
            const existingCandidate = await models_1.ElectionCandidate.findOne({
                electionId,
                userId: candidateData.userId
            }).session(session);
            if (existingCandidate && !existingCandidate.withdrawn && !existingCandidate.disqualified) {
                throw new errorHandler_1.ValidationError('User is already a candidate in this election');
            }
            const candidateId = (0, crypto_1.randomUUID)();
            const candidate = new models_1.ElectionCandidate({
                candidateId,
                electionId,
                userId: candidateData.userId,
                guildId: election.guildId,
                displayName: candidateData.displayName,
                username: candidateData.username,
                bio: candidateData.bio,
                campaignMessage: candidateData.campaignMessage,
                nominationTime: new Date(),
                withdrawn: false,
                disqualified: false
            });
            await candidate.save({ session });
            await this.logAction(electionId, 'NOMINATE', candidateData.userId, candidateId, {
                displayName: candidateData.displayName,
                username: candidateData.username,
                bio: candidateData.bio.substring(0, 100) + (candidateData.bio.length > 100 ? '...' : '')
            }, guildId, session);
            await session.commitTransaction();
            this.logOperation('Candidate registered successfully', { candidateId, electionId });
            return candidate;
        }
        catch (error) {
            await session.abortTransaction();
            this.logger.error('[WealthElectionService] Failed to register candidate', { error, electionId, userId: candidateData.userId });
            throw error;
        }
        finally {
            await session.endSession();
        }
    }
    async takeBalanceSnapshot(electionId, client) {
        this.logOperation('Taking balance snapshot', { electionId });
        const election = await WealthElection_1.default.findOne({ electionId });
        if (!election) {
            throw new errorHandler_1.ValidationError('Election not found');
        }
        if (!election.canTakeSnapshot()) {
            throw new errorHandler_1.ValidationError('Cannot take snapshot at this time');
        }
        const guild = await client.guilds.fetch(election.guildId);
        const eligibleUserIds = new Set();
        for (const roleId of election.eligibleVoterRoles) {
            try {
                const role = guild.roles.cache.get(roleId);
                if (role) {
                    role.members.forEach(member => eligibleUserIds.add(member.id));
                }
            }
            catch (error) {
                this.logger.warn(`[WealthElectionService] Could not fetch role ${roleId}`, { error });
            }
        }
        this.logger.info(`[WealthElectionService] Found ${eligibleUserIds.size} eligible voters for snapshot`);
        const snapshotData = [];
        const snapshotTime = new Date();
        let totalWeight = 0;
        const economyService = this.app.getService('EconomyService');
        for (const userId of eligibleUserIds) {
            try {
                const balance = await economyService.getBalance(userId, election.guildId);
                const snapshotRecord = {
                    snapshotId: (0, crypto_1.randomUUID)(),
                    electionId,
                    userId,
                    guildId: election.guildId,
                    balance,
                    recordedAt: snapshotTime
                };
                snapshotData.push(snapshotRecord);
                totalWeight += balance;
            }
            catch (error) {
                this.logger.warn(`[WealthElectionService] Failed to get balance for user ${userId}`, { error });
                snapshotData.push({
                    snapshotId: (0, crypto_1.randomUUID)(),
                    electionId,
                    userId,
                    guildId: election.guildId,
                    balance: 0,
                    recordedAt: snapshotTime
                });
            }
        }
        const snapshotSummary = snapshotData
            .sort((a, b) => a.userId.localeCompare(b.userId))
            .map(s => `${s.userId}:${s.balance}`)
            .join('|');
        const snapshotHash = (0, crypto_2.createHash)('sha256')
            .update(`${electionId}:${snapshotTime.toISOString()}:${snapshotSummary}`)
            .digest('hex');
        await models_1.SnapshotBalance.insertMany(snapshotData);
        await WealthElection_1.default.updateOne({ electionId }, {
            snapshotTime,
            snapshotHash,
            status: 'VOTING'
        });
        await this.logAction(electionId, 'SNAPSHOT', 'SYSTEM', null, {
            totalUsers: snapshotData.length,
            totalWeight,
            snapshotHash,
            eligibleRoles: election.eligibleVoterRoles.length
        }, election.guildId);
        this.logOperation('Balance snapshot completed', {
            electionId,
            totalUsers: snapshotData.length,
            totalWeight,
            snapshotHash
        });
        return snapshotHash;
    }
    async castVote(electionId, voterUserId, candidateId) {
        this.logOperation('Casting vote', { electionId, voterUserId, candidateId });
        const session = await mongoose_1.default.startSession();
        session.startTransaction();
        try {
            const election = await WealthElection_1.default.findOne({ electionId, status: 'VOTING' }).session(session);
            if (!election) {
                throw new errorHandler_1.ValidationError('Election not found or not in voting phase');
            }
            if (!election.isVotingOpen()) {
                throw new errorHandler_1.ValidationError('Voting period has ended');
            }
            const candidate = await models_1.ElectionCandidate.findOne({
                candidateId,
                electionId,
                withdrawn: false,
                disqualified: false
            }).session(session);
            if (!candidate) {
                throw new errorHandler_1.ValidationError('Candidate not found or not eligible');
            }
            const voterSnapshot = await models_1.SnapshotBalance.findOne({ electionId, userId: voterUserId });
            if (!voterSnapshot) {
                throw new errorHandler_1.ValidationError('You are not eligible to vote in this election');
            }
            if (!voterSnapshot.validateProofHash()) {
                this.logger.error('[WealthElectionService] Snapshot integrity violation detected', {
                    electionId,
                    voterUserId,
                    snapshotId: voterSnapshot.snapshotId
                });
                throw new errorHandler_1.ValidationError('Voting data integrity error - please contact an administrator');
            }
            const weight = voterSnapshot.balance;
            if (weight <= 0) {
                throw new errorHandler_1.ValidationError('You must have PLC balance to vote');
            }
            const existingBallot = await models_1.ElectionBallot.findOne({
                electionId,
                voterUserId,
                replaced: false
            }).session(session);
            const ballotId = (0, crypto_1.randomUUID)();
            let previousVote = false;
            if (existingBallot) {
                existingBallot.markAsReplaced(ballotId);
                await existingBallot.save({ session });
                previousVote = true;
            }
            const ballot = new models_1.ElectionBallot({
                ballotId,
                electionId,
                voterUserId,
                candidateId,
                weight,
                timestamp: new Date(),
                guildId: election.guildId,
                replaced: false
            });
            await ballot.save({ session });
            await this.logAction(electionId, 'VOTE', voterUserId, candidateId, {
                weight,
                candidateName: candidate.displayName,
                previousVote,
                snapshotHash: election.snapshotHash
            }, election.guildId, session);
            await session.commitTransaction();
            this.logOperation('Vote cast successfully', { electionId, voterUserId, candidateId, weight });
            return {
                voteWeight: weight,
                isUpdate: previousVote,
                ballot
            };
        }
        catch (error) {
            await session.abortTransaction();
            this.logger.error('[WealthElectionService] Failed to cast vote', { error, electionId, voterUserId, candidateId });
            throw error;
        }
        finally {
            await session.endSession();
        }
    }
    async endElection(electionId, endedBy) {
        this.logOperation('Ending election', { electionId, endedBy });
        const election = await WealthElection_1.default.findOne({ electionId });
        if (!election) {
            throw new errorHandler_1.ValidationError('Election not found');
        }
        if (election.status === 'ENDED') {
            throw new errorHandler_1.ValidationError('Election has already ended');
        }
        const results = await this.computeFinalTally(electionId);
        const endedAt = new Date();
        await WealthElection_1.default.updateOne({ electionId }, {
            status: 'ENDED',
            endedBy,
            endedAt
        });
        const summary = {
            results,
            totalVotes: results.reduce((sum, r) => sum + r.votes, 0),
            totalWeight: results.reduce((sum, r) => sum + r.weight, 0),
            totalCandidates: results.length,
            snapshotHash: election.snapshotHash,
            completedAt: endedAt
        };
        await this.logAction(electionId, 'END', endedBy, null, {
            totalVotes: summary.totalVotes,
            totalWeight: summary.totalWeight,
            totalCandidates: summary.totalCandidates,
            winners: results.filter(r => r.winner).map(r => r.candidate.displayName)
        }, election.guildId);
        this.logOperation('Election ended successfully', { electionId, ...summary });
        return summary;
    }
    async computeFinalTally(electionId) {
        const ballots = await models_1.ElectionBallot.find({ electionId, replaced: false });
        const candidates = await models_1.ElectionCandidate.find({
            electionId,
            withdrawn: false,
            disqualified: false
        });
        const tallies = new Map();
        candidates.forEach(candidate => {
            tallies.set(candidate.candidateId, { votes: 0, weight: 0, candidate });
        });
        ballots.forEach(ballot => {
            const tally = tallies.get(ballot.candidateId);
            if (tally) {
                tally.votes += 1;
                tally.weight += ballot.weight;
            }
        });
        const election = await WealthElection_1.default.findOne({ electionId });
        if (!election)
            throw new Error('Election not found during tally');
        const results = await Promise.all(Array.from(tallies.values()).map(async (tally) => {
            const candidateSnapshot = await models_1.SnapshotBalance.findOne({
                electionId,
                userId: tally.candidate.userId
            });
            return {
                ...tally,
                candidateBalance: candidateSnapshot?.balance || 0
            };
        }));
        results.sort((a, b) => {
            if (b.weight !== a.weight)
                return b.weight - a.weight;
            if (b.candidateBalance !== a.candidateBalance) {
                return b.candidateBalance - a.candidateBalance;
            }
            if (b.votes !== a.votes)
                return b.votes - a.votes;
            const aHash = (0, crypto_2.createHash)('sha256')
                .update(`${election.snapshotHash}:${a.candidate.candidateId}`)
                .digest('hex');
            const bHash = (0, crypto_2.createHash)('sha256')
                .update(`${election.snapshotHash}:${b.candidate.candidateId}`)
                .digest('hex');
            return aHash < bHash ? -1 : 1;
        });
        return results.map((result, index) => ({
            rank: index + 1,
            candidate: result.candidate,
            votes: result.votes,
            weight: result.weight,
            winner: index < election.seats
        }));
    }
    async getElection(electionId) {
        return await WealthElection_1.default.findOne({ electionId });
    }
    async getElectionCandidates(electionId) {
        return await models_1.ElectionCandidate.find({
            electionId,
            withdrawn: false,
            disqualified: false
        }).sort({ nominationTime: 1 });
    }
    async getElectionCandidate(electionId, userId) {
        return await models_1.ElectionCandidate.findOne({
            electionId,
            userId
        });
    }
    async getUserVote(electionId, userId) {
        return await models_1.ElectionBallot.findOne({
            electionId,
            voterUserId: userId,
            replaced: false
        });
    }
    async withdrawCandidate(electionId, userId, reason) {
        const candidate = await models_1.ElectionCandidate.findOne({ electionId, userId });
        if (!candidate) {
            throw new errorHandler_1.ValidationError('Candidate not found');
        }
        if (candidate.withdrawn) {
            throw new errorHandler_1.ValidationError('Candidate has already withdrawn');
        }
        candidate.withdraw();
        await candidate.save();
        await this.logAction(electionId, 'WITHDRAW', userId, candidate.candidateId, {
            candidateName: candidate.displayName,
            reason
        }, candidate.guildId);
    }
    async completeElection(electionId) {
        const election = await WealthElection_1.default.findOne({ electionId });
        if (!election) {
            throw new errorHandler_1.ValidationError('Election not found');
        }
        if (election.status === 'ENDED' || election.status === 'CANCELLED') {
            throw new errorHandler_1.ValidationError('Election has already ended');
        }
        await WealthElection_1.default.updateOne({ electionId }, {
            status: 'ENDED',
            endedAt: new Date()
        });
        return await WealthElection_1.default.findOne({ electionId });
    }
    async getElectionResults(electionId) {
        const ballots = await models_1.ElectionBallot.find({ electionId, replaced: false });
        const candidates = await models_1.ElectionCandidate.find({
            electionId,
            withdrawn: false,
            disqualified: false
        });
        const candidateResults = new Map();
        let totalVotes = 0;
        let totalVoteWeight = 0;
        candidates.forEach(candidate => {
            candidateResults.set(candidate.candidateId, {
                userId: candidate.userId,
                displayName: candidate.displayName,
                voteCount: 0,
                totalVoteWeight: 0
            });
        });
        ballots.forEach(ballot => {
            const candidateData = candidateResults.get(ballot.candidateId);
            if (candidateData) {
                candidateData.voteCount++;
                candidateData.totalVoteWeight += ballot.weight;
                totalVotes++;
                totalVoteWeight += ballot.weight;
            }
        });
        const sortedCandidates = Array.from(candidateResults.values())
            .sort((a, b) => b.totalVoteWeight - a.totalVoteWeight)
            .map((candidate, index) => ({
            ...candidate,
            rank: index + 1
        }));
        return {
            candidates: sortedCandidates,
            totalVotes,
            totalVoteWeight
        };
    }
    validateElectionConfig(config) {
        if (!config.title || config.title.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Election title is required');
        }
        if (!config.description || config.description.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Election description is required');
        }
        if (config.seats < 1 || config.seats > 25) {
            throw new errorHandler_1.ValidationError('Seats must be between 1 and 25');
        }
        if (config.nominationHours < 1 || config.nominationHours > 168) {
            throw new errorHandler_1.ValidationError('Nomination period must be between 1 and 168 hours');
        }
        if (config.votingHours < 1 || config.votingHours > 168) {
            throw new errorHandler_1.ValidationError('Voting period must be between 1 and 168 hours');
        }
        if (!config.eligibleVoterRoles || config.eligibleVoterRoles.length === 0) {
            throw new errorHandler_1.ValidationError('At least one eligible voter role is required');
        }
        if (!config.eligibleCandidateRoles || config.eligibleCandidateRoles.length === 0) {
            throw new errorHandler_1.ValidationError('At least one eligible candidate role is required');
        }
    }
    async logAction(electionId, actionType, actorId, targetId, details, guildId, session) {
        try {
            const auditData = {
                logId: (0, crypto_1.randomUUID)(),
                electionId,
                actionType,
                actorId,
                targetId,
                details,
                timestamp: new Date(),
                guildId
            };
            if (session) {
                await models_1.ElectionAuditLog.create([auditData], { session });
            }
            else {
                await models_1.ElectionAuditLog.create(auditData);
            }
        }
        catch (error) {
            this.logger.error('[WealthElectionService] Failed to log action', { error, actionType });
        }
    }
}
exports.WealthElectionService = WealthElectionService;
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WealthElectionService.prototype, "createElection", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", Promise)
], WealthElectionService.prototype, "registerCandidate", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, discord_js_1.Client]),
    __metadata("design:returntype", Promise)
], WealthElectionService.prototype, "takeBalanceSnapshot", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], WealthElectionService.prototype, "castVote", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], WealthElectionService.prototype, "endElection", null);
