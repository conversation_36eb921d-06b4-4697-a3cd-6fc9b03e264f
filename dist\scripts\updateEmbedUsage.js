"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateEmbedUsage = main;
const fs_1 = __importDefault(require("fs"));
const priorityUpdates = [
    {
        file: 'src/commands/balance.ts',
        updates: [
            {
                from: 'createEconomyEmbed(\'Your Balance\')',
                to: 'await createServerEconomyEmbed(interaction.guild!.id, \'Your Balance\')'
            },
            {
                from: 'formatCoins(user.balance)',
                to: 'await formatServerCoins(interaction.guild!.id, user.balance)'
            }
        ]
    },
    {
        file: 'src/commands/leaderboard.ts',
        updates: [
            {
                from: 'createEconomyEmbed(\'Leaderboard\')',
                to: 'await createServerEconomyEmbed(interaction.guild!.id, \'Leaderboard\')'
            },
            {
                from: 'createEconomyEmbed(\'Phalanx Loyalty Coins Leaderboard\')',
                to: 'await createServerEconomyEmbed(interaction.guild!.id, \'Leaderboard\')'
            },
            {
                from: 'formatCoins(user.balance)',
                to: 'await formatServerCoins(interaction.guild!.id, user.balance)'
            }
        ]
    },
    {
        file: 'src/commands/roles.ts',
        updates: [
            {
                from: 'createEconomyEmbed(\'Role Achievements\')',
                to: 'await createServerEconomyEmbed(interaction.guild!.id, \'Role Achievements\')'
            },
            {
                from: 'createEconomyEmbed(\'Phalanx Role Achievements\')',
                to: 'await createServerEconomyEmbed(interaction.guild!.id, \'Role Achievements\')'
            },
            {
                from: 'formatCoins(',
                to: 'await formatServerCoins(interaction.guild!.id, '
            }
        ]
    }
];
function updateFile(filePath, updates) {
    try {
        let content = fs_1.default.readFileSync(filePath, 'utf-8');
        let modified = false;
        updates.forEach(update => {
            if (content.includes(update.from)) {
                content = content.replace(new RegExp(update.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), update.to);
                modified = true;
                console.log(`✅ Updated ${filePath}: ${update.from} → ${update.to}`);
            }
        });
        if (modified) {
            fs_1.default.writeFileSync(filePath, content, 'utf-8');
            console.log(`📝 Saved changes to ${filePath}`);
        }
        else {
            console.log(`⏭️  No changes needed for ${filePath}`);
        }
    }
    catch (error) {
        console.error(`❌ Failed to update ${filePath}:`, error);
    }
}
function main() {
    console.log('🔄 Updating critical embed usage to use server-specific configurations...\n');
    priorityUpdates.forEach(({ file, updates }) => {
        console.log(`\n📁 Processing ${file}:`);
        updateFile(file, updates);
    });
    console.log('\n✨ Priority updates completed!');
    console.log('\n📋 Next steps:');
    console.log('1. Test the updated commands to ensure they work correctly');
    console.log('2. Update remaining files using similar patterns');
    console.log('3. Run the audit script again to check progress');
    console.log('4. Update import statements to include server-specific functions');
}
if (require.main === module) {
    main();
}
