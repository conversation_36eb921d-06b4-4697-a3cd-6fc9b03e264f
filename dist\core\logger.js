"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loggers = exports.CategoryLogger = exports.Logger = void 0;
exports.getLogger = getLogger;
exports.createLogger = createLogger;
exports.shutdownLogger = shutdownLogger;
const winston_1 = __importDefault(require("winston"));
const config_1 = require("../config");
const constants_1 = require("../config/constants");
const developmentFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.colorize(), winston_1.default.format.printf(({ timestamp, level, message, category, ...meta }) => {
    const categoryStr = category ? `[${category}]` : '';
    const metaStr = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
    return `${timestamp} ${level} ${categoryStr} ${message}${metaStr}`;
}));
const productionFormat = winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json());
class Logger {
    constructor(config) {
        this.config = config || (0, config_1.getLoggingConfig)();
        this.winston = this.createLogger();
    }
    createLogger() {
        const transports = [];
        transports.push(new winston_1.default.transports.Console({
            level: this.config.level || 'info',
            format: (0, config_1.isDevelopment)() ? developmentFormat : productionFormat,
        }));
        if (this.config.file && this.config.filePath) {
            transports.push(new winston_1.default.transports.File({
                filename: this.config.filePath,
                level: this.config.level,
                format: productionFormat,
                maxsize: 10 * 1024 * 1024,
                maxFiles: 5,
                tailable: true,
            }));
        }
        return winston_1.default.createLogger({
            level: this.config.level,
            format: this.config.format === 'json' ? productionFormat : developmentFormat,
            transports,
            exitOnError: false,
        });
    }
    error(message, meta) {
        if (meta && (meta instanceof Error || meta?.error || meta?.stack || meta?.name || meta?.code || meta?.reason || meta?.errorObject)) {
            console.error('[Logger][Raw Error]', message, meta);
        }
        this.winston.error(message, this.formatMeta(meta));
    }
    warn(message, meta) {
        this.winston.warn(message, this.formatMeta(meta));
    }
    info(message, meta) {
        this.winston.info(message, this.formatMeta(meta));
    }
    debug(message, meta) {
        this.winston.debug(message, this.formatMeta(meta));
    }
    logWithCategory(level, category, message, meta) {
        this.winston.log(level, message, { ...this.formatMeta(meta), category });
    }
    formatMeta(meta) {
        if (!meta)
            return {};
        let errorSource = null;
        if (meta instanceof Error) {
            errorSource = meta;
        }
        else if (meta && meta.error instanceof Error) {
            errorSource = meta.error;
        }
        if (errorSource) {
            const formattedError = {
                name: errorSource.name,
                message: errorSource.message,
                stack: errorSource.stack,
                ...(errorSource.reason && { reason: errorSource.reason.toString() }),
            };
            if (meta && meta.error instanceof Error) {
                return { ...meta, error: formattedError };
            }
            return formattedError;
        }
        try {
            JSON.stringify(meta);
            return meta;
        }
        catch (error) {
            return { serializedMeta: String(meta) };
        }
    }
    child(category) {
        return new CategoryLogger(this, category);
    }
    getWinstonLogger() {
        return this.winston;
    }
    setLevel(level) {
        this.winston.level = level;
        this.winston.transports.forEach(transport => {
            transport.level = level;
        });
    }
    close() {
        this.winston.close();
    }
}
exports.Logger = Logger;
class CategoryLogger {
    constructor(parent, category) {
        this.parent = parent;
        this.category = category;
    }
    error(message, meta) {
        this.parent.logWithCategory('error', this.category, message, meta);
    }
    warn(message, meta) {
        this.parent.logWithCategory('warn', this.category, message, meta);
    }
    info(message, meta) {
        this.parent.logWithCategory('info', this.category, message, meta);
    }
    debug(message, meta) {
        this.parent.logWithCategory('debug', this.category, message, meta);
    }
}
exports.CategoryLogger = CategoryLogger;
let globalLogger = null;
function getLogger() {
    if (!globalLogger) {
        globalLogger = new Logger();
    }
    return globalLogger;
}
function createLogger(category) {
    return getLogger().child(category);
}
exports.loggers = {
    economy: () => createLogger(constants_1.LOGGING.CATEGORIES.ECONOMY),
    milestone: () => createLogger(constants_1.LOGGING.CATEGORIES.MILESTONE),
    dynasty: () => createLogger(constants_1.LOGGING.CATEGORIES.DYNASTY),
    admin: () => createLogger(constants_1.LOGGING.CATEGORIES.ADMIN),
    database: () => createLogger(constants_1.LOGGING.CATEGORIES.DATABASE),
    discord: () => createLogger(constants_1.LOGGING.CATEGORIES.DISCORD),
};
function shutdownLogger() {
    if (globalLogger) {
        globalLogger.close();
        globalLogger = null;
    }
}
exports.default = Logger;
