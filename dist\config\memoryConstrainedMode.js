"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ESSENTIAL_CONFIG = exports.ULTRA_MINIMAL_CONFIG = void 0;
exports.getMemoryConstrainedConfig = getMemoryConstrainedConfig;
exports.applyMemoryConstrainedConfig = applyMemoryConstrainedConfig;
exports.isFeatureEnabledInMemoryMode = isFeatureEnabledInMemoryMode;
exports.getMemoryRecommendations = getMemoryRecommendations;
exports.initializeMemoryConstrainedMode = initializeMemoryConstrainedMode;
exports.ULTRA_MINIMAL_CONFIG = {
    features: {
        tradeSystem: false,
        electionSystem: false,
        salarySystem: false,
        suggestionSystem: false,
        milestoneSystem: false,
        reactionRewards: false,
        taxSystem: false,
        dynastySystem: false,
    },
    memory: {
        maxHeapMB: 300,
        warningThresholdMB: 200,
        criticalThresholdMB: 250,
        gcInterval: 50,
    },
    cache: {
        maxDiscordCacheSize: 50,
        maxDatabaseCacheSize: 20,
        sweepIntervalMs: 30000,
        disableMessageCache: true,
        disableMemberCache: true,
        disablePresenceCache: true,
    },
    database: {
        maxPoolSize: 1,
        minPoolSize: 1,
        maxIdleTimeMs: 10000,
        enableCompression: true,
    },
    logging: {
        level: 'warn',
        disableColors: true,
        maxLogFiles: 1,
    }
};
exports.ESSENTIAL_CONFIG = {
    features: {
        tradeSystem: false,
        electionSystem: false,
        salarySystem: false,
        suggestionSystem: true,
        milestoneSystem: true,
        reactionRewards: true,
        taxSystem: false,
        dynastySystem: false,
    },
    memory: {
        maxHeapMB: 350,
        warningThresholdMB: 250,
        criticalThresholdMB: 300,
        gcInterval: 100,
    },
    cache: {
        maxDiscordCacheSize: 100,
        maxDatabaseCacheSize: 50,
        sweepIntervalMs: 60000,
        disableMessageCache: true,
        disableMemberCache: false,
        disablePresenceCache: true,
    },
    database: {
        maxPoolSize: 2,
        minPoolSize: 1,
        maxIdleTimeMs: 30000,
        enableCompression: true,
    },
    logging: {
        level: 'info',
        disableColors: true,
        maxLogFiles: 2,
    }
};
function getMemoryConstrainedConfig() {
    const mode = process.env.MEMORY_MODE || 'essential';
    switch (mode.toLowerCase()) {
        case 'ultra-minimal':
        case 'minimal':
            return exports.ULTRA_MINIMAL_CONFIG;
        case 'essential':
        default:
            return exports.ESSENTIAL_CONFIG;
    }
}
function applyMemoryConstrainedConfig(config) {
    Object.entries(config.features).forEach(([feature, enabled]) => {
        const envVar = `ENABLE_${feature.toUpperCase()}`;
        process.env[envVar] = enabled.toString();
    });
    process.env.MEMORY_LIMIT_MB = config.memory.maxHeapMB.toString();
    process.env.MEMORY_WARNING_MB = config.memory.warningThresholdMB.toString();
    process.env.MEMORY_CRITICAL_MB = config.memory.criticalThresholdMB.toString();
    process.env.MAX_DISCORD_CACHE_SIZE = config.cache.maxDiscordCacheSize.toString();
    process.env.DISABLE_MESSAGE_CACHE = config.cache.disableMessageCache.toString();
    process.env.DISABLE_MEMBER_CACHE = config.cache.disableMemberCache.toString();
    process.env.DISABLE_PRESENCE_CACHE = config.cache.disablePresenceCache.toString();
    process.env.DB_MAX_POOL_SIZE = config.database.maxPoolSize.toString();
    process.env.DB_MIN_POOL_SIZE = config.database.minPoolSize.toString();
    process.env.LOG_LEVEL = config.logging.level;
    process.env.DISABLE_COLORS = config.logging.disableColors.toString();
    console.log('[MemoryConstrainedMode] Configuration applied:', {
        mode: process.env.MEMORY_MODE || 'essential',
        maxHeapMB: config.memory.maxHeapMB,
        enabledFeatures: Object.entries(config.features)
            .filter(([, enabled]) => enabled)
            .map(([feature]) => feature)
    });
}
function isFeatureEnabledInMemoryMode(feature) {
    if (process.env.MEMORY_CONSTRAINED_MODE !== 'true') {
        return true;
    }
    const config = getMemoryConstrainedConfig();
    return config.features[feature];
}
function getMemoryRecommendations() {
    const memUsage = process.memoryUsage();
    const heapMB = memUsage.heapUsed / 1024 / 1024;
    const config = getMemoryConstrainedConfig();
    const recommendations = [];
    if (heapMB > config.memory.criticalThresholdMB) {
        recommendations.push('CRITICAL: Switch to ultra-minimal mode');
        recommendations.push('Consider disabling all non-essential features');
        recommendations.push('Reduce database pool size to 1');
    }
    else if (heapMB > config.memory.warningThresholdMB) {
        recommendations.push('Consider switching to ultra-minimal mode');
        recommendations.push('Disable trade system if enabled');
        recommendations.push('Reduce Discord cache sizes');
    }
    if (process.env.MEMORY_MODE !== 'ultra-minimal' && heapMB > 200) {
        recommendations.push('Consider ultra-minimal mode for maximum savings');
    }
    return recommendations;
}
function initializeMemoryConstrainedMode() {
    if (process.env.MEMORY_CONSTRAINED_MODE === 'true') {
        console.log('[MemoryConstrainedMode] Initializing memory-constrained mode...');
        const config = getMemoryConstrainedConfig();
        applyMemoryConstrainedConfig(config);
        if (global.gc && typeof global.gc === 'function') {
            setInterval(() => {
                try {
                    global.gc();
                }
                catch (error) {
                }
            }, config.memory.gcInterval * 1000);
        }
        console.log('[MemoryConstrainedMode] Memory-constrained mode initialized');
    }
}
