"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.commandRegistry = exports.CommandRegistry = exports.BaseCommand = exports.CommandCategory = void 0;
const discord_js_1 = require("discord.js");
const logger_1 = require("../../core/logger");
const errorHandler_1 = require("../../utils/errorHandler");
const features_1 = require("../../config/features");
var CommandCategory;
(function (CommandCategory) {
    CommandCategory["ECONOMY"] = "economy";
    CommandCategory["ADMIN"] = "admin";
    CommandCategory["ROLE"] = "role";
    CommandCategory["MILESTONE"] = "milestone";
    CommandCategory["UTILITY"] = "utility";
    CommandCategory["AUTOMATION"] = "automation";
})(CommandCategory || (exports.CommandCategory = CommandCategory = {}));
class BaseCommand {
    constructor(config) {
        this.config = config;
        this.category = config.category;
        this.adminOnly = config.adminOnly || false;
        this.guildOnly = config.guildOnly || true;
        this.cooldown = config.cooldown || 3;
        this.requiredFeatures = config.requiredFeatures || [];
        this.logger = (0, logger_1.createLogger)(`command:${config.name}`);
        this.data = this.buildCommand(config);
    }
    buildCommand(config) {
        const command = new discord_js_1.SlashCommandBuilder()
            .setName(config.name)
            .setDescription(config.description);
        if (config.adminOnly) {
            command.setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator);
        }
        if (config.requiredPermissions) {
            const permissions = config.requiredPermissions.reduce((acc, perm) => {
                return acc | discord_js_1.PermissionFlagsBits[perm];
            }, 0n);
            command.setDefaultMemberPermissions(permissions);
        }
        this.customizeCommand(command);
        return command;
    }
    customizeCommand(command) {
    }
    async execute(interaction) {
        const wrappedExecute = (0, errorHandler_1.withErrorHandler)(async (interaction) => {
            await this.validateExecution(interaction);
            const context = {
                interaction,
                client: interaction.client,
                guild: interaction.guild,
                member: interaction.member,
                logger: this.logger,
            };
            this.logExecution(interaction);
            await this.executeCommand(context);
        });
        await wrappedExecute(interaction);
    }
    async validateExecution(interaction) {
        if (this.guildOnly && !interaction.guild) {
            throw new Error('This command can only be used in a server.');
        }
        for (const feature of this.requiredFeatures) {
            if (!(0, features_1.isFeatureActive)(feature)) {
                throw new Error(`This command requires the ${feature} feature to be enabled.`);
            }
        }
        if (this.adminOnly && interaction.member) {
            const member = interaction.member;
            if (!member.permissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
                throw new Error('This command requires administrator permissions.');
            }
        }
    }
    logExecution(interaction) {
        this.logger.info(`Command executed: ${this.config.name}`, {
            userId: interaction.user.id,
            username: interaction.user.username,
            guildId: interaction.guild?.id,
            guildName: interaction.guild?.name,
            channelId: interaction.channel?.id,
            commandName: this.config.name,
            category: this.category,
        });
    }
    isFeatureEnabled(featureName) {
        return (0, features_1.isFeatureActive)(featureName);
    }
    getConfig() {
        return { ...this.config };
    }
    getMetadata() {
        return {
            name: this.config.name,
            description: this.config.description,
            category: this.category,
            adminOnly: this.adminOnly,
            guildOnly: this.guildOnly,
            cooldown: this.cooldown,
            requiredFeatures: this.requiredFeatures,
        };
    }
}
exports.BaseCommand = BaseCommand;
class CommandRegistry {
    constructor() {
        this.commands = new Map();
        this.categories = new Map();
        this.logger = (0, logger_1.createLogger)('command-registry');
    }
    register(command) {
        if (this.commands.has(command.data.name)) {
            this.logger.warn(`Command already registered: ${command.data.name}`);
            return;
        }
        this.commands.set(command.data.name, command);
        const category = command.category;
        if (!this.categories.has(category)) {
            this.categories.set(category, []);
        }
        this.categories.get(category).push(command);
        this.logger.debug(`Registered command: ${command.data.name} (${command.category})`);
    }
    get(name) {
        return this.commands.get(name);
    }
    getAll() {
        return Array.from(this.commands.values());
    }
    getByCategory(category) {
        return this.categories.get(category) || [];
    }
    getCategories() {
        return Array.from(this.categories.keys());
    }
    getCount() {
        return this.commands.size;
    }
    clear() {
        this.commands.clear();
        this.categories.clear();
        this.logger.debug('Cleared all commands');
    }
}
exports.CommandRegistry = CommandRegistry;
exports.commandRegistry = new CommandRegistry();
