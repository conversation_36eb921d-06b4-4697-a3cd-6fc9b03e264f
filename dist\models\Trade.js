"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("../config/constants");
const tradeSchema = new mongoose_1.Schema({
    tradeId: {
        type: String,
        required: [true, 'Trade ID is required'],
        unique: true,
        index: true
    },
    sellerId: {
        type: String,
        required: [true, 'Seller ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Seller ID must be a valid Discord snowflake'
        },
        index: true
    },
    buyerId: {
        type: String,
        required: [true, 'Buyer ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Buyer ID must be a valid Discord snowflake'
        },
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    amount: {
        type: Number,
        required: [true, 'Trade amount is required'],
        min: [constants_1.TRADE.MIN_TRADE_AMOUNT, `Minimum trade amount is ${constants_1.TRADE.MIN_TRADE_AMOUNT} PLC`],
        max: [constants_1.TRADE.MAX_TRADE_AMOUNT, `Maximum trade amount is ${constants_1.TRADE.MAX_TRADE_AMOUNT} PLC`],
        validate: {
            validator: function (v) {
                return Number.isInteger(v) && v > 0;
            },
            message: 'Trade amount must be a positive integer'
        }
    },
    itemDescription: {
        type: String,
        required: [true, 'Item description is required'],
        maxlength: [500, 'Item description cannot exceed 500 characters'],
        validate: {
            validator: function (v) {
                return v.trim().length > 0;
            },
            message: 'Item description cannot be empty'
        }
    },
    notes: {
        type: String,
        maxlength: [200, 'Notes cannot exceed 200 characters']
    },
    state: {
        type: String,
        enum: Object.values(constants_1.TRADE.STATES),
        required: [true, 'Trade state is required'],
        default: constants_1.TRADE.STATES.PROPOSED,
        index: true
    },
    initiatedBy: {
        type: String,
        enum: ['SELLER', 'BUYER'],
        required: [true, 'Initiator is required']
    },
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    acceptedAt: {
        type: Date,
        index: true
    },
    expiresAt: {
        type: Date,
        required: [true, 'Expiration date is required'],
        index: true
    },
    completedAt: {
        type: Date,
        index: true
    },
    escrowLocked: {
        type: Boolean,
        default: false,
        index: true
    },
    escrowAmount: {
        type: Number,
        default: 0,
        min: [0, 'Escrow amount cannot be negative']
    },
    sellerConfirmed: {
        type: Boolean,
        default: false
    },
    buyerConfirmed: {
        type: Boolean,
        default: false
    },
    sellerConfirmedAt: {
        type: Date
    },
    buyerConfirmedAt: {
        type: Date
    },
    disputeId: {
        type: String,
        index: true
    },
    disputedBy: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Disputed by must be a valid Discord snowflake'
        }
    },
    disputedAt: {
        type: Date
    },
    disputeReason: {
        type: String,
        maxlength: [500, 'Dispute reason cannot exceed 500 characters']
    },
    lastWarningAt: {
        type: Date
    },
    warningsSent: {
        type: Number,
        default: 0,
        min: [0, 'Warnings sent cannot be negative']
    },
    extensionGranted: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: false
});
tradeSchema.index({ sellerId: 1, state: 1 });
tradeSchema.index({ buyerId: 1, state: 1 });
tradeSchema.index({ guildId: 1, state: 1 });
tradeSchema.index({ state: 1, expiresAt: 1 });
tradeSchema.index({ createdAt: -1 });
tradeSchema.virtual('involvedUsers').get(function () {
    return [this.sellerId, this.buyerId];
});
tradeSchema.methods.involvesUser = function (discordId) {
    return this.sellerId === discordId || this.buyerId === discordId;
};
tradeSchema.methods.getOtherParty = function (discordId) {
    if (this.sellerId === discordId)
        return this.buyerId;
    if (this.buyerId === discordId)
        return this.sellerId;
    return null;
};
tradeSchema.methods.isExpired = function () {
    return new Date() > this.expiresAt;
};
tradeSchema.methods.isBothConfirmed = function () {
    return this.sellerConfirmed && this.buyerConfirmed;
};
exports.default = (0, mongoose_1.model)('Trade', tradeSchema);
