"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runtimeConfig = exports.RuntimeConfigManager = void 0;
exports.isFeatureActiveRuntime = isFeatureActiveRuntime;
exports.requireFeatureRuntime = requireFeatureRuntime;
const events_1 = require("events");
const logger_1 = require("../core/logger");
const features_1 = require("./features");
class RuntimeConfigManager extends events_1.EventEmitter {
    constructor() {
        super();
        this.logger = null;
        this.featureOverrides = new Map();
        this.configOverrides = new Map();
        this.logger = null;
    }
    ensureLogger() {
        if (!this.logger) {
            this.logger = (0, logger_1.createLogger)('runtime-config');
        }
    }
    setFeatureOverride(featureName, enabled, reason, source = 'manual', expiresAt) {
        const oldOverride = this.featureOverrides.get(featureName);
        const oldValue = oldOverride?.enabled;
        const override = {
            enabled,
            reason,
            timestamp: new Date(),
            expiresAt,
            source,
        };
        this.featureOverrides.set(featureName, override);
        this.ensureLogger();
        this.logger.info(`Feature override set: ${featureName} = ${enabled}`, {
            featureName,
            enabled,
            reason,
            source,
            expiresAt,
        });
        this.emit('configChange', {
            type: 'feature_toggle',
            feature: featureName,
            oldValue,
            newValue: enabled,
            timestamp: new Date(),
            source,
        });
    }
    removeFeatureOverride(featureName, source = 'manual') {
        const override = this.featureOverrides.get(featureName);
        if (!override) {
            return false;
        }
        this.featureOverrides.delete(featureName);
        this.ensureLogger();
        this.logger.info(`Feature override removed: ${featureName}`, {
            featureName,
            source,
            previousOverride: override,
        });
        this.emit('configChange', {
            type: 'feature_toggle',
            feature: featureName,
            oldValue: override.enabled,
            newValue: undefined,
            timestamp: new Date(),
            source,
        });
        return true;
    }
    isFeatureEnabled(featureName) {
        this.cleanupExpiredOverrides();
        const override = this.featureOverrides.get(featureName);
        if (override) {
            return override.enabled;
        }
        const config = features_1.FEATURE_REGISTRY[featureName];
        return config?.enabled || false;
    }
    getFeatureConfig(featureName) {
        const baseConfig = features_1.FEATURE_REGISTRY[featureName];
        const override = this.featureOverrides.get(featureName);
        if (!baseConfig) {
            throw new Error(`Feature not found: ${featureName}`);
        }
        const config = { ...baseConfig };
        if (override) {
            config.enabled = override.enabled;
            return { ...config, override };
        }
        return config;
    }
    getFeatureOverrides() {
        this.cleanupExpiredOverrides();
        return new Map(this.featureOverrides);
    }
    setConfigOverride(key, value, source = 'manual') {
        const oldValue = this.configOverrides.get(key);
        this.configOverrides.set(key, value);
        this.ensureLogger();
        this.logger.info(`Configuration override set: ${key}`, {
            key,
            oldValue,
            newValue: value,
            source,
        });
        this.emit('configChange', {
            type: 'config_update',
            feature: key,
            oldValue,
            newValue: value,
            timestamp: new Date(),
            source,
        });
    }
    getConfigValue(key, defaultValue) {
        return this.configOverrides.get(key) ?? defaultValue;
    }
    removeConfigOverride(key, source = 'manual') {
        const oldValue = this.configOverrides.get(key);
        const removed = this.configOverrides.delete(key);
        if (removed) {
            this.ensureLogger();
            this.logger.info(`Configuration override removed: ${key}`, {
                key,
                oldValue,
                source,
            });
            this.emit('configChange', {
                type: 'config_update',
                feature: key,
                oldValue,
                newValue: undefined,
                timestamp: new Date(),
                source,
            });
        }
        return removed;
    }
    getStats() {
        this.cleanupExpiredOverrides();
        return {
            featureOverrides: this.featureOverrides.size,
            configOverrides: this.configOverrides.size,
            totalFeatures: Object.keys(features_1.FEATURE_REGISTRY).length,
            enabledFeatures: Object.entries(features_1.FEATURE_REGISTRY)
                .filter(([name]) => this.isFeatureEnabled(name))
                .length,
            overriddenFeatures: Array.from(this.featureOverrides.keys()),
        };
    }
    exportConfig() {
        return {
            timestamp: new Date().toISOString(),
            featureOverrides: Object.fromEntries(Array.from(this.featureOverrides.entries()).map(([key, override]) => [
                key,
                {
                    enabled: override.enabled,
                    reason: override.reason,
                    timestamp: override.timestamp.toISOString(),
                    expiresAt: override.expiresAt?.toISOString(),
                    source: override.source,
                },
            ])),
            configOverrides: Object.fromEntries(this.configOverrides),
        };
    }
    importConfig(config, source = 'import') {
        if (config.featureOverrides) {
            for (const [featureName, overrideDataRaw] of Object.entries(config.featureOverrides)) {
                const overrideData = overrideDataRaw;
                this.setFeatureOverride(featureName, overrideData.enabled ?? false, overrideData.reason || 'Imported configuration', source, overrideData.expiresAt ? new Date(overrideData.expiresAt) : undefined);
            }
        }
        if (config.configOverrides) {
            for (const [key, value] of Object.entries(config.configOverrides)) {
                this.setConfigOverride(key, value, source);
            }
        }
        this.ensureLogger();
        this.logger.info('Configuration imported', {
            featureOverrides: Object.keys(config.featureOverrides || {}).length,
            configOverrides: Object.keys(config.configOverrides || {}).length,
            source,
        });
    }
    resetAll(source = 'manual') {
        const featureCount = this.featureOverrides.size;
        const configCount = this.configOverrides.size;
        this.featureOverrides.clear();
        this.configOverrides.clear();
        this.ensureLogger();
        this.logger.info('All configuration overrides reset', {
            featureOverrides: featureCount,
            configOverrides: configCount,
            source,
        });
        this.emit('configChange', {
            type: 'config_update',
            oldValue: { featureCount, configCount },
            newValue: { featureCount: 0, configCount: 0 },
            timestamp: new Date(),
            source,
        });
    }
    cleanupExpiredOverrides() {
        const now = new Date();
        const expiredFeatures = [];
        for (const [featureName, override] of this.featureOverrides.entries()) {
            if (override.expiresAt && override.expiresAt <= now) {
                expiredFeatures.push(featureName);
            }
        }
        for (const featureName of expiredFeatures) {
            this.removeFeatureOverride(featureName, 'auto-expire');
        }
    }
    startCleanupSchedule(intervalMs = 60000) {
        setInterval(() => {
            this.cleanupExpiredOverrides();
            this.performMemoryOptimization();
        }, intervalMs);
        this.ensureLogger();
        this.logger.info('Cleanup schedule started with memory optimization', { intervalMs });
    }
    performMemoryOptimization() {
        const MAX_OVERRIDES = 100;
        if (this.featureOverrides.size > MAX_OVERRIDES) {
            const entriesToRemove = this.featureOverrides.size - MAX_OVERRIDES;
            const keys = Array.from(this.featureOverrides.keys());
            for (let i = 0; i < entriesToRemove; i++) {
                this.featureOverrides.delete(keys[i]);
            }
            this.ensureLogger();
            this.logger.debug('Memory optimization: removed old feature overrides', {
                removed: entriesToRemove,
                remaining: this.featureOverrides.size
            });
        }
    }
}
exports.RuntimeConfigManager = RuntimeConfigManager;
exports.runtimeConfig = new RuntimeConfigManager();
function isFeatureActiveRuntime(featureName) {
    return exports.runtimeConfig.isFeatureEnabled(featureName);
}
function requireFeatureRuntime(featureName) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = function (...args) {
            if (!isFeatureActiveRuntime(featureName)) {
                throw new Error(`Feature ${featureName} is not enabled`);
            }
            return originalMethod.apply(this, args);
        };
        return descriptor;
    };
}
exports.default = exports.runtimeConfig;
