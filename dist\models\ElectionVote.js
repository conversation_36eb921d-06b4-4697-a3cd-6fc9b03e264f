"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const electionVoteSchema = new mongoose_1.Schema({
    voteId: {
        type: String,
        required: [true, 'Vote ID is required'],
        unique: true,
        index: true
    },
    electionId: {
        type: String,
        required: [true, 'Election ID is required'],
        validate: {
            validator: function (v) {
                return v.trim().length > 0;
            },
            message: 'Election ID cannot be empty'
        },
        index: true
    },
    voterId: {
        type: String,
        required: [true, 'Voter ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Voter ID must be a valid Discord snowflake'
        },
        index: true
    },
    candidateId: {
        type: String,
        required: [true, 'Candidate ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Candidate ID must be a valid Discord snowflake'
        },
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    voteWeight: {
        type: Number,
        required: [true, 'Vote weight is required'],
        min: [0, 'Vote weight cannot be negative']
    },
    voterBalance: {
        type: Number,
        required: [true, 'Voter balance is required'],
        min: [0, 'Voter balance cannot be negative']
    },
    voterDisplayName: {
        type: String,
        required: [true, 'Voter display name is required'],
        maxlength: [100, 'Voter display name cannot exceed 100 characters'],
        trim: true
    },
    voterUsername: {
        type: String,
        required: [true, 'Voter username is required'],
        maxlength: [50, 'Voter username cannot exceed 50 characters'],
        trim: true
    },
    candidateDisplayName: {
        type: String,
        required: [true, 'Candidate display name is required'],
        maxlength: [100, 'Candidate display name cannot exceed 100 characters'],
        trim: true
    },
    candidateUsername: {
        type: String,
        required: [true, 'Candidate username is required'],
        maxlength: [50, 'Candidate username cannot exceed 50 characters'],
        trim: true
    },
    status: {
        type: String,
        enum: ['ACTIVE', 'REPLACED', 'INVALIDATED'],
        default: 'ACTIVE',
        index: true
    },
    votedAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    replacedAt: {
        type: Date,
        index: true
    },
    invalidatedAt: {
        type: Date,
        index: true
    },
    replacedBy: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^[0-9a-fA-F]{24}$/.test(v);
            },
            message: 'Replaced by must be a valid ObjectId'
        }
    },
    invalidationReason: {
        type: String,
        maxlength: [200, 'Invalidation reason cannot exceed 200 characters'],
        trim: true
    },
    ipAddress: {
        type: String,
        validate: {
            validator: function (v) {
                if (!v)
                    return true;
                const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
                const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
                return ipv4Regex.test(v) || ipv6Regex.test(v);
            },
            message: 'Invalid IP address format'
        }
    }
}, {
    timestamps: false
});
electionVoteSchema.index({ electionId: 1, status: 1 });
electionVoteSchema.index({ electionId: 1, candidateId: 1, status: 1 });
electionVoteSchema.index({ voterId: 1, electionId: 1 }, { unique: true });
electionVoteSchema.index({ guildId: 1, voterId: 1 });
electionVoteSchema.index({ votedAt: -1 });
exports.default = (0, mongoose_1.model)('ElectionVote', electionVoteSchema);
