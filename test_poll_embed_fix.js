/**
 * Test Poll Embed Fix for Discord Limits
 * 
 * This script tests the fixed embed creation that splits options across multiple fields
 * to respect Discord's 1024-character limit per field.
 */

const { EmbedBuilder, But<PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonStyle } = require('discord.js');

// Mock poll data with 20 realistic options
const mockPoll = {
  pollId: '7c32de61-9d2b-4a90-ba54-3652d1961f2a',
  title: 'Server Improvement Poll',
  description: 'Vote on which improvements you\'d like to see implemented first',
  status: 'ACTIVE',
  createdAt: new Date(),
  totalVotes: 0,
  totalVoteWeight: 0,
  options: [
    'Increase server activity events',
    'Add new gaming channels',
    'Implement weekly contests',
    'Create study groups',
    'Add music bot features',
    'Organize movie nights',
    'Set up coding challenges',
    'Add art showcase channel',
    'Create book club',
    'Add fitness tracking',
    'Implement mentorship program',
    'Add language exchange',
    'Create investment discussions',
    'Add tech news channel',
    'Organize virtual meetups',
    'Add recipe sharing',
    'Create pet photos channel',
    'Add travel stories',
    'Implement skill sharing',
    'Add meditation sessions'
  ],
  getVoteResults: function() {
    return this.options.map(() => ({
      votes: 0,
      weight: 0,
      percentage: 0,
      weightPercentage: 0
    }));
  }
};

// Simulate the fixed createPollEmbed method
function createPollEmbedFixed(poll) {
  const results = poll.getVoteResults();

  // Create embed
  const embed = new EmbedBuilder()
    .setTitle(`📊 ${poll.title}`)
    .setDescription(poll.description)
    .setColor(poll.status === 'ACTIVE' ? 0x00ff00 : 0xff0000)
    .setTimestamp(poll.createdAt);

  // Add poll options with results - Split into multiple fields if needed
  const maxFieldLength = 1000; // Leave buffer under Discord's 1024 limit
  const optionFields = [];
  
  let currentFieldText = '';
  let currentFieldIndex = 1;
  
  poll.options.forEach((option, index) => {
    const result = results[index];
    const bar = '░'.repeat(10); // createProgressBar simulation
    const optionText = `**${index + 1}.** ${option}\n${bar} ${result.votes} votes (${result.percentage.toFixed(1)}%) | 💰 ${result.weight} coins (${result.weightPercentage.toFixed(1)}%)\n\n`;
    
    // Check if adding this option would exceed the field limit
    if (currentFieldText.length + optionText.length > maxFieldLength && currentFieldText.length > 0) {
      // Add current field and start a new one
      optionFields.push({
        name: optionFields.length === 0 ? 'Poll Options' : `Poll Options (continued ${currentFieldIndex})`,
        value: currentFieldText.trim(),
        inline: false
      });
      currentFieldText = optionText;
      currentFieldIndex++;
    } else {
      currentFieldText += optionText;
    }
  });
  
  // Add the final field
  if (currentFieldText.length > 0) {
    optionFields.push({
      name: optionFields.length === 0 ? 'Poll Options' : `Poll Options (continued ${currentFieldIndex})`,
      value: currentFieldText.trim(),
      inline: false
    });
  }
  
  // Add all option fields
  embed.addFields(optionFields);
  
  // Add summary fields
  embed.addFields([
    { name: 'Total Votes', value: `${poll.totalVotes}`, inline: true },
    { name: 'Total Weight', value: `${poll.totalVoteWeight} coins`, inline: true },
    { name: 'Status', value: poll.status, inline: true }
  ]);

  // Create voting buttons (same as before)
  const components = [];

  if (poll.status === 'ACTIVE') {
    const maxVotingButtons = Math.min(poll.options.length, 20);
    const buttonsPerRow = 5;
    const maxVotingRows = 4;

    // Create voting button rows
    for (let row = 0; row < maxVotingRows && row * buttonsPerRow < maxVotingButtons; row++) {
      const startIndex = row * buttonsPerRow;
      const endIndex = Math.min(startIndex + buttonsPerRow, maxVotingButtons);

      const rowButtons = poll.options.slice(startIndex, endIndex).map((option, index) => {
        const optionIndex = startIndex + index;
        return new ButtonBuilder()
          .setCustomId(`poll_vote_${poll.pollId}_${optionIndex}`)
          .setLabel(`${optionIndex + 1}. ${option.substring(0, 18)}${option.length > 18 ? '...' : ''}`)
          .setStyle(ButtonStyle.Primary);
      });

      if (rowButtons.length > 0) {
        components.push(new ActionRowBuilder().addComponents(rowButtons));
      }
    }

    // Add end poll button
    const endButton = new ButtonBuilder()
      .setCustomId(`poll_end_${poll.pollId}`)
      .setLabel('End Poll')
      .setStyle(ButtonStyle.Danger);

    components.push(new ActionRowBuilder().addComponents(endButton));
  }

  return { embed, components };
}

// Test the fix
function testEmbedFix() {
  console.log('🔧 Testing Poll Embed Fix for Discord Limits\n');
  
  const { embed, components } = createPollEmbedFixed(mockPoll);
  
  console.log('📊 Embed Analysis:');
  console.log(`- Total fields: ${embed.data.fields.length}`);
  
  let totalEmbedSize = 0;
  let maxFieldSize = 0;
  let fieldSizes = [];
  
  embed.data.fields.forEach((field, index) => {
    const fieldSize = field.value.length;
    fieldSizes.push(fieldSize);
    totalEmbedSize += fieldSize + field.name.length;
    maxFieldSize = Math.max(maxFieldSize, fieldSize);
    
    console.log(`  Field ${index + 1} (${field.name}): ${fieldSize} characters`);
    
    if (fieldSize > 1024) {
      console.log(`    ❌ EXCEEDS LIMIT by ${fieldSize - 1024} characters`);
    } else {
      console.log(`    ✅ Within limit (${1024 - fieldSize} remaining)`);
    }
  });
  
  console.log(`\n📏 Discord Limits Check:`);
  console.log(`- Largest field: ${maxFieldSize}/1024 characters`);
  console.log(`- Total embed size: ~${totalEmbedSize} characters`);
  console.log(`- Total fields: ${embed.data.fields.length}/25 (Discord limit)`);
  
  console.log(`\n🎛️ Component Analysis:`);
  console.log(`- Action rows: ${components.length}/5`);
  console.log(`- Total buttons: ${components.reduce((total, row) => total + row.components.length, 0)}/25`);
  
  // Check if all limits are satisfied
  const limitsOk = maxFieldSize <= 1024 && 
                   embed.data.fields.length <= 25 && 
                   components.length <= 5 &&
                   components.reduce((total, row) => total + row.components.length, 0) <= 25;
  
  if (limitsOk) {
    console.log('\n✅ All Discord limits satisfied! The fix should work.');
  } else {
    console.log('\n❌ Some limits still exceeded. Further optimization needed.');
  }
  
  return { embed, components, limitsOk };
}

// Test with different option counts
function testVariousOptionCounts() {
  console.log('\n🧪 Testing Various Option Counts\n');
  
  const testCounts = [8, 15, 20];
  
  testCounts.forEach(count => {
    const testPoll = {
      ...mockPoll,
      options: mockPoll.options.slice(0, count)
    };
    
    const { embed } = createPollEmbedFixed(testPoll);
    const maxFieldSize = Math.max(...embed.data.fields.map(f => f.value.length));
    
    console.log(`${count} options:`);
    console.log(`- Fields created: ${embed.data.fields.length}`);
    console.log(`- Largest field: ${maxFieldSize} characters`);
    
    if (maxFieldSize <= 1024) {
      console.log(`✅ All fields within Discord limits`);
    } else {
      console.log(`❌ Field exceeds limit by ${maxFieldSize - 1024} characters`);
    }
    console.log();
  });
}

// Main execution
console.log('🚀 Testing Poll Embed Fix for 20-Option Support\n');
console.log('='.repeat(60));

const result = testEmbedFix();

console.log('='.repeat(60));
testVariousOptionCounts();

console.log('='.repeat(60));
console.log('🎯 Summary:');
console.log('- Fixed embed creation by splitting options across multiple fields');
console.log('- Each field respects Discord\'s 1024-character limit');
console.log('- Maintains all functionality while ensuring Discord API compliance');
console.log('- Should resolve the "Received one or more errors" issue');

if (result.limitsOk) {
  console.log('\n🎉 Fix is ready for deployment!');
} else {
  console.log('\n⚠️ Additional optimization may be needed.');
}
