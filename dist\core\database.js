"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const config_1 = require("../config");
const constants_1 = require("../config/constants");
const models_1 = require("../models");
class DatabaseService {
    constructor(logger, config) {
        this.name = 'DatabaseService';
        this.connectionPromise = null;
        this.logger = logger;
        this.config = config || (0, config_1.getDatabaseConfig)();
        mongoose_1.default.set('strictQuery', false);
        this.setupEventHandlers();
    }
    async initialize() {
        this.logger.info('[Database] Initializing database service');
        await this.connect();
        await this.initializeModels();
        await this.setupIndexes();
        await this.performCleanup();
    }
    async connect() {
        if (this.connectionPromise) {
            return this.connectionPromise;
        }
        this.connectionPromise = this._connect();
        return this.connectionPromise;
    }
    async _connect() {
        try {
            this.logger.info('[Database] Connecting to MongoDB...');
            this.logger.debug('[Database] Connection URI:', this.config.uri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'));
            this.logger.debug('[Database] Connection options:', this.config.options);
            await mongoose_1.default.connect(this.config.uri, {
                ...this.config.options,
                serverSelectionTimeoutMS: constants_1.DATABASE.CONNECTION_TIMEOUT_MS,
            });
            this.logger.info('[Database] Successfully connected to MongoDB');
        }
        catch (error) {
            console.error('RAW MONGOOSE ERROR:', error);
            this.logger.error('[Database] Failed to connect to MongoDB', {
                error: error?.message,
                stack: error?.stack,
                name: error?.name,
                code: error?.code,
                reason: error?.reason,
                errorObject: error,
                configUri: this.config.uri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'),
                configOptions: this.config.options
            });
            this.connectionPromise = null;
            throw error;
        }
    }
    async disconnect() {
        try {
            this.logger.info('[Database] Disconnecting from MongoDB...');
            await mongoose_1.default.disconnect();
            this.connectionPromise = null;
            this.logger.info('[Database] Disconnected from MongoDB');
        }
        catch (error) {
            this.logger.error('[Database] Error disconnecting from MongoDB', { error });
            throw error;
        }
    }
    isConnected() {
        return mongoose_1.default.connection.readyState === 1;
    }
    getConnectionStatus() {
        const states = ['disconnected', 'connected', 'connecting', 'disconnecting'];
        return states[mongoose_1.default.connection.readyState] || 'unknown';
    }
    async shutdown() {
        this.logger.info('[Database] Shutting down database service');
        await this.disconnect();
    }
    setupEventHandlers() {
        mongoose_1.default.connection.on('connected', () => {
            this.logger.info('[Database] Mongoose connected to MongoDB');
            this.logConnectionStats();
        });
        mongoose_1.default.connection.on('error', (error) => {
            this.logger.error('[Database] Mongoose connection error', {
                error: error?.message,
                stack: error?.stack,
                name: error?.name,
                code: error?.code,
                reason: error?.reason,
                errorObject: error
            });
        });
        mongoose_1.default.connection.on('disconnected', () => {
            this.logger.warn('[Database] Mongoose disconnected from MongoDB');
        });
        mongoose_1.default.connection.on('connectionPoolCreated', () => {
            this.logger.debug('[Database] Connection pool created');
        });
        mongoose_1.default.connection.on('connectionPoolClosed', () => {
            this.logger.debug('[Database] Connection pool closed');
        });
        mongoose_1.default.connection.on('connectionCreated', () => {
            this.logger.debug('[Database] New connection created');
            this.logConnectionStats();
        });
        mongoose_1.default.connection.on('connectionClosed', () => {
            this.logger.debug('[Database] Connection closed');
            this.logConnectionStats();
        });
        process.on('SIGINT', async () => {
            await this.shutdown();
            process.exit(0);
        });
    }
    logConnectionStats() {
        try {
            const readyState = mongoose_1.default.connection.readyState;
            const states = ['disconnected', 'connected', 'connecting', 'disconnecting'];
            this.logger.debug('[Database] Connection state', {
                state: states[readyState] || 'unknown',
                readyState
            });
        }
        catch (error) {
        }
    }
    async initializeModels() {
        try {
            this.logger.info('[Database] Initializing models...');
            await (0, models_1.initializeModels)();
            this.logger.info('[Database] Models initialized successfully');
        }
        catch (error) {
            this.logger.error('[Database] Failed to initialize models', { error });
            throw error;
        }
    }
    async setupIndexes() {
        try {
            this.logger.info('[Database] Setting up indexes...');
            const db = mongoose_1.default.connection.db;
            if (!db) {
                throw new Error('Database connection not established');
            }
            for (const indexConfig of constants_1.DATABASE.REQUIRED_INDEXES) {
                const collection = db.collection(indexConfig.collection);
                try {
                    const indexOptions = { background: true };
                    if ('unique' in indexConfig) {
                        indexOptions.unique = indexConfig.unique;
                    }
                    if ('expireAfterSeconds' in indexConfig) {
                        indexOptions.expireAfterSeconds = indexConfig.expireAfterSeconds;
                    }
                    await collection.createIndex(indexConfig.index, indexOptions);
                    this.logger.debug(`[Database] Created index for ${indexConfig.collection}`, {
                        index: indexConfig.index,
                        unique: 'unique' in indexConfig ? indexConfig.unique : undefined,
                    });
                }
                catch (error) {
                    if (error.code !== 11000) {
                        this.logger.warn(`[Database] Failed to create index for ${indexConfig.collection}`, {
                            error: error.message,
                            index: indexConfig.index,
                        });
                    }
                }
            }
            this.logger.info('[Database] Index setup completed');
        }
        catch (error) {
            this.logger.error('[Database] Failed to setup indexes', { error });
            throw error;
        }
    }
    async performCleanup() {
        try {
            this.logger.info('[Database] Performing cleanup operations...');
            const db = mongoose_1.default.connection.db;
            if (!db) {
                throw new Error('Database connection not established');
            }
            const usersCollection = db.collection('users');
            const roleForSalesCollection = db.collection('roleforsales');
            try {
                await usersCollection.dropIndex('userId_1');
                this.logger.info('[Database] Dropped old userId_1 index');
            }
            catch (error) {
                this.logger.debug('[Database] userId_1 index not found (expected)');
            }
            try {
                await roleForSalesCollection.dropIndex('roleId_1');
                this.logger.info('[Database] Dropped old roleId_1 index from roleforsales collection');
            }
            catch (error) {
                this.logger.debug('[Database] roleId_1 index not found in roleforsales collection (expected)');
            }
            const deleteResult = await usersCollection.deleteMany({
                $or: [
                    { discordId: null },
                    { discordId: { $exists: false } },
                    { userId: { $exists: true } }
                ]
            });
            if (deleteResult.deletedCount > 0) {
                this.logger.info(`[Database] Cleaned up ${deleteResult.deletedCount} corrupted user records`);
            }
            this.logger.info('[Database] Cleanup operations completed');
        }
        catch (error) {
            this.logger.error('[Database] Failed to perform cleanup', { error });
        }
    }
    async getStats() {
        try {
            const db = mongoose_1.default.connection.db;
            if (!db) {
                throw new Error('Database connection not established');
            }
            const stats = await db.stats();
            return {
                collections: stats.collections,
                dataSize: stats.dataSize,
                indexSize: stats.indexSize,
                objects: stats.objects,
                avgObjSize: stats.avgObjSize,
            };
        }
        catch (error) {
            this.logger.error('[Database] Failed to get stats', { error });
            throw error;
        }
    }
    async healthCheck() {
        try {
            if (!this.isConnected()) {
                return false;
            }
            const db = mongoose_1.default.connection.db;
            await db?.admin().ping();
            return true;
        }
        catch (error) {
            this.logger.error('[Database] Health check failed', { error });
            return false;
        }
    }
}
exports.DatabaseService = DatabaseService;
