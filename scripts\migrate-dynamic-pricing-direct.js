/**
 * Direct MongoDB Migration for Dynamic Pricing
 * Uses direct MongoDB operations to add pricing fields to existing roles
 */

require('dotenv').config();
const { MongoClient } = require('mongodb');

async function migrateDynamicPricingDirect() {
    const client = new MongoClient(process.env.MONGODB_URI);
    
    try {
        console.log('🔄 Connecting to MongoDB...');
        await client.connect();
        console.log('✅ Connected to MongoDB');

        const db = client.db();
        const collection = db.collection('roleforsales');

        console.log('🔍 Checking existing roles...');
        
        // Find all roles that don't have priceType field
        const rolesNeedingMigration = await collection.find({
            priceType: { $exists: false }
        }).toArray();

        console.log(`📊 Found ${rolesNeedingMigration.length} roles needing migration`);

        if (rolesNeedingMigration.length === 0) {
            console.log('✅ All roles already have dynamic pricing fields');
            return;
        }

        // Show sample roles before migration
        console.log(`📋 Sample roles before migration:`);
        rolesNeedingMigration.slice(0, 3).forEach((role, index) => {
            console.log(`   ${index + 1}. ${role.name} (${role.roleType || 'unknown'})`);
            console.log(`      • Price: ${role.price}`);
            console.log(`      • Has priceType: ${role.priceType ? 'Yes' : 'No'}`);
        });

        // Update all existing roles to use fixed pricing
        const updateResult = await collection.updateMany(
            { priceType: { $exists: false } },
            { 
                $set: { 
                    priceType: 'fixed'
                    // Don't set percentageValue for fixed pricing roles
                }
            }
        );

        console.log(`✅ Updated ${updateResult.modifiedCount} roles to use fixed pricing`);

        // Verify the migration
        const verifyFixed = await collection.countDocuments({ priceType: 'fixed' });
        const verifyPercentage = await collection.countDocuments({ priceType: 'percentage' });
        const totalRoles = await collection.countDocuments();
        
        console.log(`📊 Migration Summary:`);
        console.log(`   • Total roles: ${totalRoles}`);
        console.log(`   • Fixed pricing roles: ${verifyFixed}`);
        console.log(`   • Percentage pricing roles: ${verifyPercentage}`);

        // Show sample roles after migration
        const sampleRolesAfter = await collection.find({}).limit(3).toArray();
        console.log(`📋 Sample roles after migration:`);
        sampleRolesAfter.forEach((role, index) => {
            console.log(`   ${index + 1}. ${role.name} (${role.roleType || 'unknown'})`);
            console.log(`      • Price: ${role.price}`);
            console.log(`      • Price Type: ${role.priceType}`);
            console.log(`      • Percentage Value: ${role.percentageValue || 'N/A'}`);
        });

        // Final validation
        const rolesStillMissing = await collection.countDocuments({
            priceType: { $exists: false }
        });

        if (rolesStillMissing > 0) {
            console.warn(`⚠️  Warning: ${rolesStillMissing} roles still missing pricing type`);
        } else {
            console.log('✅ All roles now have valid pricing types');
        }

        console.log('✅ Dynamic pricing migration completed successfully!');
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    } finally {
        await client.close();
        console.log('🔌 Disconnected from MongoDB');
        process.exit(0);
    }
}

async function validateMigrationDirect() {
    const client = new MongoClient(process.env.MONGODB_URI);
    
    try {
        await client.connect();
        const db = client.db();
        const collection = db.collection('roleforsales');
        
        const issues = [];
        
        // Check for roles without pricing type
        const noPricingType = await collection.countDocuments({
            priceType: { $exists: false }
        });
        
        if (noPricingType > 0) {
            issues.push(`${noPricingType} roles missing priceType field`);
        }
        
        // Check for percentage roles without percentageValue
        const percentageWithoutValue = await collection.countDocuments({
            priceType: 'percentage',
            percentageValue: { $exists: false }
        });
        
        if (percentageWithoutValue > 0) {
            issues.push(`${percentageWithoutValue} percentage roles missing percentageValue`);
        }
        
        // Check for invalid percentage values
        const invalidPercentage = await collection.countDocuments({
            priceType: 'percentage',
            $or: [
                { percentageValue: { $lt: 0.1 } },
                { percentageValue: { $gt: 50 } }
            ]
        });
        
        if (invalidPercentage > 0) {
            issues.push(`${invalidPercentage} roles with invalid percentage values (must be 0.1-50)`);
        }
        
        // Show current state
        const totalRoles = await collection.countDocuments();
        const fixedRoles = await collection.countDocuments({ priceType: 'fixed' });
        const percentageRoles = await collection.countDocuments({ priceType: 'percentage' });
        
        console.log(`📊 Current Database State:`);
        console.log(`   • Total roles: ${totalRoles}`);
        console.log(`   • Fixed pricing roles: ${fixedRoles}`);
        console.log(`   • Percentage pricing roles: ${percentageRoles}`);
        
        if (issues.length === 0) {
            console.log('✅ Migration validation passed - no issues found');
        } else {
            console.log('⚠️  Migration validation found issues:');
            issues.forEach(issue => console.log(`   • ${issue}`));
        }
        
        return issues.length === 0;
        
    } catch (error) {
        console.error('❌ Validation failed:', error);
        return false;
    } finally {
        await client.close();
    }
}

// Run the migration
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--validate')) {
        validateMigrationDirect();
    } else {
        migrateDynamicPricingDirect();
    }
}

module.exports = { migrateDynamicPricingDirect, validateMigrationDirect };
