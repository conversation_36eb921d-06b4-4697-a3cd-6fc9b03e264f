"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.hasBankerPermissions = hasBankerPermissions;
exports.validateBankerPermissions = validateBankerPermissions;
exports.getBankerRoleId = getBankerRoleId;
exports.setBankerRole = setBankerRole;
exports.validateBankerRole = validateBankerRole;
const discord_js_1 = require("discord.js");
const ServerConfiguration_1 = __importDefault(require("../../models/ServerConfiguration"));
const errorHandler_1 = require("../errorHandler");
async function hasBankerPermissions(member) {
    try {
        if (member.permissions.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            return true;
        }
        const serverConfig = await ServerConfiguration_1.default.findByGuildId(member.guild.id);
        if (!serverConfig?.bankerRoleId) {
            return false;
        }
        return member.roles.cache.has(serverConfig.bankerRoleId);
    }
    catch (error) {
        console.error('Error checking banker permissions:', error);
        return false;
    }
}
async function validateBankerPermissions(member) {
    const hasPermissions = await hasBankerPermissions(member);
    if (!hasPermissions) {
        throw new errorHandler_1.ValidationError('This command requires administrator permissions or the designated banker role.');
    }
}
async function getBankerRoleId(guildId) {
    try {
        const serverConfig = await ServerConfiguration_1.default.findByGuildId(guildId);
        return serverConfig?.bankerRoleId || null;
    }
    catch (error) {
        console.error('Error getting banker role ID:', error);
        return null;
    }
}
async function setBankerRole(guildId, roleId) {
    try {
        await ServerConfiguration_1.default.createOrUpdate(guildId, { bankerRoleId: roleId });
    }
    catch (error) {
        console.error('Error setting banker role:', error);
        throw new errorHandler_1.ValidationError('Failed to update banker role configuration.');
    }
}
async function validateBankerRole(member, roleId) {
    const guild = member.guild;
    const role = await guild.roles.fetch(roleId).catch(() => null);
    if (!role) {
        throw new errorHandler_1.ValidationError('The specified role does not exist in this server.');
    }
    if (role.id === guild.id) {
        throw new errorHandler_1.ValidationError('The @everyone role cannot be used as a banker role.');
    }
    const botMember = guild.members.me;
    if (botMember && role.position >= botMember.roles.highest.position && !botMember.permissions.has(discord_js_1.PermissionFlagsBits.Administrator)) {
        throw new errorHandler_1.ValidationError('The bot cannot manage this role due to role hierarchy. Please choose a role below the bot\'s highest role.');
    }
}
