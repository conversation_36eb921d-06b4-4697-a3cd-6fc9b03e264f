import { <PERSON>lashCommandBuilder, ChatInputCommandInteraction, GuildMember } from 'discord.js';
import { RoleForSale } from '../models/User';
import { withErrorHandler, ValidationError, DatabaseError, PermissionError } from '../utils/errorHandler';
import { createServerSuccessEmbed, createServerErrorEmbed, addUserInfo, formatServerCoins, EMOJIS } from '../utils/embedBuilder';
import { hasBankerPermissions } from '../utils/permissions/BankerPermissions';
import ConfigurableConstants from '../config/configurableConstants';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('removerolefromsale')
        .setDescription('Remove a role from the purchasable shop inventory (Admin/Banker only)')
        .addRoleOption(option =>
            option
                .setName('role')
                .setDescription('The role to remove from the shop')
                .setRequired(true)
        ),
    execute: withError<PERSON>andler(async (interaction: ChatInputCommandInteraction) => {
        const role = interaction.options.getRole('role', true);
        const guildId = interaction.guild?.id;

        // Basic validation
        if (!guildId) {
            throw new ValidationError('This command can only be used in a server.');
        }

        if (!interaction.guild) {
            throw new ValidationError('Guild information not available.');
        }

        if (!interaction.member || !(interaction.member instanceof GuildMember)) {
            throw new ValidationError('Member information not available.');
        }

        // Permission check - Admin or Banker role
        const hasPermissions = await hasBankerPermissions(interaction.member);
        if (!hasPermissions) {
            throw new PermissionError('This command requires administrator permissions or the designated banker role.');
        }

        try {
            // Ensure we have a full Role object (not APIRole)
            const fullRole = interaction.guild.roles.cache.get(role.id) || await interaction.guild.roles.fetch(role.id);
            if (!fullRole) {
                throw new ValidationError('Role not found in guild');
            }

            // Check if role is currently for sale (only shop_purchase roles can be removed from sale)
            const roleForSale = await RoleForSale.findOne({
                roleId: fullRole.id,
                guildId: guildId,
                roleType: 'shop_purchase'
            });

            if (!roleForSale) {
                // Role is not in the shop
                const embed = await createServerErrorEmbed(guildId, 'Role Not Found in Shop');
                embed.setDescription(
                    `${EMOJIS.ERROR.X} **${fullRole.name}** is not currently available for purchase in the role shop.\n\n` +
                    `Use \`/shop\` to see which roles are currently for sale.`
                );

                addUserInfo(embed, interaction.user);

                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }

            // Store role information before deletion
            const roleName = roleForSale.name;
            const rolePrice = roleForSale.price;
            const roleDescription = roleForSale.description;

            // Remove role from shop (only shop_purchase roles)
            await RoleForSale.findOneAndDelete({
                roleId: fullRole.id,
                guildId: guildId,
                roleType: 'shop_purchase'
            });

            // Create success embed
            const embed = await createServerSuccessEmbed(guildId, 'Role Removed from Shop!');
            const coinName = await ConfigurableConstants.getCoinName(guildId);
            const formattedPrice = await formatServerCoins(guildId, rolePrice);

            embed.setDescription(
                `${EMOJIS.SUCCESS.CHECK} **${roleName}** has been removed from the role shop!\n\n` +
                `${EMOJIS.ECONOMY.COINS} **Previous Price:** ${formattedPrice}\n` +
                `${EMOJIS.MISC.SCROLL} **Description:** ${roleDescription || 'None'}`
            );

            embed.addFields({
                name: `${EMOJIS.MISC.LIGHTBULB} What's Next?`,
                value: [
                    `• Members can no longer purchase this role from the shop`,
                    `• Use \`/setroleforsale\` to add it back to the shop`,
                    `• Use \`/shop\` to view the updated role shop`,
                    `• Existing role holders keep their roles`
                ].join('\n'),
                inline: false
            });

            // Add user info to embed
            addUserInfo(embed, interaction.user);

            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });

        } catch (error: unknown) {
            if (error instanceof ValidationError || error instanceof PermissionError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new DatabaseError(`Failed to remove role from shop: ${error.message}`);
            } else {
                throw new DatabaseError('Failed to remove role from shop.');
            }
        }
    })
};
