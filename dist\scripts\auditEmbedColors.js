"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.auditEmbedColors = main;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const issues = [];
const filesToCheck = [
    'src/utils/embedBuilder.ts',
    'src/services/automessageService.ts',
    'src/services/salary/SalaryService.ts',
    'src/services/suggestion/SuggestionService.ts',
    'src/utils/electionEmbedBuilder.ts',
    'src/commands',
    'src/services'
];
const patterns = [
    {
        pattern: /\.setColor\(['"`]#[0-9A-Fa-f]{6}['"`]\)/g,
        issue: 'Hardcoded hex color'
    },
    {
        pattern: /\.setColor\(['"`][a-zA-Z]+['"`]\)/g,
        issue: 'Hardcoded color name'
    },
    {
        pattern: /createSuccessEmbed\(/g,
        issue: 'Using non-server-specific success embed'
    },
    {
        pattern: /createErrorEmbed\(/g,
        issue: 'Using non-server-specific error embed'
    },
    {
        pattern: /createAdminEmbed\(/g,
        issue: 'Using non-server-specific admin embed'
    },
    {
        pattern: /createEconomyEmbed\(/g,
        issue: 'Using non-server-specific economy embed'
    },
    {
        pattern: /formatCoins\(/g,
        issue: 'Using non-server-specific coin formatting'
    },
    {
        pattern: /COLORS\.(PRIMARY|SUCCESS|ERROR|WARNING|INFO|GOLD)/g,
        issue: 'Using hardcoded color constant'
    }
];
function scanFile(filePath) {
    try {
        const content = fs_1.default.readFileSync(filePath, 'utf-8');
        const lines = content.split('\n');
        lines.forEach((line, index) => {
            patterns.forEach(({ pattern, issue }) => {
                const matches = line.match(pattern);
                if (matches) {
                    issues.push({
                        file: filePath,
                        line: index + 1,
                        issue,
                        code: line.trim()
                    });
                }
            });
        });
    }
    catch (error) {
        console.warn(`Could not read file: ${filePath}`);
    }
}
function scanDirectory(dirPath) {
    try {
        const items = fs_1.default.readdirSync(dirPath);
        items.forEach(item => {
            const fullPath = path_1.default.join(dirPath, item);
            const stat = fs_1.default.statSync(fullPath);
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            }
            else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
                scanFile(fullPath);
            }
        });
    }
    catch (error) {
        console.warn(`Could not read directory: ${dirPath}`);
    }
}
function main() {
    console.log('🔍 Auditing embed colors and server-specific usage...\n');
    filesToCheck.forEach(fileOrDir => {
        const fullPath = path_1.default.resolve(fileOrDir);
        try {
            const stat = fs_1.default.statSync(fullPath);
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            }
            else {
                scanFile(fullPath);
            }
        }
        catch (error) {
            console.warn(`Path not found: ${fileOrDir}`);
        }
    });
    const groupedIssues = issues.reduce((acc, issue) => {
        if (!acc[issue.issue]) {
            acc[issue.issue] = [];
        }
        acc[issue.issue].push(issue);
        return acc;
    }, {});
    console.log(`📊 Found ${issues.length} potential issues:\n`);
    Object.entries(groupedIssues).forEach(([issueType, issueList]) => {
        console.log(`🔸 ${issueType} (${issueList.length} occurrences):`);
        issueList.forEach(issue => {
            console.log(`   ${issue.file}:${issue.line} - ${issue.code}`);
        });
        console.log('');
    });
    console.log('💡 Recommendations:');
    console.log('1. Replace createSuccessEmbed with createServerSuccessEmbed');
    console.log('2. Replace createErrorEmbed with createServerErrorEmbed');
    console.log('3. Replace createAdminEmbed with createServerAdminEmbed');
    console.log('4. Replace createEconomyEmbed with createServerEconomyEmbed');
    console.log('5. Replace formatCoins with formatServerCoins');
    console.log('6. Replace hardcoded colors with server-specific colors');
    console.log('7. Add guildId parameter to embed creation functions');
    if (issues.length === 0) {
        console.log('✅ No issues found! All embeds appear to be using server-specific configurations.');
    }
}
if (require.main === module) {
    main();
}
