/**
 * Test Discord Embed and Component Limits
 * 
 * This script tests the Discord limits that might be causing the poll creation failure
 */

// Simulate the embed field content for 20 options
function simulateOptionsText(optionCount) {
  let optionsText = '';
  
  for (let i = 0; i < optionCount; i++) {
    const option = `Option ${i + 1}`;
    const bar = '░'.repeat(10); // Empty progress bar
    const votes = 0;
    const percentage = 0.0;
    const weight = 0;
    const weightPercentage = 0.0;
    
    optionsText += `**${i + 1}.** ${option}\n`;
    optionsText += `${bar} ${votes} votes (${percentage.toFixed(1)}%)\n`;
    optionsText += `💰 ${weight} coins (${weightPercentage.toFixed(1)}%)\n\n`;
  }
  
  return optionsText;
}

// Test embed field limits
function testEmbedLimits() {
  console.log('🔍 Testing Discord Embed Limits\n');
  
  const discordLimits = {
    embedFieldValue: 1024,
    embedTotal: 6000,
    embedTitle: 256,
    embedDescription: 4096
  };
  
  console.log('Discord Limits:');
  console.log(`- Embed field value: ${discordLimits.embedFieldValue} characters`);
  console.log(`- Total embed: ${discordLimits.embedTotal} characters`);
  console.log(`- Embed title: ${discordLimits.embedTitle} characters`);
  console.log(`- Embed description: ${discordLimits.embedDescription} characters\n`);
  
  // Test different option counts
  const testCases = [8, 15, 20];
  
  testCases.forEach(optionCount => {
    const optionsText = simulateOptionsText(optionCount);
    const length = optionsText.length;
    
    console.log(`${optionCount} options:`);
    console.log(`- Options field length: ${length} characters`);
    
    if (length > discordLimits.embedFieldValue) {
      console.log(`❌ EXCEEDS LIMIT by ${length - discordLimits.embedFieldValue} characters`);
    } else {
      console.log(`✅ Within limit (${discordLimits.embedFieldValue - length} characters remaining)`);
    }
    console.log();
  });
}

// Test component limits
function testComponentLimits() {
  console.log('🎛️ Testing Discord Component Limits\n');
  
  const discordLimits = {
    maxRows: 5,
    maxButtonsPerRow: 5,
    maxTotalButtons: 25,
    customIdLength: 100,
    buttonLabelLength: 80
  };
  
  console.log('Discord Component Limits:');
  console.log(`- Max action rows: ${discordLimits.maxRows}`);
  console.log(`- Max buttons per row: ${discordLimits.maxButtonsPerRow}`);
  console.log(`- Max total buttons: ${discordLimits.maxTotalButtons}`);
  console.log(`- Custom ID length: ${discordLimits.customIdLength} characters`);
  console.log(`- Button label length: ${discordLimits.buttonLabelLength} characters\n`);
  
  // Test our 20-option layout
  const optionCount = 20;
  const buttonsPerRow = 5;
  const maxVotingRows = 4;
  const endButtonRows = 1;
  
  const totalRows = maxVotingRows + endButtonRows;
  const totalButtons = optionCount + 1; // +1 for end button
  
  console.log('Our 20-option layout:');
  console.log(`- Voting buttons: ${optionCount}`);
  console.log(`- Voting rows: ${maxVotingRows}`);
  console.log(`- End button rows: ${endButtonRows}`);
  console.log(`- Total rows: ${totalRows}`);
  console.log(`- Total buttons: ${totalButtons}\n`);
  
  // Check limits
  const issues = [];
  
  if (totalRows > discordLimits.maxRows) {
    issues.push(`❌ Too many rows: ${totalRows} > ${discordLimits.maxRows}`);
  } else {
    console.log(`✅ Rows within limit: ${totalRows}/${discordLimits.maxRows}`);
  }
  
  if (totalButtons > discordLimits.maxTotalButtons) {
    issues.push(`❌ Too many buttons: ${totalButtons} > ${discordLimits.maxTotalButtons}`);
  } else {
    console.log(`✅ Buttons within limit: ${totalButtons}/${discordLimits.maxTotalButtons}`);
  }
  
  // Test custom ID length
  const samplePollId = '7c32de61-9d2b-4a90-ba54-3652d1961f2a';
  const sampleCustomId = `poll_vote_${samplePollId}_19`;
  
  if (sampleCustomId.length > discordLimits.customIdLength) {
    issues.push(`❌ Custom ID too long: ${sampleCustomId.length} > ${discordLimits.customIdLength}`);
  } else {
    console.log(`✅ Custom ID within limit: ${sampleCustomId.length}/${discordLimits.customIdLength}`);
  }
  
  // Test button label length
  const sampleLabel = '20. This is a very long option name that might exceed limits...';
  const truncatedLabel = `20. ${sampleLabel.substring(0, 18)}${sampleLabel.length > 18 ? '...' : ''}`;
  
  if (truncatedLabel.length > discordLimits.buttonLabelLength) {
    issues.push(`❌ Button label too long: ${truncatedLabel.length} > ${discordLimits.buttonLabelLength}`);
  } else {
    console.log(`✅ Button label within limit: ${truncatedLabel.length}/${discordLimits.buttonLabelLength}`);
  }
  
  if (issues.length > 0) {
    console.log('\n❌ Issues found:');
    issues.forEach(issue => console.log(issue));
  } else {
    console.log('\n✅ All component limits satisfied');
  }
}

// Calculate exact character usage for 20 options
function calculateExactUsage() {
  console.log('📊 Calculating Exact Character Usage for 20 Options\n');
  
  // Simulate realistic option names
  const options = [
    'Increase server activity events',
    'Add new gaming channels',
    'Implement weekly contests',
    'Create study groups',
    'Add music bot features',
    'Organize movie nights',
    'Set up coding challenges',
    'Add art showcase channel',
    'Create book club',
    'Add fitness tracking',
    'Implement mentorship program',
    'Add language exchange',
    'Create investment discussions',
    'Add tech news channel',
    'Organize virtual meetups',
    'Add recipe sharing',
    'Create pet photos channel',
    'Add travel stories',
    'Implement skill sharing',
    'Add meditation sessions'
  ];
  
  let optionsText = '';
  options.forEach((option, index) => {
    const bar = '░'.repeat(10);
    optionsText += `**${index + 1}.** ${option}\n`;
    optionsText += `${bar} 0 votes (0.0%)\n`;
    optionsText += `💰 0 coins (0.0%)\n\n`;
  });
  
  console.log(`Realistic 20-option field length: ${optionsText.length} characters`);
  console.log(`Discord limit: 1024 characters`);
  
  if (optionsText.length > 1024) {
    console.log(`❌ EXCEEDS LIMIT by ${optionsText.length - 1024} characters`);
    console.log('\nThis is likely the cause of the Discord API error!');
  } else {
    console.log(`✅ Within limit (${1024 - optionsText.length} characters remaining)`);
  }
}

// Main execution
console.log('🚀 Discord Limits Analysis for 20-Option Polls\n');
console.log('='.repeat(60));

testEmbedLimits();
console.log('='.repeat(60));
testComponentLimits();
console.log('='.repeat(60));
calculateExactUsage();

console.log('\n' + '='.repeat(60));
console.log('🎯 Conclusion: The issue is likely the embed field value limit!');
