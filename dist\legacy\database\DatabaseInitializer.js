"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyDatabaseInitializer = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
class LegacyDatabaseInitializer {
    static async initialize() {
        try {
            await mongoose_1.default.connect(process.env.MONGODB_URI);
            console.log('Connected to MongoDB');
            await this.initializeDatabase();
        }
        catch (error) {
            console.error('MongoDB connection error:', error);
            throw error;
        }
    }
    static async initializeDatabase() {
        try {
            console.log('Initializing database...');
            const db = mongoose_1.default.connection.db;
            if (!db) {
                throw new Error('Database connection not established');
            }
            const usersCollection = db.collection('users');
            const indexes = await usersCollection.indexes();
            console.log('Existing indexes:', indexes.map(idx => idx.name));
            try {
                await usersCollection.dropIndex('userId_1');
                console.log('Dropped old userId_1 index');
            }
            catch (error) {
                console.log('userId_1 index not found (this is expected)');
            }
            const deleteResult = await usersCollection.deleteMany({
                $or: [
                    { discordId: null },
                    { discordId: { $exists: false } },
                    { userId: { $exists: true } }
                ]
            });
            if (deleteResult.deletedCount > 0) {
                console.log(`Cleaned up ${deleteResult.deletedCount} corrupted user records`);
            }
            await usersCollection.createIndex({ discordId: 1 }, { unique: true });
            console.log('Ensured discordId index exists');
            console.log('Database initialization complete');
        }
        catch (error) {
            console.error('Database initialization error:', error);
            throw error;
        }
    }
    static getConnectionStatus() {
        const states = ['disconnected', 'connected', 'connecting', 'disconnecting'];
        return states[mongoose_1.default.connection.readyState] || 'unknown';
    }
    static async close() {
        try {
            await mongoose_1.default.disconnect();
            console.log('Database connection closed');
        }
        catch (error) {
            console.error('Error closing database connection:', error);
            throw error;
        }
    }
}
exports.LegacyDatabaseInitializer = LegacyDatabaseInitializer;
exports.default = LegacyDatabaseInitializer;
