"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLogCommand = void 0;
const discord_js_1 = require("discord.js");
const BaseCommand_1 = require("../base/BaseCommand");
const embedBuilder_1 = require("../../utils/embedBuilder");
const errorHandler_1 = require("../../utils/errorHandler");
const auditChannelService_1 = __importDefault(require("../../services/auditChannelService"));
class AuditLogCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'auditlog',
            description: 'Configure audit logging for economy transactions (admin only)',
            category: BaseCommand_1.CommandCategory.ADMIN,
            adminOnly: true,
            requiredFeatures: ['ECONOMY_SYSTEM'],
            requiredPermissions: ['Administrator'],
        });
    }
    customizeCommand(command) {
        command
            .addSubcommand(subcommand => subcommand
            .setName('setup')
            .setDescription('Set the audit logging channel for economy transactions')
            .addChannelOption(option => option.setName('channel')
            .setDescription('The channel where audit logs will be sent')
            .setRequired(true)
            .addChannelTypes(discord_js_1.ChannelType.GuildText)))
            .addSubcommand(subcommand => subcommand
            .setName('disable')
            .setDescription('Disable audit logging for this server'))
            .addSubcommand(subcommand => subcommand
            .setName('status')
            .setDescription('View current audit logging configuration'));
    }
    async executeCommand(context) {
        const { interaction } = context;
        const subcommand = interaction.options.getSubcommand();
        switch (subcommand) {
            case 'setup':
                await this.handleSetupAuditChannel(context);
                break;
            case 'disable':
                await this.handleDisableAuditLogging(context);
                break;
            case 'status':
                await this.handleViewAuditStatus(context);
                break;
            default:
                throw new errorHandler_1.ValidationError('Invalid subcommand');
        }
    }
    async handleSetupAuditChannel(context) {
        const { interaction } = context;
        const channelOption = interaction.options.getChannel('channel', true);
        const guildId = interaction.guild.id;
        try {
            const validation = await auditChannelService_1.default.validateAuditChannel(interaction.guild, channelOption.id);
            if (!validation.valid) {
                const embed = await (0, embedBuilder_1.createServerErrorEmbed)(guildId, 'Invalid Audit Channel');
                embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Channel Validation Failed**\n\n` +
                    `${validation.error}\n\n` +
                    `${embedBuilder_1.EMOJIS.ADMIN.INFO} **Required Permissions:**\n` +
                    `• View Channel\n` +
                    `• Send Messages\n` +
                    `• Embed Links`);
                (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            await auditChannelService_1.default.setAuditChannel(guildId, channelOption.id);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Audit Logging Configured Successfully!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Audit Channel Set**\n\n` +
                `Economy transaction audit logs will now be sent to ${channelOption}.\n\n` +
                `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **Logged Transactions:**\n` +
                `• \`/give\` - Administrative coin awards\n` +
                `• \`/fine\` - Administrative fines\n` +
                `• \`/pay\` - User-to-user payments\n\n` +
                `${embedBuilder_1.EMOJIS.ADMIN.INFO} All audit logs include transaction details, timestamps, and user information.`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.HAMMER} Administrator`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.TAG} Audit Channel`,
                value: `${channelOption}`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.SUCCESS.PARTY} Status`,
                value: `**Active**`,
                inline: true
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            this.logger.info(`Admin ${interaction.user.username} set audit channel to ${channelOption.name}`, {
                adminId: interaction.user.id,
                channelId: channelOption.id,
                channelName: channelOption.name,
                guildId: interaction.guild?.id,
            });
        }
        catch (error) {
            this.logger.error('Error setting audit channel', {
                error,
                adminId: interaction.user.id,
                channelId: channelOption.id,
                channelName: channelOption.name
            });
            throw error;
        }
    }
    async handleDisableAuditLogging(context) {
        const { interaction } = context;
        const guildId = interaction.guild.id;
        try {
            const currentChannelId = await auditChannelService_1.default.getAuditChannelId(guildId);
            let currentChannelName = 'None';
            if (currentChannelId) {
                const currentChannel = await interaction.guild.channels.fetch(currentChannelId).catch(() => null);
                currentChannelName = currentChannel?.name || 'Unknown Channel';
            }
            await auditChannelService_1.default.disableAuditLogging(guildId);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Audit Logging Disabled Successfully!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Audit Logging Disabled**\n\n` +
                `Economy transaction audit logs will no longer be sent to any channel.\n\n` +
                `${embedBuilder_1.EMOJIS.ADMIN.INFO} **Note:** You can re-enable audit logging at any time using \`/auditlog setup\`.`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.HAMMER} Administrator`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.TAG} Previous Channel`,
                value: `**${currentChannelName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ADMIN.SCALES} Status`,
                value: `**Disabled**`,
                inline: true
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            this.logger.info(`Admin ${interaction.user.username} disabled audit logging`, {
                adminId: interaction.user.id,
                previousChannelId: currentChannelId,
                previousChannelName: currentChannelName,
                guildId: interaction.guild?.id,
            });
        }
        catch (error) {
            this.logger.error('Error disabling audit logging', {
                error,
                adminId: interaction.user.id
            });
            throw error;
        }
    }
    async handleViewAuditStatus(context) {
        const { interaction } = context;
        const guildId = interaction.guild.id;
        try {
            const currentChannelId = await auditChannelService_1.default.getAuditChannelId(guildId);
            let embed;
            if (currentChannelId) {
                const currentChannel = await interaction.guild.channels.fetch(currentChannelId).catch(() => null);
                if (currentChannel) {
                    const validation = await auditChannelService_1.default.validateAuditChannel(interaction.guild, currentChannelId);
                    embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Audit Logging Status');
                    if (validation.valid) {
                        embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Audit Logging Active**\n\n` +
                            `Economy transaction logs are being sent to ${currentChannel}.\n\n` +
                            `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **Monitored Transactions:**\n` +
                            `• Administrative coin awards (\`/give\`)\n` +
                            `• Administrative fines (\`/fine\`)\n` +
                            `• User payments (\`/pay\`)`);
                    }
                    else {
                        embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Audit Logging Configuration Issue**\n\n` +
                            `Audit channel is set to ${currentChannel}, but there's a problem:\n` +
                            `**${validation.error}**\n\n` +
                            `${embedBuilder_1.EMOJIS.ADMIN.INFO} Please fix the permissions or use \`/auditlog setup\` to choose a different channel.`);
                    }
                    embed.addFields({
                        name: `${embedBuilder_1.EMOJIS.MISC.TAG} Audit Channel`,
                        value: `${currentChannel}`,
                        inline: true
                    }, {
                        name: `${embedBuilder_1.EMOJIS.SUCCESS.PARTY} Status`,
                        value: validation.valid ? `**Active**` : `**Issue Detected**`,
                        inline: true
                    }, {
                        name: `${embedBuilder_1.EMOJIS.MISC.ID} Channel ID`,
                        value: `\`${currentChannel.id}\``,
                        inline: true
                    });
                }
                else {
                    embed = await (0, embedBuilder_1.createServerErrorEmbed)(guildId, 'Audit Channel Configuration Issue');
                    embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Channel Not Found**\n\n` +
                        `An audit channel is configured but the channel no longer exists.\n` +
                        `Please use \`/auditlog disable\` to clear the configuration or \`/auditlog setup\` to set a new channel.`);
                }
            }
            else {
                embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Audit Logging Status');
                embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.SCALES} **Audit Logging Disabled**\n\n` +
                    `No audit channel is currently configured for this server.\n\n` +
                    `${embedBuilder_1.EMOJIS.ADMIN.INFO} Use \`/auditlog setup #channel\` to enable audit logging.`);
            }
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });
        }
        catch (error) {
            this.logger.error('Error viewing audit status', {
                error,
                adminId: interaction.user.id
            });
            throw error;
        }
    }
}
exports.AuditLogCommand = AuditLogCommand;
