"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserCleanupService = void 0;
const User_1 = __importDefault(require("../models/User"));
const Transaction_1 = __importDefault(require("../models/Transaction"));
const ReactionReward_1 = require("../models/ReactionReward");
const errorHandler_1 = require("../utils/errorHandler");
const mongoose_1 = __importDefault(require("mongoose"));
class UserCleanupService {
    static async cleanupUserData(member) {
        const startTime = Date.now();
        const result = {
            success: false,
            userDataRemoved: false,
            transactionsRemoved: 0,
            reactionRewardsRemoved: 0,
            errors: [],
            timeTaken: 0
        };
        const userId = member.user?.id;
        const guildId = member.guild?.id;
        const guildName = member.guild?.name || 'Unknown Guild';
        const userName = member.displayName || member.user?.username || 'Unknown User';
        if (!userId) {
            const errorMsg = 'No user ID available for cleanup';
            result.errors.push(errorMsg);
            console.error(`[User Cleanup] ${errorMsg}`);
            result.timeTaken = Date.now() - startTime;
            return result;
        }
        if (!guildId) {
            const errorMsg = 'No guild ID available for cleanup';
            result.errors.push(errorMsg);
            console.error(`[User Cleanup] ${errorMsg}`);
            result.timeTaken = Date.now() - startTime;
            return result;
        }
        console.log(`[User Cleanup] Starting guild-scoped cleanup for user ${userName} (${userId}) who left ${guildName} (${guildId})`);
        try {
            await new Promise(resolve => setTimeout(resolve, this.GRACE_PERIOD_MS));
            if (member.guild) {
                const stillInGuild = member.guild.members.cache.has(userId);
                if (stillInGuild) {
                    console.log(`[User Cleanup] User ${userName} rejoined during grace period, skipping cleanup`);
                    result.success = true;
                    result.timeTaken = Date.now() - startTime;
                    return result;
                }
            }
            if (mongoose_1.default.connection.readyState !== 1) {
                const errorMsg = 'Database is not connected. Please try again in a moment.';
                result.errors.push(errorMsg);
                console.error(`[User Cleanup] ${errorMsg}`);
                result.timeTaken = Date.now() - startTime;
                return result;
            }
            const session = await mongoose_1.default.startSession();
            try {
                await session.withTransaction(async () => {
                    const userDeleteResult = await User_1.default.deleteOne({
                        discordId: userId,
                        guildId: guildId
                    }).session(session);
                    result.userDataRemoved = userDeleteResult.deletedCount > 0;
                    if (result.userDataRemoved) {
                        console.log(`[User Cleanup] Removed user balance record for ${userName} in guild ${guildName}`);
                    }
                    const transactionDeleteResult = await Transaction_1.default.deleteMany({
                        discordId: userId,
                        guildId: guildId
                    }).session(session);
                    result.transactionsRemoved = transactionDeleteResult.deletedCount;
                    if (result.transactionsRemoved > 0) {
                        console.log(`[User Cleanup] Removed ${result.transactionsRemoved} transaction records for ${userName} in guild ${guildName}`);
                    }
                    const reactionRewardDeleteResult = await ReactionReward_1.ReactionReward.deleteMany({ userId }).session(session);
                    result.reactionRewardsRemoved = reactionRewardDeleteResult.deletedCount;
                    if (result.reactionRewardsRemoved > 0) {
                        console.log(`[User Cleanup] Removed ${result.reactionRewardsRemoved} reaction reward records for ${userName}`);
                    }
                    const totalRecordsRemoved = (result.userDataRemoved ? 1 : 0) + result.transactionsRemoved + result.reactionRewardsRemoved;
                    console.log(`[User Cleanup] Successfully removed ${totalRecordsRemoved} total records for ${userName} from ${guildName}`);
                });
                result.success = true;
            }
            catch (error) {
                const errorMsg = `Database transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
                result.errors.push(errorMsg);
                console.error(`[User Cleanup] ${errorMsg}`);
                throw error;
            }
            finally {
                await session.endSession();
            }
        }
        catch (error) {
            const errorMsg = `Cleanup failed for user ${userName}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            result.errors.push(errorMsg);
            console.error(`[User Cleanup] ${errorMsg}`);
            result.success = false;
        }
        result.timeTaken = Date.now() - startTime;
        if (result.success) {
            console.log(`[User Cleanup] Completed cleanup for ${userName} in ${result.timeTaken}ms`);
        }
        else {
            console.error(`[User Cleanup] Failed cleanup for ${userName} after ${result.timeTaken}ms:`, result.errors);
        }
        return result;
    }
    static async checkUserData(userId, guildId) {
        try {
            const [userRecord, transactionCount, reactionRewardCount] = await Promise.all([
                User_1.default.findOne({ discordId: userId, guildId: guildId }),
                Transaction_1.default.countDocuments({ discordId: userId, guildId: guildId }),
                ReactionReward_1.ReactionReward.countDocuments({ userId })
            ]);
            return {
                hasUserRecord: !!userRecord,
                transactionCount,
                reactionRewardCount
            };
        }
        catch (error) {
            console.error(`[User Cleanup] Failed to check user data for ${userId}:`, error);
            throw new errorHandler_1.DatabaseError(`Failed to check user data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    static async cleanupOrphanedData() {
        console.log('[User Cleanup] Starting orphaned data cleanup...');
        try {
            console.log('[User Cleanup] Orphaned data cleanup not yet implemented - requires guild membership verification');
            return {
                orphanedUsers: 0,
                orphanedTransactions: 0,
                orphanedReactionRewards: 0
            };
        }
        catch (error) {
            console.error('[User Cleanup] Orphaned data cleanup failed:', error);
            throw new errorHandler_1.DatabaseError(`Orphaned data cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
exports.UserCleanupService = UserCleanupService;
UserCleanupService.CLEANUP_TIMEOUT_MS = 30000;
UserCleanupService.GRACE_PERIOD_MS = 5000;
