"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReadyEventHandler = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const base_1 = require("./base");
const constants_1 = require("../config/constants");
class ReadyEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'ready');
        this.name = 'ready';
        this.once = true;
    }
    async execute() {
        try {
            this.logger.info(`[Ready] Bot logged in as ${this.app.client.user?.tag}`);
            await this.initializeScheduledTasks();
            await this.initializeTradeBackgroundService();
            this.logger.info('[Ready] Bot is ready and all systems initialized');
        }
        catch (error) {
            this.handleError(error, { event: 'ready' });
        }
    }
    async initializeScheduledTasks() {
        try {
            if (this.isFeatureEnabled('TAX_SYSTEM')) {
                this.initializeTaxCollection();
            }
            if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
                this.initializeMilestoneTracking();
            }
            if (this.isFeatureEnabled('USER_CLEANUP')) {
                this.initializeUserCleanup();
            }
            if (this.isFeatureEnabled('AUDIT_LOGGING')) {
                this.initializeAuditCleanup();
            }
            if (this.isFeatureEnabled('SALARY_SYSTEM')) {
                this.initializeSalaryDistribution();
            }
            if (this.isFeatureEnabled('COIN_DECAY_SYSTEM')) {
                this.initializeCoinDecay();
            }
            this.logger.info('[Ready] Scheduled tasks initialized');
        }
        catch (error) {
            this.logger.error('[Ready] Failed to initialize scheduled tasks', { error });
        }
    }
    initializeTaxCollection() {
        node_cron_1.default.schedule(constants_1.SCHEDULES.TAX_COLLECTION, async () => {
            this.logger.info('[Tax Collection] Running scheduled tax collection check...');
            try {
                const { processTaxCollection } = await Promise.resolve().then(() => __importStar(require('../services/taxService')));
                for (const [guildId, guild] of this.app.client.guilds.cache) {
                    try {
                        const result = await processTaxCollection(this.app.client, guildId);
                        if (result.totalProcessed > 0) {
                            this.logger.info(`[Tax Collection] Guild ${guild.name}: Processed ${result.totalProcessed}, Taxed ${result.totalTaxed}, Roles Removed ${result.totalRolesRemoved}`);
                            if (result.errors.length > 0) {
                                this.logger.error(`[Tax Collection] Guild ${guild.name} errors:`, result.errors);
                            }
                        }
                    }
                    catch (error) {
                        this.logger.error(`[Tax Collection] Failed for guild ${guild.name}:`, error);
                    }
                }
            }
            catch (error) {
                this.logger.error('[Tax Collection] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.logger.info('[Tax Collection] Cron job initialized - running every hour');
    }
    initializeMilestoneTracking() {
        node_cron_1.default.schedule(constants_1.SCHEDULES.MILESTONE_RESET, async () => {
            this.logger.info('[Milestone Tracking] Running daily milestone check...');
            try {
                for (const [guildId, guild] of this.app.client.guilds.cache) {
                    try {
                        this.logger.debug(`[Milestone Tracking] Daily reset processed for guild ${guild.name}`);
                    }
                    catch (error) {
                        this.logger.error(`[Milestone Tracking] Failed for guild ${guild.name}:`, error);
                    }
                }
            }
            catch (error) {
                this.logger.error('[Milestone Tracking] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.logger.info('[Milestone Tracking] Daily cron job initialized - running at midnight UTC');
    }
    initializeUserCleanup() {
        node_cron_1.default.schedule(constants_1.SCHEDULES.USER_CLEANUP, async () => {
            this.logger.info('[User Cleanup] Running scheduled cleanup check...');
            try {
                const { UserCleanupService } = await Promise.resolve().then(() => __importStar(require('../services/userCleanupService')));
                const stats = await UserCleanupService.cleanupOrphanedData();
                this.logger.info('[User Cleanup] Orphaned data cleanup completed', stats);
            }
            catch (error) {
                this.logger.error('[User Cleanup] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.logger.info('[User Cleanup] Cron job initialized - running daily at 2 AM UTC');
    }
    initializeAuditCleanup() {
        node_cron_1.default.schedule(constants_1.SCHEDULES.AUDIT_CLEANUP, async () => {
            this.logger.info('[Audit Cleanup] Running scheduled audit cleanup...');
            try {
                const { MilestoneAuditService } = await Promise.resolve().then(() => __importStar(require('../services/milestoneAuditService')));
                const cutoffDate = new Date();
                cutoffDate.setDate(cutoffDate.getDate() - 90);
                const cleanupStats = await MilestoneAuditService.cleanupOldLogs(cutoffDate.getTime());
                this.logger.info('[Audit Cleanup] Cleanup completed', cleanupStats);
            }
            catch (error) {
                this.logger.error('[Audit Cleanup] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.logger.info('[Audit Cleanup] Cron job initialized - running weekly on Sunday at 3 AM UTC');
    }
    initializeSalaryDistribution() {
        node_cron_1.default.schedule(constants_1.SCHEDULES.SALARY_DAILY, async () => {
            this.logger.info('[Salary Distribution] Running daily salary distribution...');
            try {
                const { SalaryService } = await Promise.resolve().then(() => __importStar(require('../services/salary/SalaryService')));
                const { SalaryFrequency } = await Promise.resolve().then(() => __importStar(require('../models/RoleSalary')));
                const salaryService = this.app.getService('SalaryService');
                if (!salaryService) {
                    this.logger.error('[Salary Distribution] SalaryService not available');
                    return;
                }
                const result = await salaryService.processSalaryDistribution(this.app.client, SalaryFrequency.DAILY);
                if (result.totalProcessed > 0) {
                    this.logger.info('[Salary Distribution] Daily distribution completed', {
                        totalProcessed: result.totalProcessed,
                        totalPaid: result.totalPaid,
                        totalAmount: result.totalAmount,
                        errorCount: result.errors.length
                    });
                    if (result.errors.length > 0) {
                        this.logger.warn('[Salary Distribution] Daily distribution had errors', {
                            errors: result.errors.slice(0, 5)
                        });
                    }
                }
            }
            catch (error) {
                this.logger.error('[Salary Distribution] Daily cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        node_cron_1.default.schedule(constants_1.SCHEDULES.SALARY_WEEKLY, async () => {
            this.logger.info('[Salary Distribution] Running weekly salary distribution...');
            try {
                const { SalaryService } = await Promise.resolve().then(() => __importStar(require('../services/salary/SalaryService')));
                const { SalaryFrequency } = await Promise.resolve().then(() => __importStar(require('../models/RoleSalary')));
                const salaryService = this.app.getService('SalaryService');
                if (!salaryService) {
                    this.logger.error('[Salary Distribution] SalaryService not available');
                    return;
                }
                const result = await salaryService.processSalaryDistribution(this.app.client, SalaryFrequency.WEEKLY);
                if (result.totalProcessed > 0) {
                    this.logger.info('[Salary Distribution] Weekly distribution completed', {
                        totalProcessed: result.totalProcessed,
                        totalPaid: result.totalPaid,
                        totalAmount: result.totalAmount,
                        errorCount: result.errors.length
                    });
                    if (result.errors.length > 0) {
                        this.logger.warn('[Salary Distribution] Weekly distribution had errors', {
                            errors: result.errors.slice(0, 5)
                        });
                    }
                }
            }
            catch (error) {
                this.logger.error('[Salary Distribution] Weekly cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.logger.info('[Salary Distribution] Cron jobs initialized - daily at 1 AM UTC, weekly on Sunday at 1 AM UTC');
    }
    initializeCoinDecay() {
        node_cron_1.default.schedule(constants_1.SCHEDULES.COIN_DECAY, async () => {
            this.logger.info('[Coin Decay] Running scheduled coin decay processing...');
            try {
                const { processAllCoinDecay } = await Promise.resolve().then(() => __importStar(require('../services/coinDecayService')));
                const results = await processAllCoinDecay(this.app.client);
                this.logger.info('[Coin Decay] Processing completed', {
                    processedGuilds: results.processedGuilds,
                    totalUsersProcessed: results.totalUsersProcessed,
                    totalDecayAmount: results.totalDecayAmount,
                    errorCount: results.errors.length
                });
                if (results.errors.length > 0) {
                    this.logger.warn('[Coin Decay] Errors occurred during processing', {
                        errors: results.errors
                    });
                }
            }
            catch (error) {
                this.logger.error('[Coin Decay] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.logger.info('[Coin Decay] Cron job initialized - running daily at 3 AM UTC');
    }
    async initializeTradeBackgroundService() {
        try {
            if (this.isFeatureEnabled('TRADE_SYSTEM')) {
                const tradeBackgroundService = this.app.getService('TradeBackgroundService');
                if (tradeBackgroundService && typeof tradeBackgroundService.setClient === 'function') {
                    tradeBackgroundService.setClient(this.app.client);
                    this.logger.info('[Ready] Trade background service initialized with Discord client');
                }
            }
        }
        catch (error) {
            this.logger.error('[Ready] Failed to initialize trade background service', { error });
        }
    }
}
exports.ReadyEventHandler = ReadyEventHandler;
