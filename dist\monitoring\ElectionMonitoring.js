"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElectionMonitoringService = void 0;
const events_1 = require("events");
const models_1 = require("../models");
class ElectionMonitoringService extends events_1.EventEmitter {
    constructor(logger) {
        super();
        this.logger = logger;
        this.metrics = new Map();
        this.alerts = [];
        this.alertConfigs = new Map();
        this.lastAlertTime = new Map();
        this.setupDefaultAlerts();
        this.startMonitoring();
    }
    setupDefaultAlerts() {
        this.alertConfigs.set('query_response_time', {
            threshold: 5000,
            condition: 'gt',
            timeWindow: 300000,
            cooldown: 600000
        });
        this.alertConfigs.set('concurrent_votes', {
            threshold: 100,
            condition: 'gt',
            timeWindow: 60000,
            cooldown: 300000
        });
        this.alertConfigs.set('failed_operations', {
            threshold: 10,
            condition: 'gt',
            timeWindow: 300000,
            cooldown: 600000
        });
        this.alertConfigs.set('memory_usage', {
            threshold: 85,
            condition: 'gt',
            timeWindow: 60000,
            cooldown: 300000
        });
        this.alertConfigs.set('active_elections', {
            threshold: 50,
            condition: 'gt',
            timeWindow: 3600000,
            cooldown: 3600000
        });
    }
    startMonitoring() {
        this.monitoringInterval = setInterval(async () => {
            await this.collectSystemMetrics();
            await this.collectElectionMetrics();
            await this.checkAlerts();
            this.cleanupOldMetrics();
        }, 30000);
        this.logger.info('[ElectionMonitoring] Monitoring started');
    }
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = undefined;
        }
        this.logger.info('[ElectionMonitoring] Monitoring stopped');
    }
    recordMetric(name, value, metadata) {
        if (!this.metrics.has(name)) {
            this.metrics.set(name, []);
        }
        const metrics = this.metrics.get(name);
        metrics.push({
            timestamp: Date.now(),
            value,
            metadata
        });
        if (metrics.length > 1000) {
            metrics.splice(0, metrics.length - 1000);
        }
        this.emit('metric_recorded', { name, value, metadata });
    }
    recordOperationTime(operation, duration, electionId) {
        this.recordMetric(`operation_time_${operation}`, duration, { electionId });
        this.recordMetric('operation_time_all', duration, { operation, electionId });
    }
    recordElectionEvent(eventType, electionId, metadata) {
        this.recordMetric(`event_${eventType}`, 1, { electionId, ...metadata });
        this.recordMetric('events_total', 1, { eventType, electionId, ...metadata });
        this.emit('election_event', { eventType, electionId, metadata });
    }
    async collectSystemMetrics() {
        try {
            const memUsage = process.memoryUsage();
            this.recordMetric('memory_heap_used', memUsage.heapUsed);
            this.recordMetric('memory_heap_total', memUsage.heapTotal);
            this.recordMetric('memory_external', memUsage.external);
            this.recordMetric('memory_rss', memUsage.rss);
            const memPercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
            this.recordMetric('memory_usage', memPercent);
            const start = process.hrtime.bigint();
            setImmediate(() => {
                const lag = Number(process.hrtime.bigint() - start) / 1000000;
                this.recordMetric('event_loop_lag', lag);
            });
        }
        catch (error) {
            this.logger.error('[ElectionMonitoring] Failed to collect system metrics', { error });
        }
    }
    async collectElectionMetrics() {
        try {
            const startTime = Date.now();
            const electionCounts = await models_1.Election.aggregate([
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 }
                    }
                }
            ]);
            electionCounts.forEach(({ _id: status, count }) => {
                this.recordMetric(`elections_${status.toLowerCase()}`, count);
            });
            const totalActive = electionCounts
                .filter(({ _id }) => _id !== 'ENDED' && _id !== 'CANCELLED')
                .reduce((sum, { count }) => sum + count, 0);
            this.recordMetric('active_elections', totalActive);
            const [candidateCount, voteCount, snapshotCount] = await Promise.all([
                models_1.ElectionCandidate.countDocuments({ withdrawn: false, disqualified: false }),
                models_1.ElectionBallot.countDocuments({ replaced: false }),
                models_1.SnapshotBalance.countDocuments({})
            ]);
            this.recordMetric('active_candidates', candidateCount);
            this.recordMetric('total_votes', voteCount);
            this.recordMetric('snapshot_records', snapshotCount);
            const queryTime = Date.now() - startTime;
            this.recordMetric('query_response_time', queryTime);
            const now = new Date();
            const upcomingEnds = await models_1.Election.countDocuments({
                status: { $in: ['NOMINATING', 'VOTING'] },
                $or: [
                    { nominationEnd: { $lte: new Date(now.getTime() + 3600000) } },
                    { votingEnd: { $lte: new Date(now.getTime() + 3600000) } }
                ]
            });
            this.recordMetric('elections_ending_soon', upcomingEnds);
        }
        catch (error) {
            this.logger.error('[ElectionMonitoring] Failed to collect election metrics', { error });
            this.recordMetric('failed_operations', 1, { operation: 'collect_metrics' });
        }
    }
    async checkAlerts() {
        const now = Date.now();
        for (const [metricName, config] of this.alertConfigs) {
            try {
                const lastAlert = this.lastAlertTime.get(metricName) || 0;
                if (now - lastAlert < config.cooldown) {
                    continue;
                }
                const metrics = this.metrics.get(metricName);
                if (!metrics || metrics.length === 0) {
                    continue;
                }
                const windowStart = now - config.timeWindow;
                const recentMetrics = metrics.filter(m => m.timestamp >= windowStart);
                if (recentMetrics.length === 0) {
                    continue;
                }
                const avgValue = recentMetrics.reduce((sum, m) => sum + m.value, 0) / recentMetrics.length;
                let alertTriggered = false;
                switch (config.condition) {
                    case 'gt':
                        alertTriggered = avgValue > config.threshold;
                        break;
                    case 'lt':
                        alertTriggered = avgValue < config.threshold;
                        break;
                    case 'eq':
                        alertTriggered = avgValue === config.threshold;
                        break;
                }
                if (alertTriggered) {
                    await this.triggerAlert(metricName, avgValue, config, recentMetrics);
                }
            }
            catch (error) {
                this.logger.error('[ElectionMonitoring] Failed to check alerts', { error, metricName });
            }
        }
    }
    async triggerAlert(metricName, value, config, recentMetrics) {
        const alertId = `${metricName}_${Date.now()}`;
        const severity = this.determineSeverity(metricName, value, config.threshold);
        const alert = {
            id: alertId,
            metric: metricName,
            message: this.generateAlertMessage(metricName, value, config),
            severity,
            timestamp: Date.now(),
            metadata: {
                threshold: config.threshold,
                condition: config.condition,
                recentValues: recentMetrics.slice(-5).map(m => m.value)
            }
        };
        this.alerts.push(alert);
        this.lastAlertTime.set(metricName, Date.now());
        if (this.alerts.length > 100) {
            this.alerts.splice(0, this.alerts.length - 100);
        }
        this.logger.warn('[ElectionMonitoring] Alert triggered', alert);
        this.emit('alert', alert);
        if (severity === 'critical') {
            this.emit('critical_alert', alert);
            await this.handleCriticalAlert(alert);
        }
    }
    determineSeverity(metricName, value, threshold) {
        const ratio = value / threshold;
        if (metricName.includes('memory') || metricName.includes('failed')) {
            if (ratio >= 2)
                return 'critical';
            if (ratio >= 1.5)
                return 'high';
            if (ratio >= 1.2)
                return 'medium';
            return 'low';
        }
        if (metricName.includes('query_response_time')) {
            if (ratio >= 3)
                return 'critical';
            if (ratio >= 2)
                return 'high';
            if (ratio >= 1.5)
                return 'medium';
            return 'low';
        }
        if (ratio >= 2)
            return 'high';
        if (ratio >= 1.5)
            return 'medium';
        return 'low';
    }
    generateAlertMessage(metricName, value, config) {
        const condition = config.condition === 'gt' ? 'exceeded' :
            config.condition === 'lt' ? 'below' : 'equals';
        const formattedValue = metricName.includes('time') ? `${Math.round(value)}ms` :
            metricName.includes('memory') ? `${Math.round(value)}%` :
                Math.round(value).toString();
        return `${metricName} ${condition} threshold: ${formattedValue} (threshold: ${config.threshold})`;
    }
    async handleCriticalAlert(alert) {
        this.logger.error('[ElectionMonitoring] CRITICAL ALERT', alert);
        switch (alert.metric) {
            case 'memory_usage':
                if (global.gc) {
                    global.gc();
                    this.logger.info('[ElectionMonitoring] Forced garbage collection due to high memory usage');
                }
                break;
            case 'failed_operations':
                this.emit('emergency_mode', { reason: 'high_failure_rate', alert });
                break;
            case 'query_response_time':
                this.emit('database_performance_issue', { alert });
                break;
        }
    }
    cleanupOldMetrics() {
        const cutoffTime = Date.now() - (24 * 60 * 60 * 1000);
        for (const [metricName, metrics] of this.metrics) {
            const filteredMetrics = metrics.filter(m => m.timestamp > cutoffTime);
            this.metrics.set(metricName, filteredMetrics);
        }
        const cutoffTimeAlerts = Date.now() - (7 * 24 * 60 * 60 * 1000);
        this.alerts = this.alerts.filter(a => a.timestamp > cutoffTimeAlerts);
    }
    getMetricHistory(metricName, timeWindow) {
        const metrics = this.metrics.get(metricName) || [];
        if (!timeWindow) {
            return metrics;
        }
        const cutoffTime = Date.now() - timeWindow;
        return metrics.filter(m => m.timestamp >= cutoffTime);
    }
    getCurrentMetric(metricName) {
        const metrics = this.metrics.get(metricName);
        if (!metrics || metrics.length === 0) {
            return null;
        }
        return metrics[metrics.length - 1].value;
    }
    getRecentAlerts(limit = 50) {
        return this.alerts
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, limit);
    }
    getHealthSummary() {
        const now = Date.now();
        const oneHourAgo = now - 3600000;
        return {
            status: this.getOverallHealthStatus(),
            metrics: {
                memory_usage: this.getCurrentMetric('memory_usage'),
                active_elections: this.getCurrentMetric('active_elections'),
                total_votes: this.getCurrentMetric('total_votes'),
                query_response_time: this.getCurrentMetric('query_response_time'),
                event_loop_lag: this.getCurrentMetric('event_loop_lag')
            },
            alerts: {
                total: this.alerts.length,
                critical: this.alerts.filter(a => a.severity === 'critical').length,
                recent: this.alerts.filter(a => a.timestamp >= oneHourAgo).length
            },
            uptime: process.uptime(),
            timestamp: now
        };
    }
    getOverallHealthStatus() {
        const recentAlerts = this.alerts.filter(a => a.timestamp >= Date.now() - 3600000);
        const criticalAlerts = recentAlerts.filter(a => a.severity === 'critical');
        const highAlerts = recentAlerts.filter(a => a.severity === 'high');
        if (criticalAlerts.length > 0) {
            return 'critical';
        }
        if (highAlerts.length > 2 || recentAlerts.length > 5) {
            return 'warning';
        }
        return 'healthy';
    }
    generateReport(timeWindow = 3600000) {
        const endTime = Date.now();
        const startTime = endTime - timeWindow;
        const report = {
            timeRange: {
                start: new Date(startTime).toISOString(),
                end: new Date(endTime).toISOString(),
                duration: timeWindow
            },
            metrics: {},
            alerts: this.alerts.filter(a => a.timestamp >= startTime),
            health: this.getHealthSummary()
        };
        for (const [metricName, metrics] of this.metrics) {
            const relevantMetrics = metrics.filter(m => m.timestamp >= startTime);
            if (relevantMetrics.length > 0) {
                const values = relevantMetrics.map(m => m.value);
                report.metrics[metricName] = {
                    count: values.length,
                    min: Math.min(...values),
                    max: Math.max(...values),
                    avg: values.reduce((sum, v) => sum + v, 0) / values.length,
                    latest: values[values.length - 1]
                };
            }
        }
        return report;
    }
}
exports.ElectionMonitoringService = ElectionMonitoringService;
