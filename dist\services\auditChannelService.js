"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditChannelService = void 0;
const discord_js_1 = require("discord.js");
const serverConfigurationService_1 = __importDefault(require("./serverConfigurationService"));
const embedBuilder_1 = require("../utils/embedBuilder");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
class AuditChannelService {
    static async setAuditChannel(guildId, channelId) {
        await serverConfigurationService_1.default.setAuditChannelId(guildId, channelId);
    }
    static async disableAuditLogging(guildId) {
        await serverConfigurationService_1.default.setAuditChannelId(guildId, null);
    }
    static async getAuditChannelId(guildId) {
        return await serverConfigurationService_1.default.getAuditChannelId(guildId);
    }
    static async validateAuditChannel(guild, channelId) {
        try {
            const channel = await guild.channels.fetch(channelId);
            if (!channel) {
                return { valid: false, error: 'Channel not found in this server.' };
            }
            if (!channel.isTextBased()) {
                return { valid: false, error: 'Channel must be a text channel.' };
            }
            const textChannel = channel;
            const botMember = guild.members.me;
            if (!botMember) {
                return { valid: false, error: 'Bot member not found in server.' };
            }
            const permissions = textChannel.permissionsFor(botMember);
            if (!permissions) {
                return { valid: false, error: 'Unable to check channel permissions.' };
            }
            const requiredPermissions = [
                discord_js_1.PermissionFlagsBits.SendMessages,
                discord_js_1.PermissionFlagsBits.EmbedLinks,
                discord_js_1.PermissionFlagsBits.ViewChannel
            ];
            const missingPermissions = requiredPermissions.filter(perm => !permissions.has(perm));
            if (missingPermissions.length > 0) {
                const permissionNames = missingPermissions.map(perm => {
                    switch (perm) {
                        case discord_js_1.PermissionFlagsBits.SendMessages: return 'Send Messages';
                        case discord_js_1.PermissionFlagsBits.EmbedLinks: return 'Embed Links';
                        case discord_js_1.PermissionFlagsBits.ViewChannel: return 'View Channel';
                        default: return 'Unknown Permission';
                    }
                });
                return {
                    valid: false,
                    error: `Bot is missing required permissions in this channel: ${permissionNames.join(', ')}`
                };
            }
            return { valid: true };
        }
        catch (error) {
            console.error('[AuditChannelService] Error validating channel:', error);
            return { valid: false, error: 'Failed to validate channel. Please try again.' };
        }
    }
    static async logTransaction(client, entry) {
        try {
            const auditChannelId = await this.getAuditChannelId(entry.guildId);
            if (!auditChannelId) {
                return;
            }
            const guild = client.guilds.cache.get(entry.guildId);
            if (!guild) {
                console.warn(`[AuditChannelService] Guild not found: ${entry.guildId}`);
                return;
            }
            const channel = await guild.channels.fetch(auditChannelId).catch(() => null);
            if (!channel || !channel.isTextBased()) {
                console.warn(`[AuditChannelService] Audit channel not found or not text-based: ${auditChannelId}`);
                await this.disableAuditLogging(entry.guildId);
                return;
            }
            const textChannel = channel;
            const validation = await this.validateAuditChannel(guild, auditChannelId);
            if (!validation.valid) {
                console.warn(`[AuditChannelService] Invalid audit channel permissions: ${validation.error}`);
                return;
            }
            const embed = await this.createAuditEmbed(entry);
            await textChannel.send({ embeds: [embed] });
            console.log(`[AuditChannelService] Audit log sent for ${entry.type} transaction: ${entry.executor.username} -> ${entry.target.username} (${entry.amount})`);
        }
        catch (error) {
            console.error('[AuditChannelService] Failed to send audit log:', error);
        }
    }
    static async createAuditEmbed(entry) {
        const { type, executor, target, amount, reason, guildId, timestamp } = entry;
        const formattedAmount = await (0, embedBuilder_1.formatServerCoins)(guildId, Math.abs(amount));
        const constants = await configurableConstants_1.default.getAllServerConstants(guildId);
        let title;
        let emoji;
        let description;
        switch (type) {
            case 'give':
                title = 'Administrative Coin Award';
                emoji = embedBuilder_1.EMOJIS.ADMIN.HAMMER;
                description = `${emoji} **Administrator awarded coins to user**\n\n` +
                    `${formattedAmount} has been awarded to **${target.displayName}**`;
                break;
            case 'fine':
                title = 'Administrative Fine Issued';
                emoji = embedBuilder_1.EMOJIS.ADMIN.SCALES;
                description = `${emoji} **Administrator fined user**\n\n` +
                    `${formattedAmount} has been deducted from **${target.displayName}**`;
                break;
            case 'pay':
                title = 'User Payment Transaction';
                emoji = embedBuilder_1.EMOJIS.ECONOMY.COINS;
                description = `${emoji} **User-to-user payment completed**\n\n` +
                    `**${executor.displayName}** paid ${formattedAmount} to **${target.displayName}**`;
                break;
            default:
                title = 'Economy Transaction';
                emoji = embedBuilder_1.EMOJIS.ECONOMY.COINS;
                description = `${emoji} **Transaction completed**`;
        }
        const embed = await (0, embedBuilder_1.createServerBaseEmbed)(guildId, title, description);
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.ACTIONS.SENDER} ${type === 'pay' ? 'Sender' : 'Administrator'}`,
            value: `${executor} (${executor.id})`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.ACTIONS.TARGET} ${type === 'pay' ? 'Recipient' : 'Target User'}`,
            value: `${target} (${target.id})`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
            value: formattedAmount,
            inline: true
        });
        if (reason) {
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Reason`,
                value: reason,
                inline: false
            });
        }
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Transaction Time`,
            value: `<t:${Math.floor(timestamp.getTime() / 1000)}:F>`,
            inline: false
        });
        embed.setFooter({ text: `${constants.nationName} Economy Audit Log` });
        return embed;
    }
}
exports.AuditChannelService = AuditChannelService;
exports.default = AuditChannelService;
