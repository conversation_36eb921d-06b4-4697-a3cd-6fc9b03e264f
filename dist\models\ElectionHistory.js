"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const electionHistorySchema = new mongoose_1.Schema({
    originalElectionId: {
        type: String,
        required: [true, 'Original election ID is required'],
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    title: {
        type: String,
        required: [true, 'Election title is required'],
        maxlength: [100, 'Election title cannot exceed 100 characters'],
        trim: true
    },
    description: {
        type: String,
        maxlength: [500, 'Election description cannot exceed 500 characters'],
        trim: true
    },
    createdBy: {
        type: String,
        required: [true, 'Creator ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Creator ID must be a valid Discord snowflake'
        },
        index: true
    },
    winnerId: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Winner ID must be a valid Discord snowflake'
        },
        index: true
    },
    winnerDisplayName: {
        type: String,
        maxlength: [100, 'Winner display name cannot exceed 100 characters'],
        trim: true
    },
    winnerVoteWeight: {
        type: Number,
        default: 0,
        min: [0, 'Winner vote weight cannot be negative']
    },
    winnerPercentage: {
        type: Number,
        default: 0,
        min: [0, 'Winner percentage cannot be negative'],
        max: [100, 'Winner percentage cannot exceed 100']
    },
    totalVotes: {
        type: Number,
        required: [true, 'Total votes is required'],
        min: [0, 'Total votes cannot be negative']
    },
    totalVoteWeight: {
        type: Number,
        required: [true, 'Total vote weight is required'],
        min: [0, 'Total vote weight cannot be negative']
    },
    totalCandidates: {
        type: Number,
        required: [true, 'Total candidates is required'],
        min: [0, 'Total candidates cannot be negative']
    },
    participationRate: {
        type: Number,
        min: [0, 'Participation rate cannot be negative'],
        max: [100, 'Participation rate cannot exceed 100']
    },
    electionStarted: {
        type: Date,
        required: [true, 'Election start date is required'],
        index: true
    },
    electionEnded: {
        type: Date,
        required: [true, 'Election end date is required'],
        index: true
    },
    archivedAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    rolesToPing: [{
            type: String,
            validate: {
                validator: function (v) {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }],
    eligibleVoterRoles: [{
            type: String,
            validate: {
                validator: function (v) {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }],
    eligibleCandidateRoles: [{
            type: String,
            validate: {
                validator: function (v) {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }],
    allowedMultipleVotes: {
        type: Boolean,
        default: true
    },
    showedVoteWeights: {
        type: Boolean,
        default: true
    },
    endedBy: {
        type: String,
        required: [true, 'Ended by is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Ended by must be a valid Discord snowflake'
        }
    },
    duration: {
        type: Number,
        required: [true, 'Duration is required'],
        min: [0, 'Duration cannot be negative']
    }
}, {
    timestamps: false
});
electionHistorySchema.index({ guildId: 1, archivedAt: -1 });
electionHistorySchema.index({ createdBy: 1, archivedAt: -1 });
electionHistorySchema.index({ winnerId: 1, archivedAt: -1 });
electionHistorySchema.index({ guildId: 1, electionStarted: -1 });
exports.default = (0, mongoose_1.model)('ElectionHistory', electionHistorySchema);
