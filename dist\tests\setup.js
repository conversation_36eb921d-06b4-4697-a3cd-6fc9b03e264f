"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testUtils = void 0;
require("dotenv/config");
const globals_1 = require("@jest/globals");
globals_1.jest.setTimeout(30000);
globals_1.jest.mock('discord.js', () => ({
    Client: globals_1.jest.fn(),
    Guild: globals_1.jest.fn(),
    TextChannel: globals_1.jest.fn(),
    GuildMember: globals_1.jest.fn(),
    User: globals_1.jest.fn(),
    Message: globals_1.jest.fn(),
    EmbedBuilder: globals_1.jest.fn().mockImplementation(() => ({
        setColor: globals_1.jest.fn().mockReturnThis(),
        setTitle: globals_1.jest.fn().mockReturnThis(),
        setDescription: globals_1.jest.fn().mockReturnThis(),
        addFields: globals_1.jest.fn().mockReturnThis(),
        setFooter: globals_1.jest.fn().mockReturnThis(),
        setTimestamp: globals_1.jest.fn().mockReturnThis()
    })),
    ActionRowBuilder: globals_1.jest.fn().mockImplementation(() => ({
        addComponents: globals_1.jest.fn().mockReturnThis()
    })),
    ButtonBuilder: globals_1.jest.fn().mockImplementation(() => ({
        setCustomId: globals_1.jest.fn().mockReturnThis(),
        setLabel: globals_1.jest.fn().mockReturnThis(),
        setEmoji: globals_1.jest.fn().mockReturnThis(),
        setStyle: globals_1.jest.fn().mockReturnThis()
    })),
    ButtonStyle: {
        Primary: 1,
        Secondary: 2,
        Success: 3,
        Danger: 4
    },
    ChannelType: {
        GuildText: 0
    },
    Collection: Map,
    PermissionFlagsBits: {
        Administrator: BigInt(8)
    },
    SlashCommandBuilder: globals_1.jest.fn().mockImplementation(() => ({
        setName: globals_1.jest.fn().mockReturnThis(),
        setDescription: globals_1.jest.fn().mockReturnThis(),
        addSubcommand: globals_1.jest.fn().mockReturnThis(),
        addStringOption: globals_1.jest.fn().mockReturnThis(),
        addChannelOption: globals_1.jest.fn().mockReturnThis(),
        addRoleOption: globals_1.jest.fn().mockReturnThis(),
        setDefaultMemberPermissions: globals_1.jest.fn().mockReturnThis()
    }))
}));
globals_1.jest.mock('crypto', () => ({
    randomUUID: globals_1.jest.fn(() => 'test-uuid-1234-5678-9012')
}));
process.env.NODE_ENV = 'test';
process.env.MONGODB_TEST_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/suggestion-test';
process.env.LOG_LEVEL = 'error';
exports.testUtils = {
    createMockGuild: (id = 'test-guild-123') => ({
        id,
        name: 'Test Guild'
    }),
    createMockChannel: (id = 'test-channel-123') => ({
        id,
        type: 0,
        send: globals_1.jest.fn(),
        messages: {
            fetch: globals_1.jest.fn()
        }
    }),
    createMockMember: (id = 'test-user-123', roleIds = []) => ({
        id,
        roles: {
            cache: new Map(roleIds.map(roleId => [roleId, { id: roleId }]))
        }
    }),
    createMockClient: () => ({
        channels: {
            fetch: globals_1.jest.fn()
        },
        users: {
            cache: new Map([
                ['test-user-123', {
                        id: 'test-user-123',
                        username: 'TestUser',
                        displayAvatarURL: () => 'https://example.com/avatar.png'
                    }]
            ])
        },
        user: {
            tag: 'TestBot#1234'
        }
    }),
    wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),
    generateTestSuggestion: (overrides = {}) => ({
        suggestionId: 'test-suggestion-123',
        guildId: 'test-guild-123',
        channelId: 'test-channel-123',
        messageId: 'test-message-123',
        authorId: 'test-user-123',
        content: 'This is a test suggestion',
        votes: [],
        upvoteCount: 0,
        downvoteCount: 0,
        isEdited: false,
        editHistory: [],
        ...overrides
    })
};
const originalConsole = global.console;
global.console = {
    ...originalConsole,
    log: globals_1.jest.fn(),
    info: globals_1.jest.fn(),
    warn: globals_1.jest.fn(),
    error: originalConsole.error
};
global.afterEach(() => {
    globals_1.jest.clearAllMocks();
});
