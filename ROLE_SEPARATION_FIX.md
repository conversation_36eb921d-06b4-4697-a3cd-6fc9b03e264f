# Role System Separation Fix

## 🔍 **Problem Identified**
The `/shop` command was displaying balance-threshold roles (meant for automatic unlocking) alongside purchasable shop roles, causing user confusion and system conflicts.

## 🛠️ **Root Cause**
Both role systems were using the same `RoleForSale` database collection without any differentiation:
- `/addrole` (balance-threshold roles) → `RoleForSale` collection
- `/setroleforsale` (shop purchase roles) → Same `RoleForSale` collection

## ✅ **Solution Implemented**

### 1. **Database Schema Enhancement**
- Added `roleType` field to `RoleForSale` model with two values:
  - `'balance_threshold'` - Automatic unlock based on balance
  - `'shop_purchase'` - Manual purchase through shop

### 2. **Command Updates**

#### **Balance-Threshold System** (`roleType: 'balance_threshold'`)
- `/addrole` - Creates balance-threshold roles
- `/editrole` - Edits balance-threshold roles only
- `/removerole` - Removes balance-threshold roles only
- `/roles` - Displays balance-threshold roles only
- Role assignment service - Only processes balance-threshold roles

#### **Shop Purchase System** (`roleType: 'shop_purchase'`)
- `/setroleforsale` - Creates/updates shop purchase roles
- `/removerolefromsale` - Removes shop purchase roles only
- `/shop` - Displays shop purchase roles only

### 3. **Database Migration**
- Migrated 15 existing roles to `'balance_threshold'` type
- All existing roles were originally created via `/addrole` command

## 🧪 **Testing Instructions**

### **Test 1: Role Separation**
1. Use `/roles` command - Should only show balance-threshold roles
2. Use `/shop` command - Should only show shop purchase roles (likely empty initially)

### **Test 2: Balance-Threshold Roles**
1. Use `/addrole @role 1000 "Test achievement"` 
2. Check `/roles` - Should appear in achievements list
3. Check `/shop` - Should NOT appear in shop

### **Test 3: Shop Purchase Roles**
1. Use `/setroleforsale @role 500 "Test shop role"`
2. Check `/shop` - Should appear in shop
3. Check `/roles` - Should NOT appear in achievements

### **Test 4: Command Restrictions**
1. Try `/editrole` on a shop purchase role - Should fail
2. Try `/removerolefromsale` on a balance-threshold role - Should fail

## 📊 **Migration Results**
- ✅ 15 existing roles migrated to `balance_threshold` type
- ✅ Database schema updated with `roleType` field
- ✅ All commands updated with proper filtering
- ✅ TypeScript compiled successfully
- ✅ Discord commands deployed successfully

## 🔄 **Deployment Status**
- [x] TypeScript compilation (`npm run build`)
- [x] Command deployment (`npm run deploy-commands`)
- [x] Database migration completed
- [ ] Bot restart (required for changes to take effect)

## 🚨 **Next Steps**
1. **Restart the bot process** to load the new compiled code
2. Test the commands in Discord to verify separation
3. Monitor for any issues with existing role assignments

## 📝 **Files Modified**
- `src/models/User.ts` - Added roleType field
- `src/commands/addrole.ts` - Set roleType to 'balance_threshold'
- `src/commands/setroleforsale.ts` - Set roleType to 'shop_purchase'
- `src/commands/shop.ts` - Filter by 'shop_purchase'
- `src/commands/roles.ts` - Filter by 'balance_threshold'
- `src/commands/editrole.ts` - Filter by 'balance_threshold'
- `src/commands/removerole.ts` - Filter by 'balance_threshold'
- `src/commands/removerolefromsale.ts` - Filter by 'shop_purchase'
- `src/services/roleAssignmentService.ts` - Filter by 'balance_threshold'
- `src/services/role/RoleService.ts` - Filter by 'balance_threshold'
- `scripts/migrate-role-types.js` - Migration script

The fix ensures complete separation between automatic balance-threshold roles and manual shop purchase roles, resolving the database conflict and user interface confusion.
