"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SalaryRemoveCommand = void 0;
const discord_js_1 = require("discord.js");
const BaseCommand_1 = require("../base/BaseCommand");
const RoleSalary_1 = require("../../models/RoleSalary");
const embedBuilder_1 = require("../../utils/embedBuilder");
class SalaryRemoveCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'salaryremove',
            description: 'Remove salary configuration for a role (admin only)',
            category: BaseCommand_1.CommandCategory.ADMIN,
            adminOnly: true,
            requiredFeatures: ['SALARY_SYSTEM'],
            requiredPermissions: ['Administrator'],
        });
    }
    customizeCommand(command) {
        command
            .addRoleOption(option => option.setName('role')
            .setDescription('The role to remove salary configuration from')
            .setRequired(true));
    }
    async executeCommand(context) {
        const { interaction } = context;
        const role = interaction.options.getRole('role', true);
        try {
            const existingConfig = await RoleSalary_1.RoleSalary.findOne({
                roleId: role.id,
                guildId: interaction.guild.id
            });
            if (!existingConfig) {
                const embed = (0, embedBuilder_1.createErrorEmbed)('No Salary Configuration Found', `${embedBuilder_1.EMOJIS.ADMIN.WARNING} The role **${role.name}** does not have a salary configuration.`);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            const confirmEmbed = (0, embedBuilder_1.createWarningEmbed)('Confirm Salary Removal', `${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Are you sure you want to remove the salary configuration?**\n\n` +
                `This will permanently delete the salary setup for **${role.name}**.`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} Role`,
                value: `**${role.name}**\n\`${role.id}\``,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Current Amount`,
                value: await (0, embedBuilder_1.formatServerCoins)(interaction.guild.id, existingConfig.amount),
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Current Frequency`,
                value: `**${existingConfig.frequency.charAt(0).toUpperCase() + existingConfig.frequency.slice(1)}**`,
                inline: true
            });
            (0, embedBuilder_1.addUserInfo)(confirmEmbed, interaction.user);
            const confirmId = `salary_remove_confirm_${role.id}_${Date.now()}`;
            const cancelId = `salary_remove_cancel_${role.id}_${Date.now()}`;
            const actionRow = (0, embedBuilder_1.createConfirmationButtons)(confirmId, cancelId);
            const response = await interaction.reply({
                embeds: [confirmEmbed],
                components: [actionRow],
                ephemeral: false
            });
            try {
                const buttonInteraction = await response.awaitMessageComponent({
                    componentType: discord_js_1.ComponentType.Button,
                    time: 30000,
                    filter: (i) => i.user.id === interaction.user.id && (i.customId === confirmId || i.customId === cancelId)
                });
                if (buttonInteraction.customId === confirmId) {
                    const result = await RoleSalary_1.RoleSalary.deleteOne({
                        roleId: role.id,
                        guildId: interaction.guild.id
                    });
                    const removed = result.deletedCount > 0;
                    if (removed) {
                        const guildId = interaction.guild.id;
                        const successEmbed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Salary Configuration Removed!');
                        successEmbed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Successfully removed salary configuration**\n\n` +
                            `The salary setup for **${role.name}** has been permanently deleted.`)
                            .addFields({
                            name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} What This Means`,
                            value: `• No more automatic salary payments for this role\n` +
                                `• All existing salary data has been cleaned up\n` +
                                `• Members can still keep their current balances`,
                            inline: false
                        });
                        (0, embedBuilder_1.addUserInfo)(successEmbed, interaction.user);
                        await buttonInteraction.update({
                            embeds: [successEmbed],
                            components: []
                        });
                        this.logger.info(`Admin ${interaction.user.username} removed salary configuration for role ${role.name}`, {
                            adminId: interaction.user.id,
                            roleId: role.id,
                            roleName: role.name,
                            guildId: interaction.guild?.id,
                            previousAmount: existingConfig.amount,
                            previousFrequency: existingConfig.frequency
                        });
                    }
                    else {
                        const errorEmbed = (0, embedBuilder_1.createErrorEmbed)('Removal Failed', `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Failed to remove salary configuration. It may have already been deleted.`);
                        await buttonInteraction.update({
                            embeds: [errorEmbed],
                            components: []
                        });
                    }
                }
                else {
                    const cancelEmbed = (0, embedBuilder_1.createWarningEmbed)('Removal Cancelled', `${embedBuilder_1.EMOJIS.ADMIN.INFO} Salary configuration removal has been cancelled.\n\n` +
                        `The salary setup for **${role.name}** remains unchanged.`);
                    await buttonInteraction.update({
                        embeds: [cancelEmbed],
                        components: []
                    });
                }
            }
            catch (error) {
                const timeoutEmbed = (0, embedBuilder_1.createErrorEmbed)('Confirmation Timeout', `${embedBuilder_1.EMOJIS.MISC.CLOCK} The confirmation has timed out.\n\n` +
                    `The salary configuration for **${role.name}** remains unchanged.`);
                await interaction.editReply({
                    embeds: [timeoutEmbed],
                    components: []
                });
            }
        }
        catch (error) {
            this.logger.error('Error executing salary remove command', {
                error,
                adminId: interaction.user.id,
                roleId: role.id,
                roleName: role.name
            });
            throw error;
        }
    }
}
exports.SalaryRemoveCommand = SalaryRemoveCommand;
