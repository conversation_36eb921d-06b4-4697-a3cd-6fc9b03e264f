"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isGuildSpecificFeature = exports.requiresAdminPermission = exports.isFeatureActive = exports.featureManager = exports.FEATURE_REGISTRY = void 0;
exports.requireFeature = requireFeature;
const environment_1 = require("./environment");
const constants_1 = require("./constants");
exports.FEATURE_REGISTRY = {
    ECONOMY_SYSTEM: {
        enabled: constants_1.FEATURES.ECONOMY_SYSTEM,
        description: 'Core economy system with configurable currency',
        dependencies: [],
    },
    MILESTONE_SYSTEM: {
        enabled: constants_1.FEATURES.MILESTONE_SYSTEM && (environment_1.ENV.ENABLE_MILESTONE_SYSTEM ?? true),
        description: 'Automated milestone rewards for user activities',
        dependencies: ['ECONOMY_SYSTEM'],
    },
    DYNASTY_SYSTEM: {
        enabled: constants_1.FEATURES.DYNASTY_SYSTEM && (environment_1.ENV.ENABLE_DYNASTY_SYSTEM ?? false),
        description: 'Dynasty groups with shared progression and benefits',
        dependencies: ['ECONOMY_SYSTEM', 'MILESTONE_SYSTEM'],
    },
    REACTION_REWARDS: {
        enabled: constants_1.FEATURES.REACTION_REWARDS && (environment_1.ENV.ENABLE_REACTION_REWARDS ?? true),
        description: 'Coin rewards for message reactions in monetized channels',
        dependencies: ['ECONOMY_SYSTEM'],
    },
    TAX_SYSTEM: {
        enabled: constants_1.FEATURES.TAX_SYSTEM && (environment_1.ENV.ENABLE_TAX_SYSTEM ?? true),
        description: 'Automated taxation system for role maintenance',
        dependencies: ['ECONOMY_SYSTEM'],
        adminOnly: true,
    },
    COIN_DECAY_SYSTEM: {
        enabled: constants_1.FEATURES.COIN_DECAY_SYSTEM,
        description: 'Automated coin decay for inactive users',
        dependencies: ['ECONOMY_SYSTEM'],
        adminOnly: true,
    },
    STARTER_BALANCE: {
        enabled: constants_1.FEATURES.STARTER_BALANCE,
        description: 'Automatic starter balance for new role assignments',
        dependencies: ['ECONOMY_SYSTEM'],
        adminOnly: true,
    },
    AUTO_MESSAGES: {
        enabled: constants_1.FEATURES.AUTO_MESSAGES,
        description: 'Automated messages for server events',
        dependencies: [],
        adminOnly: true,
        guildSpecific: true,
    },
    ROLE_AUTOMATION: {
        enabled: constants_1.FEATURES.ROLE_AUTOMATION,
        description: 'Automatic role assignment based on PLC balance',
        dependencies: ['ECONOMY_SYSTEM'],
    },
    USER_CLEANUP: {
        enabled: constants_1.FEATURES.USER_CLEANUP,
        description: 'Automatic cleanup of user data when members leave',
        dependencies: [],
        adminOnly: true,
    },
    AUDIT_LOGGING: {
        enabled: constants_1.FEATURES.AUDIT_LOGGING,
        description: 'Comprehensive audit logging for admin actions',
        dependencies: [],
        adminOnly: true,
    },
    TRADE_SYSTEM: {
        enabled: constants_1.FEATURES.TRADE_SYSTEM && (environment_1.ENV.ENABLE_TRADE_SYSTEM ?? true),
        description: 'Secure peer-to-peer trading system with escrow protection',
        dependencies: ['ECONOMY_SYSTEM'],
    },
    SALARY_SYSTEM: {
        enabled: constants_1.FEATURES.SALARY_SYSTEM && (environment_1.ENV.ENABLE_SALARY_SYSTEM ?? true),
        description: 'Automated role-based salary distribution system',
        dependencies: ['ECONOMY_SYSTEM'],
        adminOnly: true,
    },
};
class FeatureDependencyResolver {
    constructor() {
        this.resolved = new Set();
        this.resolving = new Set();
    }
    resolve() {
        const enabledFeatures = new Set();
        for (const [featureName, config] of Object.entries(exports.FEATURE_REGISTRY)) {
            if (this.isFeatureEnabled(featureName)) {
                enabledFeatures.add(featureName);
            }
        }
        return enabledFeatures;
    }
    isFeatureEnabled(featureName) {
        if (this.resolved.has(featureName)) {
            return true;
        }
        if (this.resolving.has(featureName)) {
            throw new Error(`Circular dependency detected for feature: ${featureName}`);
        }
        const config = exports.FEATURE_REGISTRY[featureName];
        if (!config) {
            return false;
        }
        if (!config.enabled) {
            return false;
        }
        this.resolving.add(featureName);
        if (config.dependencies) {
            for (const dependency of config.dependencies) {
                if (!this.isFeatureEnabled(dependency)) {
                    this.resolving.delete(featureName);
                    return false;
                }
            }
        }
        this.resolving.delete(featureName);
        this.resolved.add(featureName);
        return true;
    }
}
class FeatureManager {
    constructor() {
        const resolver = new FeatureDependencyResolver();
        this.enabledFeatures = resolver.resolve();
        console.log('[Feature Manager] Enabled features:', Array.from(this.enabledFeatures).join(', '));
    }
    isEnabled(featureName) {
        return this.enabledFeatures.has(featureName);
    }
    getEnabledFeatures() {
        return Array.from(this.enabledFeatures);
    }
    getFeatureConfig(featureName) {
        return exports.FEATURE_REGISTRY[featureName];
    }
    requiresAdmin(featureName) {
        const config = this.getFeatureConfig(featureName);
        return config?.adminOnly ?? false;
    }
    isGuildSpecific(featureName) {
        const config = this.getFeatureConfig(featureName);
        return config?.guildSpecific ?? false;
    }
    getFeaturesByCategory(category) {
        const coreFeatures = ['ECONOMY_SYSTEM', 'REACTION_REWARDS', 'ROLE_AUTOMATION'];
        const adminFeatures = ['TAX_SYSTEM', 'STARTER_BALANCE', 'AUTO_MESSAGES', 'USER_CLEANUP', 'AUDIT_LOGGING'];
        const advancedFeatures = ['MILESTONE_SYSTEM', 'DYNASTY_SYSTEM'];
        const categoryMap = {
            core: coreFeatures,
            admin: adminFeatures,
            advanced: advancedFeatures,
        };
        return categoryMap[category].filter(feature => this.isEnabled(feature));
    }
}
exports.featureManager = new FeatureManager();
const isFeatureActive = (featureName) => {
    return exports.featureManager.isEnabled(featureName);
};
exports.isFeatureActive = isFeatureActive;
const requiresAdminPermission = (featureName) => {
    return exports.featureManager.requiresAdmin(featureName);
};
exports.requiresAdminPermission = requiresAdminPermission;
const isGuildSpecificFeature = (featureName) => {
    return exports.featureManager.isGuildSpecific(featureName);
};
exports.isGuildSpecificFeature = isGuildSpecificFeature;
function requireFeature(featureName) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        descriptor.value = function (...args) {
            if (!(0, exports.isFeatureActive)(featureName)) {
                throw new Error(`Feature ${featureName} is not enabled`);
            }
            return originalMethod.apply(this, args);
        };
        return descriptor;
    };
}
exports.default = exports.featureManager;
