# 🧪 Dynamic Pricing Testing Instructions

## 🎯 Testing Overview

This guide provides comprehensive testing instructions for the new Dynamic Pricing System. Follow these tests to ensure both fixed and percentage pricing work correctly.

## 🚀 Pre-Testing Setup

### 1. Ensure Migration Completed
```bash
node scripts/migrate-dynamic-pricing-direct.js --validate
```
**Expected Output:**
```
📊 Current Database State:
   • Total roles: [number]
   • Fixed pricing roles: [number]  
   • Percentage pricing roles: 0
✅ Migration validation passed - no issues found
```

### 2. Restart <PERSON>t Process
- Stop the bot process
- Start the bot with the new compiled code
- Verify bot is online and responding

### 3. Check Current Economy
Use `/leaderboard` to see the total server economy size. This helps predict percentage pricing calculations.

## 🔧 Test Suite 1: Fixed Pricing (Backward Compatibility)

### Test 1.1: Create Fixed Price Role
```bash
/setroleforsale @TestFixed 1000 "Fixed price test role - 1000 coins"
```

**Expected Results:**
- ✅ Command succeeds
- ✅ Success message shows "Price: 1,000 PLC"
- ✅ No percentage indicator in message

### Test 1.2: Verify in Shop
```bash
/shop
```

**Expected Results:**
- ✅ TestFixed role appears in shop
- ✅ Shows "1,000 PLC" (no percentage indicator)
- ✅ Buy button is enabled/disabled based on user balance

### Test 1.3: Purchase Fixed Price Role
**Prerequisites:** User needs 1000+ coins
```bash
# Click "Buy" button for TestFixed role in /shop
```

**Expected Results:**
- ✅ Purchase succeeds
- ✅ Deducts exactly 1,000 coins
- ✅ Role is assigned to user
- ✅ Success message shows "Cost: 1,000 PLC"

### Test 1.4: Test /buyrole Command
```bash
/buyrole @TestFixed2
```
(After creating TestFixed2 with `/setroleforsale @TestFixed2 500 "Manual buy test"`)

**Expected Results:**
- ✅ Purchase succeeds with fixed price
- ✅ Shows correct fixed price in confirmation

## 🎯 Test Suite 2: Percentage Pricing (New Feature)

### Test 2.1: Create Percentage Price Role
```bash
/setroleforsale @TestPercent 5% "Percentage test role - 5% of economy"
```

**Expected Results:**
- ✅ Command succeeds
- ✅ Success message shows calculated price + "(5% of economy)"
- ✅ Price updates based on current total economy

### Test 2.2: Verify in Shop
```bash
/shop
```

**Expected Results:**
- ✅ TestPercent role appears in shop
- ✅ Shows "[calculated amount] PLC (5% of economy)"
- ✅ Price matches 5% of total server economy
- ✅ Buy button status reflects calculated price

### Test 2.3: Test Price Calculation
**Manual Calculation:**
1. Check total economy with `/leaderboard` (sum all visible balances)
2. Calculate 5% of total
3. Compare with displayed price in `/shop`

**Expected Results:**
- ✅ Displayed price = 5% of total economy (minimum 10 coins)
- ✅ Price updates if economy changes

### Test 2.4: Purchase Percentage Price Role
**Prerequisites:** User needs sufficient coins for calculated price
```bash
# Click "Buy" button for TestPercent role in /shop
```

**Expected Results:**
- ✅ Purchase succeeds
- ✅ Deducts calculated percentage amount
- ✅ Role is assigned to user
- ✅ Success message shows "Cost: [amount] PLC (5% of economy)"

## 🔍 Test Suite 3: Edge Cases & Validation

### Test 3.1: Minimum Percentage
```bash
/setroleforsale @MinTest 0.1% "Minimum percentage test"
```

**Expected Results:**
- ✅ Command succeeds
- ✅ Price calculated correctly (minimum 10 coins)

### Test 3.2: Maximum Percentage
```bash
/setroleforsale @MaxTest 50% "Maximum percentage test"
```

**Expected Results:**
- ✅ Command succeeds
- ✅ Price calculated as 50% of economy

### Test 3.3: Invalid Percentage - Too Low
```bash
/setroleforsale @Invalid1 0.05% "Should fail - too low"
```

**Expected Results:**
- ❌ Command fails
- ❌ Error: "Percentage must be between 0.1% and 50%"

### Test 3.4: Invalid Percentage - Too High
```bash
/setroleforsale @Invalid2 51% "Should fail - too high"
```

**Expected Results:**
- ❌ Command fails
- ❌ Error: "Percentage must be between 0.1% and 50%"

### Test 3.5: Invalid Format
```bash
/setroleforsale @Invalid3 5 "Should fail - missing %"
/setroleforsale @Invalid4 five% "Should fail - not a number"
```

**Expected Results:**
- ❌ Commands fail
- ❌ Clear error messages about format

## 🔄 Test Suite 4: Dynamic Updates

### Test 4.1: Economy Change Impact
1. Note current percentage role price in `/shop`
2. Have users earn/spend significant coins (change economy)
3. Wait 5+ minutes (cache expiry)
4. Check `/shop` again

**Expected Results:**
- ✅ Percentage role prices update to reflect new economy
- ✅ Fixed role prices remain unchanged

### Test 4.2: Update Existing Role
```bash
# Change existing fixed role to percentage
/setroleforsale @TestFixed 3% "Updated to percentage pricing"
```

**Expected Results:**
- ✅ Role updates from fixed to percentage pricing
- ✅ New price reflects 3% of economy
- ✅ Shop display includes percentage indicator

### Test 4.3: Update Existing Percentage
```bash
# Change percentage value
/setroleforsale @TestPercent 10% "Updated percentage value"
```

**Expected Results:**
- ✅ Role updates to new percentage
- ✅ Price recalculates with new percentage
- ✅ Shop display reflects new percentage

## 🛠️ Test Suite 5: Integration Tests

### Test 5.1: Mixed Role Types in Shop
Create roles with both pricing types:
```bash
/setroleforsale @Fixed1 100 "Fixed 100"
/setroleforsale @Fixed2 500 "Fixed 500"  
/setroleforsale @Percent1 1% "Percent 1%"
/setroleforsale @Percent2 5% "Percent 5%"
```

**Expected Results:**
- ✅ All roles appear in `/shop`
- ✅ Fixed roles show static prices
- ✅ Percentage roles show calculated prices with indicators
- ✅ All buy buttons work correctly

### Test 5.2: Role Management Commands
```bash
/removerolefromsale @TestPercent
```

**Expected Results:**
- ✅ Percentage role removed from shop
- ✅ No longer appears in `/shop`

### Test 5.3: Permission Checks
Test with non-admin user:
```bash
/setroleforsale @Unauthorized 1000 "Should fail"
```

**Expected Results:**
- ❌ Command fails
- ❌ Permission error message

## 📊 Test Suite 6: Performance & Caching

### Test 6.1: Cache Behavior
1. Check percentage role price in `/shop`
2. Immediately check again
3. Wait 6+ minutes
4. Check again

**Expected Results:**
- ✅ First two checks show same price (cached)
- ✅ Third check may show updated price (cache refresh)

### Test 6.2: Multiple Percentage Roles
Create 5+ percentage roles and check `/shop`:

**Expected Results:**
- ✅ All roles load quickly (batch calculation)
- ✅ All show correct calculated prices
- ✅ No performance issues

## 🚨 Troubleshooting Tests

### If Tests Fail:

1. **Check Bot Logs**
   - Look for DynamicPricingService errors
   - Check LeaderboardManager functionality
   - Verify database connection

2. **Validate Database State**
   ```bash
   node scripts/migrate-dynamic-pricing-direct.js --validate
   ```

3. **Check Compiled Code**
   ```bash
   npm run build
   ```

4. **Restart Bot**
   - Ensure bot is running latest compiled code

## ✅ Success Criteria

### All Tests Pass When:
- ✅ Fixed pricing works exactly as before
- ✅ Percentage pricing calculates correctly
- ✅ Shop displays both types properly
- ✅ Purchases work for both types
- ✅ Edge cases are handled gracefully
- ✅ Invalid input is rejected with clear errors
- ✅ Performance is acceptable
- ✅ Cache system works properly

### Test Completion Checklist:
- [ ] Fixed pricing backward compatibility
- [ ] Percentage pricing calculations
- [ ] Shop display formatting
- [ ] Purchase functionality
- [ ] Input validation
- [ ] Edge case handling
- [ ] Dynamic price updates
- [ ] Integration with existing commands
- [ ] Performance under load
- [ ] Error handling and recovery

## 📝 Test Results Template

```
## Dynamic Pricing Test Results

**Test Date:** [Date]
**Bot Version:** [Version]
**Tester:** [Name]

### Fixed Pricing Tests:
- [ ] Create fixed role: PASS/FAIL
- [ ] Shop display: PASS/FAIL  
- [ ] Purchase: PASS/FAIL
- [ ] /buyrole command: PASS/FAIL

### Percentage Pricing Tests:
- [ ] Create percentage role: PASS/FAIL
- [ ] Shop display: PASS/FAIL
- [ ] Purchase: PASS/FAIL
- [ ] Price calculation: PASS/FAIL

### Edge Cases:
- [ ] Min/max percentages: PASS/FAIL
- [ ] Invalid input: PASS/FAIL
- [ ] Dynamic updates: PASS/FAIL

### Integration:
- [ ] Mixed role types: PASS/FAIL
- [ ] Role management: PASS/FAIL
- [ ] Permissions: PASS/FAIL

**Overall Result:** PASS/FAIL
**Notes:** [Any issues or observations]
```
