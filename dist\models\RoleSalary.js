"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleSalary = exports.SalaryFrequency = void 0;
const mongoose_1 = require("mongoose");
var SalaryFrequency;
(function (SalaryFrequency) {
    SalaryFrequency["DAILY"] = "daily";
    SalaryFrequency["WEEKLY"] = "weekly";
})(SalaryFrequency || (exports.SalaryFrequency = SalaryFrequency = {}));
const roleSalarySchema = new mongoose_1.Schema({
    roleId: {
        type: String,
        required: [true, 'Role ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Role ID cannot be empty'
        }
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Guild ID cannot be empty'
        }
    },
    amount: {
        type: Number,
        required: [true, 'Salary amount is required'],
        min: [1, 'Salary amount must be at least 1'],
        max: [1000000, 'Salary amount cannot exceed 1,000,000'],
        validate: {
            validator: function (v) {
                return Number.isInteger(v) && v > 0;
            },
            message: 'Salary amount must be a positive integer'
        }
    },
    frequency: {
        type: String,
        enum: {
            values: Object.values(SalaryFrequency),
            message: 'Frequency must be either daily or weekly'
        },
        required: [true, 'Salary frequency is required']
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    lastProcessed: {
        type: Date,
        default: null
    }
});
roleSalarySchema.index({ guildId: 1, roleId: 1 }, { unique: true });
roleSalarySchema.index({ frequency: 1, lastProcessed: 1 });
roleSalarySchema.index({ guildId: 1 });
roleSalarySchema.pre('save', function (next) {
    this.updatedAt = new Date();
    next();
});
exports.RoleSalary = (0, mongoose_1.model)('RoleSalary', roleSalarySchema);
exports.default = exports.RoleSalary;
