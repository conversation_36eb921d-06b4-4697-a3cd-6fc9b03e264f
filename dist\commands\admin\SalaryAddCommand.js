"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SalaryAddCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const RoleSalary_1 = require("../../models/RoleSalary");
const embedBuilder_1 = require("../../utils/embedBuilder");
const errorHandler_1 = require("../../utils/errorHandler");
class SalaryAddCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'salaryadd',
            description: 'Add or update salary configuration for a role (admin only)',
            category: BaseCommand_1.CommandCategory.ADMIN,
            adminOnly: true,
            requiredFeatures: ['SALARY_SYSTEM'],
            requiredPermissions: ['Administrator'],
        });
    }
    customizeCommand(command) {
        command
            .addRoleOption(option => option.setName('role')
            .setDescription('The role to configure salary for')
            .setRequired(true))
            .addIntegerOption(option => option.setName('amount')
            .setDescription('Salary amount to pay')
            .setRequired(true)
            .setMinValue(1)
            .setMaxValue(1000000))
            .addStringOption(option => option.setName('frequency')
            .setDescription('How often to pay the salary')
            .setRequired(true)
            .addChoices({ name: 'Daily', value: 'daily' }, { name: 'Weekly', value: 'weekly' }));
    }
    async executeCommand(context) {
        const { interaction } = context;
        const role = interaction.options.getRole('role', true);
        const amount = interaction.options.getInteger('amount', true);
        const frequency = interaction.options.getString('frequency', true);
        this.validateSalaryAdd(role.id, amount, frequency);
        try {
            if (!role || role.managed || role.id === interaction.guild.id) {
                throw new errorHandler_1.ValidationError('Invalid role. Cannot assign salary to managed roles or @everyone.');
            }
            const salaryConfig = await RoleSalary_1.RoleSalary.findOneAndUpdate({ roleId: role.id, guildId: interaction.guild.id }, {
                amount,
                frequency,
                updatedAt: new Date()
            }, {
                upsert: true,
                new: true,
                runValidators: true
            });
            const guildId = interaction.guild.id;
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Salary Configuration Updated!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} **Salary System Configuration**\n\n` +
                `Successfully configured salary for **${role.name}**!`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} Role`,
                value: `**${role.name}**\n\`${role.id}\``,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
                value: await (0, embedBuilder_1.formatServerCoins)(guildId, amount),
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Frequency`,
                value: `**${frequency.charAt(0).toUpperCase() + frequency.slice(1)}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.ADMIN.INFO} Next Payment`,
                value: this.getNextPaymentTime(frequency),
                inline: false
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            this.logger.info(`Admin ${interaction.user.username} configured salary for role ${role.name}`, {
                adminId: interaction.user.id,
                roleId: role.id,
                roleName: role.name,
                amount,
                frequency,
                guildId: interaction.guild?.id,
                configId: salaryConfig._id
            });
        }
        catch (error) {
            this.logger.error('Error executing salary add command', {
                error,
                adminId: interaction.user.id,
                roleId: role.id,
                amount,
                frequency
            });
            throw error;
        }
    }
    validateSalaryAdd(roleId, amount, frequency) {
        if (!roleId || roleId.trim().length === 0) {
            throw new errorHandler_1.ValidationError('Valid role is required.');
        }
        if (!Number.isInteger(amount) || amount <= 0) {
            throw new errorHandler_1.ValidationError('Amount must be a positive integer.');
        }
        if (amount > 1000000) {
            throw new errorHandler_1.ValidationError('Amount cannot exceed 1,000,000 coins.');
        }
        if (!Object.values(RoleSalary_1.SalaryFrequency).includes(frequency)) {
            throw new errorHandler_1.ValidationError('Frequency must be either daily or weekly.');
        }
    }
    getNextPaymentTime(frequency) {
        const now = new Date();
        let nextPayment;
        if (frequency === RoleSalary_1.SalaryFrequency.DAILY) {
            nextPayment = new Date(now);
            nextPayment.setDate(nextPayment.getDate() + 1);
            nextPayment.setHours(0, 0, 0, 0);
        }
        else {
            nextPayment = new Date(now);
            const daysUntilSunday = (7 - nextPayment.getDay()) % 7;
            nextPayment.setDate(nextPayment.getDate() + (daysUntilSunday === 0 ? 7 : daysUntilSunday));
            nextPayment.setHours(0, 0, 0, 0);
        }
        return `<t:${Math.floor(nextPayment.getTime() / 1000)}:R>`;
    }
}
exports.SalaryAddCommand = SalaryAddCommand;
