"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EconomyService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const discord_js_1 = require("discord.js");
const BaseService_1 = require("../base/BaseService");
const errorHandler_1 = require("../../utils/errorHandler");
const features_1 = require("../../config/features");
const User_1 = __importDefault(require("../../models/User"));
const Transaction_1 = __importDefault(require("../../models/Transaction"));
class EconomyService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'EconomyService';
    }
    async onInitialize() {
        if (!this.isFeatureEnabled('ECONOMY_SYSTEM')) {
            throw new Error('Economy system is not enabled');
        }
        this.logger.info('[EconomyService] Economy system initialized');
    }
    async adjustBalance(discordId, guildId, amount, type, details, client, dynastyId) {
        this.validateAdjustBalanceInput(discordId, guildId, amount, type);
        if (mongoose_1.default.connection.readyState !== 1) {
            throw new errorHandler_1.DatabaseError('Database is not connected. Please try again in a moment.');
        }
        const session = await mongoose_1.default.startSession();
        this.logOperation('Starting balance adjustment transaction', { discordId, amount, type, details });
        try {
            await session.withTransaction(async () => {
                const trimmedDiscordId = discordId.trim();
                const trimmedGuildId = guildId.trim();
                if (!trimmedDiscordId) {
                    throw new Error('Discord ID cannot be empty after trimming');
                }
                if (!trimmedGuildId) {
                    throw new Error('Guild ID cannot be empty after trimming');
                }
                const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId, guildId: trimmedGuildId }, {
                    $inc: { balance: amount },
                    $setOnInsert: { discordId: trimmedDiscordId, guildId: trimmedGuildId }
                }, {
                    new: true,
                    upsert: true,
                    runValidators: true,
                    session
                });
                this.logOperation('Creating transaction record', {
                    discordId: trimmedDiscordId,
                    type,
                    amount,
                    details,
                    dynastyId
                });
                await Transaction_1.default.create([{
                        discordId: trimmedDiscordId,
                        guildId: trimmedGuildId,
                        type,
                        amount,
                        details,
                        dynastyId,
                        timestamp: new Date()
                    }], { session });
                this.logOperation('Transaction complete', {
                    userId: user?._id,
                    newBalance: user?.balance
                });
                if (amount > 0 && client && guildId && user) {
                    process.nextTick(() => {
                        const timeoutId = setTimeout(async () => {
                            try {
                                await this.checkRoleAchievements(client, trimmedDiscordId, guildId, user.balance);
                            }
                            catch (error) {
                                this.handleError(error, { operation: 'role_achievement_check' });
                            }
                        }, 0);
                        setTimeout(() => {
                            clearTimeout(timeoutId);
                        }, 30000);
                    });
                }
            });
        }
        catch (error) {
            this.handleError(error, { discordId, amount, type });
            throw new errorHandler_1.DatabaseError(`Failed to adjust balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        finally {
            await session.endSession();
        }
    }
    async getBalance(discordId, guildId) {
        try {
            const user = await this.ensureUser(discordId, guildId);
            return user.balance;
        }
        catch (error) {
            this.handleError(error, { discordId, guildId });
            throw new errorHandler_1.DatabaseError(`Failed to get balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getLeaderboard(guildId, limit = 10) {
        try {
            if (!guildId || typeof guildId !== 'string' || guildId.trim().length === 0) {
                throw new errorHandler_1.DatabaseError('Guild ID is required for leaderboard queries');
            }
            const users = await User_1.default.find({ guildId: guildId.trim() })
                .sort({ balance: -1 })
                .limit(limit)
                .lean();
            return users.map((user, index) => ({
                discordId: user.discordId,
                balance: user.balance,
                rank: index + 1,
            }));
        }
        catch (error) {
            this.handleError(error, { guildId, limit });
            throw new errorHandler_1.DatabaseError(`Failed to get leaderboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getTransactionHistory(discordId, guildId, limit = 20) {
        try {
            if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
                throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
            }
            if (!guildId || typeof guildId !== 'string' || guildId.trim().length === 0) {
                throw new errorHandler_1.DatabaseError('Guild ID is required for transaction history queries');
            }
            const transactions = await Transaction_1.default.find({
                discordId: discordId.trim(),
                guildId: guildId.trim()
            })
                .sort({ timestamp: -1 })
                .limit(limit)
                .lean();
            return transactions.map(tx => ({
                id: tx._id.toString(),
                discordId: tx.discordId,
                type: tx.type,
                amount: tx.amount,
                details: tx.details,
                timestamp: tx.timestamp,
            }));
        }
        catch (error) {
            this.handleError(error, { discordId, limit });
            throw new errorHandler_1.DatabaseError(`Failed to get transaction history: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async ensureUser(discordId, guildId) {
        try {
            const trimmedDiscordId = discordId.trim();
            const trimmedGuildId = guildId.trim();
            if (!trimmedDiscordId) {
                throw new Error('Discord ID cannot be empty');
            }
            if (!trimmedGuildId) {
                throw new Error('Guild ID cannot be empty');
            }
            const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId, guildId: trimmedGuildId }, { $setOnInsert: { discordId: trimmedDiscordId, guildId: trimmedGuildId, balance: 0 } }, { new: true, upsert: true, runValidators: true });
            return user;
        }
        catch (error) {
            this.handleError(error, { discordId });
            throw new errorHandler_1.DatabaseError(`Failed to ensure user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    validateAdjustBalanceInput(discordId, guildId, amount, type) {
        if (!discordId || typeof discordId !== 'string' || discordId.trim().length === 0) {
            throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
        }
        if (!guildId || typeof guildId !== 'string' || guildId.trim().length === 0) {
            throw new errorHandler_1.DatabaseError('Guild ID is required for all balance operations');
        }
        if (typeof amount !== 'number' || isNaN(amount)) {
            throw new errorHandler_1.DatabaseError('Invalid amount provided');
        }
        if (!type) {
            throw new errorHandler_1.DatabaseError('Transaction type is required');
        }
    }
    async checkRoleAchievements(client, discordId, guildId, balance) {
        try {
            const roleService = this.getService('RoleService');
            if (!roleService) {
                this.logger.warn('[EconomyService] RoleService not available for achievement check.');
                return;
            }
            const roleResult = await roleService.checkAndAssignRoles(client, discordId, guildId, balance);
            if (roleResult) {
                await roleService.sendRoleAchievementNotifications(roleResult, client);
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'role_achievement_check', discordId, guildId, balance });
        }
    }
}
exports.EconomyService = EconomyService;
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, String, String, discord_js_1.Client, String]),
    __metadata("design:returntype", Promise)
], EconomyService.prototype, "adjustBalance", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], EconomyService.prototype, "getBalance", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], EconomyService.prototype, "getLeaderboard", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number]),
    __metadata("design:returntype", Promise)
], EconomyService.prototype, "getTransactionHistory", null);
