"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryOptimizationService = void 0;
const BaseService_1 = require("./base/BaseService");
class MemoryOptimizationService extends BaseService_1.BaseService {
    constructor() {
        super(...arguments);
        this.name = 'MemoryOptimizationService';
        this.MEMORY_CHECK_INTERVAL = 10 * 60 * 1000;
        this.HIGH_MEMORY_THRESHOLD_MB = 150;
        this.CRITICAL_MEMORY_THRESHOLD_MB = 250;
        this.MAX_METRICS_HISTORY = 20;
        this.metricsHistory = [];
        this.optimizationStats = {
            gcRuns: 0,
            cacheClears: 0,
            memoryFreed: 0,
            lastOptimization: new Date()
        };
    }
    async onInitialize() {
        this.logger.info('[MemoryOptimizationService] Starting memory optimization service');
        this.startMemoryMonitoring();
        this.logMemoryState('Service initialized');
    }
    async onShutdown() {
        if (this.memoryCheckTimer) {
            clearInterval(this.memoryCheckTimer);
            this.memoryCheckTimer = undefined;
        }
        await this.performComprehensiveCleanup();
        this.logger.info('[MemoryOptimizationService] Memory optimization service shutdown complete');
    }
    startMemoryMonitoring() {
        this.memoryCheckTimer = setInterval(async () => {
            try {
                await this.checkMemoryUsage();
            }
            catch (error) {
                this.handleError(error, { operation: 'memory_monitoring' });
            }
        }, this.MEMORY_CHECK_INTERVAL);
        this.logger.info('[MemoryOptimizationService] Memory monitoring started');
    }
    async checkMemoryUsage() {
        const metrics = this.collectMemoryMetrics();
        this.metricsHistory.push(metrics);
        if (this.metricsHistory.length > this.MAX_METRICS_HISTORY) {
            this.metricsHistory = this.metricsHistory.slice(-this.MAX_METRICS_HISTORY);
        }
        const heapUsedMB = metrics.heapUsed / 1024 / 1024;
        const rssMB = metrics.rss / 1024 / 1024;
        if (heapUsedMB > this.CRITICAL_MEMORY_THRESHOLD_MB) {
            this.logger.error('[MemoryOptimizationService] CRITICAL memory usage detected', {
                heapUsedMB: heapUsedMB.toFixed(2),
                rssMB: rssMB.toFixed(2)
            });
            await this.performEmergencyOptimization();
        }
        else if (heapUsedMB > this.HIGH_MEMORY_THRESHOLD_MB) {
            this.logger.warn('[MemoryOptimizationService] High memory usage detected', {
                heapUsedMB: heapUsedMB.toFixed(2),
                rssMB: rssMB.toFixed(2)
            });
            await this.performProactiveOptimization();
        }
        if (this.metricsHistory.length % 6 === 0) {
            this.logMemoryTrends();
        }
    }
    collectMemoryMetrics() {
        const memUsage = process.memoryUsage();
        return {
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            external: memUsage.external,
            rss: memUsage.rss,
            timestamp: Date.now()
        };
    }
    async performProactiveOptimization() {
        this.logger.info('[MemoryOptimizationService] Performing proactive optimization');
        const beforeMetrics = this.collectMemoryMetrics();
        if (global.gc) {
            global.gc();
            this.optimizationStats.gcRuns++;
        }
        if (this.metricsHistory.length > this.MAX_METRICS_HISTORY / 2) {
            this.metricsHistory = this.metricsHistory.slice(-Math.floor(this.MAX_METRICS_HISTORY / 2));
        }
        const afterMetrics = this.collectMemoryMetrics();
        const memoryFreed = beforeMetrics.heapUsed - afterMetrics.heapUsed;
        this.optimizationStats.memoryFreed += Math.max(0, memoryFreed);
        this.optimizationStats.lastOptimization = new Date();
        this.logger.info('[MemoryOptimizationService] Proactive optimization completed', {
            memoryFreedMB: (memoryFreed / 1024 / 1024).toFixed(2),
            heapUsedMB: (afterMetrics.heapUsed / 1024 / 1024).toFixed(2)
        });
    }
    async performEmergencyOptimization() {
        this.logger.error('[MemoryOptimizationService] Performing EMERGENCY optimization');
        const beforeMetrics = this.collectMemoryMetrics();
        await this.performComprehensiveCleanup();
        if (global.gc) {
            for (let i = 0; i < 3; i++) {
                global.gc();
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            this.optimizationStats.gcRuns += 3;
        }
        const afterMetrics = this.collectMemoryMetrics();
        const memoryFreed = beforeMetrics.heapUsed - afterMetrics.heapUsed;
        this.optimizationStats.memoryFreed += Math.max(0, memoryFreed);
        this.optimizationStats.lastOptimization = new Date();
        this.logger.error('[MemoryOptimizationService] Emergency optimization completed', {
            memoryFreedMB: (memoryFreed / 1024 / 1024).toFixed(2),
            heapUsedMB: (afterMetrics.heapUsed / 1024 / 1024).toFixed(2)
        });
    }
    async performComprehensiveCleanup() {
        this.metricsHistory = this.metricsHistory.slice(-5);
        try {
            const tradeSecurityService = this.getService('TradeSecurityService');
            if (tradeSecurityService && typeof tradeSecurityService.performCacheCleanup === 'function') {
                await tradeSecurityService.performCacheCleanup();
            }
        }
        catch (error) {
        }
        this.optimizationStats.cacheClears++;
    }
    logMemoryState(context) {
        const metrics = this.collectMemoryMetrics();
        this.logger.info(`[MemoryOptimizationService] Memory state - ${context}`, {
            heapUsedMB: (metrics.heapUsed / 1024 / 1024).toFixed(2),
            heapTotalMB: (metrics.heapTotal / 1024 / 1024).toFixed(2),
            rssMB: (metrics.rss / 1024 / 1024).toFixed(2),
            externalMB: (metrics.external / 1024 / 1024).toFixed(2)
        });
    }
    logMemoryTrends() {
        if (this.metricsHistory.length < 2)
            return;
        const recent = this.metricsHistory.slice(-6);
        const avgHeapUsed = recent.reduce((sum, m) => sum + m.heapUsed, 0) / recent.length;
        const avgRss = recent.reduce((sum, m) => sum + m.rss, 0) / recent.length;
        this.logger.info('[MemoryOptimizationService] Memory trends (last hour)', {
            avgHeapUsedMB: (avgHeapUsed / 1024 / 1024).toFixed(2),
            avgRssMB: (avgRss / 1024 / 1024).toFixed(2),
            optimizationStats: this.optimizationStats
        });
    }
    getOptimizationStats() {
        return { ...this.optimizationStats };
    }
    getCurrentMemoryMetrics() {
        return this.collectMemoryMetrics();
    }
    async forceOptimization() {
        this.logger.info('[MemoryOptimizationService] Manual optimization triggered');
        await this.performProactiveOptimization();
    }
}
exports.MemoryOptimizationService = MemoryOptimizationService;
