import { SlashCommandBuilder, ChatInputCommandInteraction, GuildMember } from 'discord.js';
import { RoleForSale } from '../models/User';
import { withErrorHandler, ValidationError, DatabaseError, PermissionError } from '../utils/errorHandler';
import { resolveRole, validateRolePermissions } from '../utils/roleResolver';
import { createServerSuccessEmbed, createServerEconomyEmbed, addUserInfo, formatServerCoins, EMOJIS } from '../utils/embedBuilder';
import { hasBankerPermissions } from '../utils/permissions/BankerPermissions';
import ConfigurableConstants from '../config/configurableConstants';
import DynamicPricingService from '../services/dynamicPricingService';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setroleforsale')
        .setDescription('Configure a role as purchasable for the specified coin amount (Admin/Banker only)')
        .addRoleOption(option =>
            option
                .setName('role')
                .setDescription('The role to make purchasable')
                .setRequired(true)
        )
        .addStringOption(option =>
            option
                .setName('price')
                .setDescription('Price: fixed amount (e.g., "1000") or percentage (e.g., "5%")')
                .setRequired(true)
        )
        .addStringOption(option =>
            option
                .setName('description')
                .setDescription('Optional description for the role')
                .setMaxLength(200)
                .setRequired(false)
        ),
    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        const role = interaction.options.getRole('role', true);
        const priceInput = interaction.options.getString('price', true);
        const description = interaction.options.getString('description');
        const guildId = interaction.guild?.id;

        // Basic validation
        if (!guildId) {
            throw new ValidationError('This command can only be used in a server.');
        }

        if (!interaction.guild) {
            throw new ValidationError('Guild information not available.');
        }

        if (!interaction.member || !(interaction.member instanceof GuildMember)) {
            throw new ValidationError('Member information not available.');
        }

        // Permission check - Admin or Banker role
        const hasPermissions = await hasBankerPermissions(interaction.member);
        if (!hasPermissions) {
            throw new PermissionError('This command requires administrator permissions or the designated banker role.');
        }

        try {
            // Parse price input
            let parsedPrice;
            try {
                parsedPrice = DynamicPricingService.parsePriceInput(priceInput);
            } catch (error) {
                throw new ValidationError(`Invalid price format: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }

            // Ensure we have a full Role object (not APIRole)
            const fullRole = interaction.guild.roles.cache.get(role.id) || await interaction.guild.roles.fetch(role.id);
            if (!fullRole) {
                throw new ValidationError('Role not found in guild');
            }

            // Validate role permissions (bot can manage this role)
            await validateRolePermissions(interaction.guild, fullRole);

            // Check if role is already for sale (only check shop_purchase roles)
            const existingRole = await RoleForSale.findOne({
                roleId: fullRole.id,
                guildId: guildId,
                roleType: 'shop_purchase'
            });

            if (existingRole) {
                // Update existing role
                existingRole.price = parsedPrice.value;
                existingRole.name = fullRole.name;
                existingRole.roleType = 'shop_purchase'; // Ensure it's marked as shop purchase
                existingRole.priceType = parsedPrice.priceType;
                existingRole.percentageValue = parsedPrice.percentageValue;
                if (description !== null) {
                    existingRole.description = description;
                }
                await existingRole.save();

                // Create success embed for update
                const embed = await createServerSuccessEmbed(guildId, 'Role Updated in Shop!');
                const coinName = await ConfigurableConstants.getCoinName(guildId);

                // Calculate and format price display
                const calculatedPrice = await DynamicPricingService.calculatePrice(existingRole, guildId);
                const formattedPrice = await formatServerCoins(guildId, calculatedPrice.displayPrice);

                let priceDisplay = formattedPrice;
                if (calculatedPrice.isPercentageBased) {
                    priceDisplay += ` (${calculatedPrice.percentageValue}% of economy)`;
                }

                embed.setDescription(
                    `${EMOJIS.SUCCESS.CHECK} **${fullRole.name}** has been updated in the role shop!\n\n` +
                    `${EMOJIS.ECONOMY.COINS} **New Price:** ${priceDisplay}\n` +
                    `${EMOJIS.MISC.SCROLL} **Description:** ${description || 'None'}`
                );

                embed.addFields({
                    name: `${EMOJIS.MISC.LIGHTBULB} What's Next?`,
                    value: [
                        `• Members can now purchase this role using \`/shop\``,
                        `• Use \`/removerolefromsale\` to remove it from the shop`,
                        `• Use \`/shop\` to view the updated role shop`
                    ].join('\n'),
                    inline: false
                });

                // Add user info to embed
                addUserInfo(embed, interaction.user);

                await interaction.reply({
                    embeds: [embed],
                    ephemeral: false
                });

            } else {
                // Create new role for sale
                const newRole = await RoleForSale.create({
                    guildId: guildId,
                    roleId: fullRole.id,
                    name: fullRole.name,
                    price: parsedPrice.value,
                    description: description,
                    roleType: 'shop_purchase',
                    priceType: parsedPrice.priceType,
                    percentageValue: parsedPrice.percentageValue
                });

                // Create success embed for new role
                const embed = await createServerSuccessEmbed(guildId, 'Role Added to Shop!');
                const coinName = await ConfigurableConstants.getCoinName(guildId);

                // Calculate and format price display
                const calculatedPrice = await DynamicPricingService.calculatePrice(newRole, guildId);
                const formattedPrice = await formatServerCoins(guildId, calculatedPrice.displayPrice);

                let priceDisplay = formattedPrice;
                if (calculatedPrice.isPercentageBased) {
                    priceDisplay += ` (${calculatedPrice.percentageValue}% of economy)`;
                }

                embed.setDescription(
                    `${EMOJIS.SUCCESS.CHECK} **${fullRole.name}** has been added to the role shop!\n\n` +
                    `${EMOJIS.ECONOMY.COINS} **Price:** ${priceDisplay}\n` +
                    `${EMOJIS.MISC.SCROLL} **Description:** ${description || 'None'}`
                );

                embed.addFields({
                    name: `${EMOJIS.MISC.LIGHTBULB} What's Next?`,
                    value: [
                        `• Members can now purchase this role using \`/shop\``,
                        `• Use \`/setroleforsale\` again to update the price or description`,
                        `• Use \`/removerolefromsale\` to remove it from the shop`,
                        `• Use \`/shop\` to view the role shop`
                    ].join('\n'),
                    inline: false
                });

                // Add user info to embed
                addUserInfo(embed, interaction.user);

                await interaction.reply({
                    embeds: [embed],
                    ephemeral: false
                });
            }

        } catch (error: unknown) {
            if (error instanceof ValidationError || error instanceof PermissionError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new DatabaseError(`Failed to configure role for sale: ${error.message}`);
            } else {
                throw new DatabaseError('Failed to configure role for sale.');
            }
        }
    })
};
