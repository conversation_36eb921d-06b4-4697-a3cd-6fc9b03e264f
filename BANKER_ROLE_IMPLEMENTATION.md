# Banker Role Implementation

This document describes the implementation of the banker role functionality and bot fining capability for the Discord bot.

## Overview

The implementation adds a new `/bankers` command that allows administrators to designate a specific Discord role as having "banker permissions". Users with this role can use both `/give` and `/fine` commands, in addition to administrators who retain these permissions.

Additionally, the `/fine` command has been modified to allow targeting Discord bots, enabling recovery of coins accidentally given to bot accounts.

## New Features

### 1. `/bankers` Command

A new admin-only command with three subcommands:

- `/bankers set @role` - Set a role as the banker role
- `/bankers remove` - Remove the current banker role (only admins will have banker permissions)
- `/bankers view` - View the current banker role configuration

**Permissions Required:** Administrator

**Features:**
- Validates that the specified role exists
- Prevents using @everyone as banker role
- Checks role hierarchy to ensure bot can manage the role
- Provides detailed feedback with role information
- Stores configuration persistently in the database

### 2. Enhanced Permission System

**Before:** Only users with Discord Administrator permissions could use `/give` and `/fine`

**After:** Users with either Administrator permissions OR the designated banker role can use these commands

**Implementation:**
- New utility functions in `src/utils/permissions/BankerPermissions.ts`
- `hasBankerPermissions()` - Check if user has banker permissions
- `validateBankerPermissions()` - Validate and throw error if no permissions
- Database integration with ServerConfiguration model

### 3. Bot Fining Capability

**Before:** `/fine` command rejected bot accounts with "You cannot fine a bot" error

**After:** `/fine` command can target bot accounts to recover accidentally given coins

**Changes:**
- Removed bot detection restriction
- Updated command description to indicate bot support
- Enhanced transaction logging to identify bot account fines
- Updated embed messages to show when targeting bot accounts

## Technical Implementation

### Database Schema Changes

Extended `ServerConfiguration` model with new field:

```typescript
interface IServerConfiguration extends Document {
  // ... existing fields
  bankerRoleId?: string; // Optional role ID for banker permissions
}
```

### File Changes

1. **src/models/ServerConfiguration.ts**
   - Added `bankerRoleId` field with validation
   - Updated interface and static methods

2. **src/utils/permissions/BankerPermissions.ts** (NEW)
   - Permission checking utilities
   - Database interaction functions
   - Role validation functions

3. **src/commands/admin/BankersCommand.ts** (NEW)
   - Complete `/bankers` command implementation
   - Subcommand handling (set/remove/view)
   - Rich embed responses

4. **src/commands/give.ts**
   - Updated permission check to use banker permissions
   - Updated command description

5. **src/commands/admin/GiveCommand.ts**
   - Updated permission check to use banker permissions
   - Updated command description

6. **src/commands/fine.ts**
   - Updated permission check to use banker permissions
   - Removed bot targeting restriction
   - Enhanced logging and embed messages

7. **src/commands/CommandManager.ts**
   - Added BankersCommand to imports and command list

8. **src/commands/admin/index.ts**
   - Added BankersCommand export

## Usage Examples

### Setting Up Banker Role

```
/bankers set @Treasurer
```
Response: Confirms the role has been set with member count and permissions granted.

### Viewing Current Configuration

```
/bankers view
```
Response: Shows current banker role or indicates none is set.

### Removing Banker Role

```
/bankers remove
```
Response: Confirms removal and shows that only admins now have permissions.

### Using Give/Fine with Banker Role

Users with the banker role can now use:
```
/give @user 100
/fine @user 50
/fine @SomeBot 25  # Now works for bots too!
```

## Permission Flow

1. **Command Execution:**
   - User runs `/give` or `/fine`
   - `validateBankerPermissions()` is called

2. **Permission Check:**
   - First check: Does user have Administrator permission? → Allow
   - Second check: Get server's banker role from database
   - Third check: Does user have the banker role? → Allow
   - Otherwise: Deny with error message

3. **Database Query:**
   - Query `ServerConfiguration` for guild's `bankerRoleId`
   - Cache result for performance (handled by Mongoose)

## Error Handling

- **Invalid Role:** "The specified role does not exist in this server."
- **@everyone Role:** "The @everyone role cannot be used as a banker role."
- **Role Hierarchy:** "The bot cannot manage this role due to role hierarchy."
- **No Permissions:** "This command requires administrator permissions or the designated banker role."
- **Database Errors:** Graceful fallback with appropriate error messages

## Security Considerations

1. **Admin-Only Configuration:** Only administrators can set/remove banker roles
2. **Role Validation:** Validates role exists and is manageable by bot
3. **Permission Inheritance:** Administrators always retain banker permissions
4. **Database Validation:** Role IDs validated as Discord snowflakes
5. **Error Handling:** Database errors don't expose sensitive information

## Testing

Comprehensive test suite in `src/tests/BankerPermissions.test.ts` covers:

- Permission checking for various user types
- Database interaction functions
- Error handling scenarios
- Edge cases (missing roles, database errors)

### Manual Testing Checklist

1. ✅ Admin can set banker role with `/bankers set @role`
2. ✅ Admin can view current banker role with `/bankers view`
3. ✅ Admin can remove banker role with `/bankers remove`
4. ✅ Users with banker role can use `/give` and `/fine`
5. ✅ Users without banker role cannot use `/give` and `/fine`
6. ✅ Administrators can always use `/give` and `/fine`
7. ✅ `/fine` command works on bot accounts
8. ✅ Error handling for invalid roles and permissions

## Migration Notes

- **Backward Compatibility:** Existing functionality unchanged for administrators
- **Database Migration:** New field is optional, existing servers work without changes
- **Command Registration:** New command automatically registered via CommandManager
- **No Breaking Changes:** All existing commands continue to work as before

## Future Enhancements

Potential improvements for future versions:

1. **Multiple Banker Roles:** Support for multiple roles with banker permissions
2. **Granular Permissions:** Separate permissions for give vs fine commands
3. **Audit Logging:** Enhanced logging of banker role changes
4. **Role Hierarchy:** More sophisticated role hierarchy checking
5. **Temporary Permissions:** Time-limited banker role assignments
