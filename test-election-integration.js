/**
 * Election System Integration Test
 * Tests the complete coin-based voting election workflow
 */

const mongoose = require('mongoose');

// Import compiled models and services
let ElectionService, Election, ElectionCandidate, ElectionVote, User;

try {
  ElectionService = require('./dist/services/election/ElectionService').ElectionService;
  Election = require('./dist/models/Election').default;
  ElectionCandidate = require('./dist/models/ElectionCandidate').default;
  ElectionVote = require('./dist/models/ElectionVote').default;
  User = require('./dist/models/User').default;
} catch (error) {
  console.error('❌ Failed to import compiled modules. Make sure to run "npm run build" first.');
  console.error(error.message);
  process.exit(1);
}

// Mock application context
const mockApp = {
  logger: {
    info: console.log,
    error: console.error,
    warn: console.warn,
    debug: console.log
  },
  getService: () => null,
  isFeatureEnabled: () => true
};

// Mock guild member
const createMockMember = (userId, roles = [], balance = 100) => ({
  id: userId,
  user: { id: userId, username: `user${userId}` },
  displayName: `User ${userId}`,
  guild: { id: 'test-guild' },
  roles: {
    cache: new Map(roles.map(roleId => [roleId, { id: roleId }]))
  },
  permissions: {
    has: () => true
  }
});

async function testElectionSystem() {
  console.log('🗳️ Starting Coin-Based Election System Integration Test...\n');

  try {
    // Connect to test database
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/banker-test';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to test database:', mongoUri.replace(/\/\/.*@/, '//***@'));

    // Clean up any existing test data
    await Election.deleteMany({ guildId: '987654321098765432' });
    await ElectionCandidate.deleteMany({ guildId: '987654321098765432' });
    await ElectionVote.deleteMany({ guildId: '987654321098765432' });
    await User.deleteMany({ guildId: '987654321098765432' });
    console.log('✅ Cleaned up test data');

    // Create test users with coin balances (using valid Discord snowflake IDs)
    const users = [
      { discordId: '123456789012345678', guildId: '987654321098765432', balance: 100 },
      { discordId: '234567890123456789', guildId: '987654321098765432', balance: 200 },
      { discordId: '345678901234567890', guildId: '987654321098765432', balance: 50 },
      { discordId: '456789012345678901', guildId: '987654321098765432', balance: 75 },
      { discordId: '567890123456789012', guildId: '987654321098765432', balance: 1000 }
    ];

    for (const userData of users) {
      await User.create(userData);
    }
    console.log('✅ Created test users with coin balances');

    // Initialize election service
    const electionService = new ElectionService(mockApp);
    await electionService.onInitialize();
    console.log('✅ Initialized election service');

    // Test 1: Create Election
    console.log('\n📝 Test 1: Creating Election...');
    const electionConfig = {
      title: 'Test Server Election',
      description: 'A test election for server leadership',
      rolesToPing: ['111111111111111111'],
      eligibleVoterRoles: ['222222222222222222'],
      eligibleCandidateRoles: ['333333333333333333'],
      allowMultipleVotes: true,
      showVoteWeights: true
    };

    const election = await electionService.createElection(
      '987654321098765432',
      '444444444444444444',
      '567890123456789012',
      electionConfig
    );

    console.log(`✅ Election created with ID: ${election.electionId}`);
    console.log(`   Title: ${election.title}`);
    console.log(`   Status: ${election.status}`);

    // Test 2: Add Candidates
    console.log('\n👥 Test 2: Adding Candidates...');
    
    await electionService.addCandidate(
      election.electionId,
      '345678901234567890',
      'Candidate One',
      'candidate1',
      'I will make the server great!'
    );
    console.log('✅ Added candidate1');

    await electionService.addCandidate(
      election.electionId,
      '456789012345678901',
      'Candidate Two',
      'candidate2',
      'Vote for change and progress!'
    );
    console.log('✅ Added candidate2');

    // Test 3: Get Candidates
    console.log('\n📋 Test 3: Retrieving Candidates...');
    const candidates = await electionService.getElectionCandidates(election.electionId);
    console.log(`✅ Found ${candidates.length} candidates:`);
    candidates.forEach(candidate => {
      console.log(`   - ${candidate.displayName} (${candidate.discordId})`);
    });

    // Test 4: Cast Votes
    console.log('\n🗳️ Test 4: Casting Votes...');
    
    // Voter 1 votes for candidate1 (100 coin weight)
    await electionService.castVote(
      election.electionId,
      '123456789012345678',
      '345678901234567890',
      'Voter One',
      'voter1',
      'Candidate One',
      'candidate1',
      '987654321098765432'
    );
    console.log('✅ Voter1 voted for candidate1 (weight: 100)');

    // Voter 2 votes for candidate2 (200 coin weight)
    await electionService.castVote(
      election.electionId,
      '234567890123456789',
      '456789012345678901',
      'Voter Two',
      'voter2',
      'Candidate Two',
      'candidate2',
      '987654321098765432'
    );
    console.log('✅ Voter2 voted for candidate2 (weight: 200)');

    // Test 5: Check Vote Results
    console.log('\n📊 Test 5: Checking Vote Results...');
    const updatedCandidates = await electionService.getElectionCandidates(election.electionId);
    console.log('Current standings:');
    updatedCandidates.forEach((candidate, index) => {
      console.log(`   ${index + 1}. ${candidate.displayName}: ${candidate.voteWeight} coins (${candidate.voteCount} votes)`);
    });

    // Test 6: Change Vote (if allowed)
    console.log('\n🔄 Test 6: Testing Vote Changes...');
    await electionService.castVote(
      election.electionId,
      '123456789012345678',
      '456789012345678901',
      'Voter One',
      'voter1',
      'Candidate Two',
      'candidate2',
      '987654321098765432'
    );
    console.log('✅ Voter1 changed vote to candidate2');

    // Test 7: Final Results
    console.log('\n🏆 Test 7: Final Results...');
    const finalCandidates = await electionService.getElectionCandidates(election.electionId);
    console.log('Final standings:');
    finalCandidates.forEach((candidate, index) => {
      console.log(`   ${index + 1}. ${candidate.displayName}: ${candidate.voteWeight} coins (${candidate.voteCount} votes) - ${candidate.percentage.toFixed(1)}%`);
    });

    // Test 8: End Election
    console.log('\n🏁 Test 8: Ending Election...');
    const endedElection = await electionService.endElection(election.electionId, '567890123456789012');
    console.log(`✅ Election ended. Status: ${endedElection.status}`);

    // Test 9: Calculate Final Results
    console.log('\n📈 Test 9: Calculating Final Results...');
    const results = await electionService.calculateFinalResults(election.electionId, 5);
    console.log('Final election results:');
    console.log(`   Total Votes: ${results.stats.totalVotes}`);
    console.log(`   Total Vote Weight: ${results.stats.totalVoteWeight}`);
    console.log(`   Participation Rate: ${results.participationRate?.toFixed(1)}%`);
    
    if (results.candidates.length > 0) {
      console.log(`   Winner: ${results.candidates[0].displayName} with ${results.candidates[0].voteWeight} coins`);
    }

    // Test 10: Archive Election
    console.log('\n📚 Test 10: Archiving Election...');
    await electionService.archiveElection(election.electionId, '567890123456789012', 5);
    console.log('✅ Election archived successfully');

    // Test 11: Error Handling - Insufficient Coins
    console.log('\n⚠️ Test 11: Testing Error Handling...');

    // Create user with 0 coins
    await User.create({ discordId: '999999999999999999', guildId: '987654321098765432', balance: 0 });

    // Create new election for error testing
    const errorTestElection = await electionService.createElection(
      '987654321098765432',
      '444444444444444444',
      '567890123456789012',
      {
        title: 'Error Test Election',
        description: 'Testing error handling',
        rolesToPing: ['111111111111111111'],
        eligibleVoterRoles: ['222222222222222222'],
        eligibleCandidateRoles: ['333333333333333333'],
        allowMultipleVotes: true,
        showVoteWeights: true
      }
    );

    // Add a candidate
    await electionService.addCandidate(
      errorTestElection.electionId,
      '345678901234567890',
      'Test Candidate',
      'testcandidate',
      'Test campaign'
    );

    // Try to vote with 0 coins - should fail
    try {
      await electionService.castVote(
        errorTestElection.electionId,
        '999999999999999999',
        '345678901234567890',
        'Zero Coin User',
        'zerouser',
        'Test Candidate',
        'testcandidate',
        '987654321098765432'
      );
      console.log('❌ ERROR: Vote with 0 coins should have failed!');
    } catch (error) {
      console.log('✅ Correctly rejected vote from user with 0 coins:', error.message);
    }

    // Test 12: Duplicate Vote Prevention
    console.log('\n🔄 Test 12: Testing Duplicate Vote Prevention...');

    // Try to vote again with same user (should update, not duplicate)
    const firstVote = await electionService.castVote(
      election.electionId,
      '123456789012345678',
      '345678901234567890',
      'Voter One',
      'voter1',
      'Candidate One',
      'candidate1',
      '987654321098765432'
    );

    const secondVote = await electionService.castVote(
      election.electionId,
      '123456789012345678',
      '456789012345678901',
      'Voter One',
      'voter1',
      'Candidate Two',
      'candidate2',
      '987654321098765432'
    );

    console.log('✅ Vote update successful - no duplicates created');
    console.log(`   First vote ID: ${firstVote.voteId}`);
    console.log(`   Second vote ID: ${secondVote.voteId}`);
    console.log(`   Is update: ${secondVote.isUpdate}`);

    // Test 13: Data Integrity Check
    console.log('\n🔍 Test 13: Data Integrity Verification...');

    // Verify vote counts match database records
    const allVotes = await ElectionVote.find({ electionId: election.electionId, status: 'ACTIVE' });
    const allCandidates = await ElectionCandidate.find({ electionId: election.electionId, status: 'ACTIVE' });

    console.log(`✅ Database integrity check:`);
    console.log(`   Active votes in DB: ${allVotes.length}`);
    console.log(`   Active candidates in DB: ${allCandidates.length}`);
    console.log(`   Election total votes: ${election.totalVotes}`);
    console.log(`   Election total candidates: ${election.totalCandidates}`);

    console.log('\n🎉 All tests passed! Coin-based election system is production-ready!');

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    console.error(error.stack);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from database');
  }
}

// Run the test
if (require.main === module) {
  testElectionSystem().catch(console.error);
}

module.exports = { testElectionSystem };
