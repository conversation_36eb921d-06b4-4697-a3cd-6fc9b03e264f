"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuggestionSystemCommand = void 0;
const discord_js_1 = require("discord.js");
const BaseCommand_1 = require("../base/BaseCommand");
const errorHandler_1 = require("../../utils/errorHandler");
const embedBuilder_1 = require("../../utils/embedBuilder");
const SuggestionService_1 = require("../../services/suggestion/SuggestionService");
class SuggestionSystemCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'suggestionsystem',
            description: 'Configure the suggestion system for this server (admin only)',
            category: BaseCommand_1.CommandCategory.ADMIN,
            adminOnly: true,
            requiredPermissions: ['Administrator'],
        });
        this.suggestionService = new SuggestionService_1.SuggestionService();
    }
    customizeCommand(command) {
        command
            .addSubcommand(subcommand => subcommand
            .setName('configure')
            .setDescription('Configure the suggestion system')
            .addStringOption(option => option.setName('status')
            .setDescription('Enable or disable the suggestion system')
            .setRequired(true)
            .addChoices({ name: 'Enable', value: 'on' }, { name: 'Disable', value: 'off' }))
            .addChannelOption(option => option.setName('channel')
            .setDescription('The channel where suggestions will be posted')
            .setRequired(false)
            .addChannelTypes(discord_js_1.ChannelType.GuildText))
            .addRoleOption(option => option.setName('role1')
            .setDescription('First role that can create suggestions (optional)')
            .setRequired(false))
            .addRoleOption(option => option.setName('role2')
            .setDescription('Second role that can create suggestions (optional)')
            .setRequired(false))
            .addRoleOption(option => option.setName('role3')
            .setDescription('Third role that can create suggestions (optional)')
            .setRequired(false))
            .addRoleOption(option => option.setName('role4')
            .setDescription('Fourth role that can create suggestions (optional)')
            .setRequired(false))
            .addRoleOption(option => option.setName('role5')
            .setDescription('Fifth role that can create suggestions (optional)')
            .setRequired(false)))
            .addSubcommand(subcommand => subcommand
            .setName('cleanup')
            .setDescription('Manually trigger cleanup of expired suggestions'))
            .addSubcommand(subcommand => subcommand
            .setName('stats')
            .setDescription('View suggestion system statistics'));
    }
    async executeCommand(context) {
        const { interaction } = context;
        const subcommand = interaction.options.getSubcommand();
        switch (subcommand) {
            case 'configure':
                await this.handleConfigure(context);
                break;
            case 'cleanup':
                await this.handleCleanup(context);
                break;
            case 'stats':
                await this.handleStats(context);
                break;
            default:
                throw new errorHandler_1.ValidationError(`Unknown subcommand: ${subcommand}`);
        }
    }
    async handleConfigure(context) {
        const { interaction } = context;
        const status = interaction.options.getString('status', true);
        const channel = interaction.options.getChannel('channel');
        const guildId = interaction.guild.id;
        const allowedRoles = [];
        for (let i = 1; i <= 5; i++) {
            const role = interaction.options.getRole(`role${i}`);
            if (role) {
                if (role.managed || role.id === guildId) {
                    throw new errorHandler_1.ValidationError(`Invalid role: ${role.name}. Cannot use managed roles or @everyone.`);
                }
                allowedRoles.push(role.id);
            }
        }
        const enabled = status === 'on';
        if (enabled && !channel) {
            throw new errorHandler_1.ValidationError('A channel must be specified when enabling the suggestion system.');
        }
        if (channel && channel.type !== discord_js_1.ChannelType.GuildText) {
            throw new errorHandler_1.ValidationError('The suggestion channel must be a text channel.');
        }
        try {
            const config = await this.suggestionService.configureSuggestionSystem({
                guildId,
                enabled,
                channelId: channel?.id || '',
                allowedRoles
            });
            if (enabled) {
                const guildId = interaction.guild.id;
                const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Suggestion System Enabled');
                embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Suggestion system has been enabled!**\n\n` +
                    `**Channel:** ${channel}\n` +
                    `**Allowed Roles:** ${allowedRoles.length > 0 ? allowedRoles.map(roleId => `<@&${roleId}>`).join(', ') : 'Everyone'}\n\n` +
                    `Users with the specified roles can now create suggestions by sending messages in the designated channel. ` +
                    `Their messages will be automatically converted to suggestion embeds with voting buttons.`)
                    .addFields({ name: '📝 How it works', value: 'Send a message in the suggestion channel and it will be converted to a suggestion with voting buttons.', inline: false }, { name: '🔺 Voting', value: 'Anyone can upvote or downvote suggestions using the buttons.', inline: true }, { name: '✏️ Editing', value: 'Authors can edit their own suggestions.', inline: true }, { name: '🗑️ Deletion', value: 'Authors can delete their own suggestions.', inline: true }, { name: '⏰ Auto-cleanup', value: 'Suggestions are automatically deleted after 24 hours.', inline: false });
                await interaction.reply({ embeds: [embed] });
            }
            else {
                const guildId = interaction.guild.id;
                const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Suggestion System Disabled');
                embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Suggestion system has been disabled.**\n\n` +
                    `Existing suggestions will continue to function until they expire (24 hours), ` +
                    `but new suggestions cannot be created.`);
                await interaction.reply({ embeds: [embed] });
            }
            this.logger.info(`Suggestion system ${enabled ? 'enabled' : 'disabled'} for guild ${guildId}`, {
                guildId,
                enabled,
                channelId: channel?.id,
                allowedRolesCount: allowedRoles.length,
                adminId: interaction.user.id
            });
        }
        catch (error) {
            this.logger.error('Failed to configure suggestion system', {
                error,
                guildId,
                enabled,
                channelId: channel?.id,
                allowedRolesCount: allowedRoles.length
            });
            const embed = (0, embedBuilder_1.createErrorEmbed)('Configuration Failed')
                .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} Failed to configure the suggestion system.\n\n` +
                `**Error:** ${error instanceof Error ? error.message : 'Unknown error'}`);
            await interaction.reply({ embeds: [embed], ephemeral: true });
        }
    }
    async handleCleanup(context) {
        const { interaction } = context;
        try {
            await interaction.deferReply();
            const stats = await this.suggestionService.triggerCleanup();
            const guildId = interaction.guild.id;
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Cleanup Completed');
            embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Manual cleanup completed successfully!**\n\n` +
                `**Statistics:**\n` +
                `• Suggestions processed: ${stats.suggestionsProcessed}\n` +
                `• Discord messages deleted: ${stats.messagesDeleted}\n` +
                `• Database records removed: ${stats.databaseRecordsRemoved}\n` +
                `• Errors encountered: ${stats.errors}\n` +
                `• Duration: ${stats.duration}ms`);
            await interaction.editReply({ embeds: [embed] });
            this.logger.info(`Manual cleanup triggered by admin ${interaction.user.id}`, {
                guildId: interaction.guild.id,
                stats
            });
        }
        catch (error) {
            this.logger.error('Failed to trigger manual cleanup', {
                error,
                guildId: interaction.guild.id,
                adminId: interaction.user.id
            });
            const embed = (0, embedBuilder_1.createErrorEmbed)('Cleanup Failed')
                .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} Failed to trigger cleanup.\n\n` +
                `**Error:** ${error instanceof Error ? error.message : 'Unknown error'}`);
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [embed] });
            }
            else {
                await interaction.reply({ embeds: [embed], ephemeral: true });
            }
        }
    }
    async handleStats(context) {
        const { interaction } = context;
        try {
            const stats = await this.suggestionService.getCleanupStats();
            const config = await this.suggestionService.getSuggestionConfig(interaction.guild.id);
            const guildId = interaction.guild.id;
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Suggestion System Statistics');
            embed.setDescription(`📊 **Current Statistics**\n\n` +
                `**System Status:** ${config?.enabled ? '🟢 Enabled' : '🔴 Disabled'}\n` +
                `**Suggestion Channel:** ${config?.channelId ? `<#${config.channelId}>` : 'Not configured'}\n` +
                `**Allowed Roles:** ${config?.allowedRoles?.length ? config.allowedRoles.map(id => `<@&${id}>`).join(', ') : 'Everyone'}\n\n` +
                `**Data Statistics:**\n` +
                `• Total active suggestions: ${stats.totalSuggestions}\n` +
                `• Expired suggestions pending cleanup: ${stats.expiredSuggestions}\n` +
                `• Next automatic cleanup: ${stats.nextCleanupIn > 0 ? `${Math.round(stats.nextCleanupIn / 1000 / 60)} minutes` : 'Unknown'}`)
                .addFields({ name: '⚙️ System Info', value: 'Suggestions automatically expire after 24 hours', inline: false }, { name: '🔄 Cleanup', value: 'Automatic cleanup runs every hour', inline: true }, { name: '📝 Manual Cleanup', value: 'Use `/suggestionsystem cleanup` to trigger manually', inline: true });
            await interaction.reply({ embeds: [embed] });
        }
        catch (error) {
            this.logger.error('Failed to get suggestion system stats', {
                error,
                guildId: interaction.guild.id,
                adminId: interaction.user.id
            });
            const embed = (0, embedBuilder_1.createErrorEmbed)('Stats Unavailable')
                .setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} Failed to retrieve statistics.\n\n` +
                `**Error:** ${error instanceof Error ? error.message : 'Unknown error'}`);
            await interaction.reply({ embeds: [embed], ephemeral: true });
        }
    }
    validateSuggestionConfig(guildId, enabled, channelId, allowedRoles) {
        if (!guildId || !/^\d{17,20}$/.test(guildId)) {
            throw new errorHandler_1.ValidationError('Invalid guild ID');
        }
        if (enabled) {
            if (!channelId || !/^\d{17,20}$/.test(channelId)) {
                throw new errorHandler_1.ValidationError('Invalid channel ID');
            }
        }
        if (allowedRoles && allowedRoles.length > 0) {
            for (const roleId of allowedRoles) {
                if (!/^\d{17,20}$/.test(roleId)) {
                    throw new errorHandler_1.ValidationError(`Invalid role ID: ${roleId}`);
                }
            }
        }
    }
}
exports.SuggestionSystemCommand = SuggestionSystemCommand;
