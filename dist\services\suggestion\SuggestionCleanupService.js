"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuggestionCleanupService = void 0;
const BaseService_1 = require("../base/BaseService");
const Suggestion_1 = require("../../models/Suggestion");
class SuggestionCleanupService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'SuggestionCleanupService';
        this.cleanupInterval = null;
        this.CLEANUP_INTERVAL_MS = 60 * 60 * 1000;
        this.BATCH_SIZE = 50;
    }
    async onInitialize() {
        this.logger.info('[SuggestionCleanupService] Starting suggestion cleanup service...');
        this.startCleanupInterval();
        await this.runCleanup();
        this.logger.info('[SuggestionCleanupService] Suggestion cleanup service initialized');
    }
    async onShutdown() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        this.logger.info('[SuggestionCleanupService] Suggestion cleanup service stopped');
    }
    startCleanupInterval() {
        this.cleanupInterval = setInterval(async () => {
            try {
                await this.runCleanup();
            }
            catch (error) {
                this.handleError(error, { operation: 'scheduled_cleanup' });
            }
        }, this.CLEANUP_INTERVAL_MS);
        this.logger.info(`[SuggestionCleanupService] Cleanup interval started (every ${this.CLEANUP_INTERVAL_MS / 1000 / 60} minutes)`);
    }
    async runCleanup() {
        const startTime = new Date();
        const stats = {
            suggestionsProcessed: 0,
            messagesDeleted: 0,
            databaseRecordsRemoved: 0,
            errors: 0,
            startTime,
            endTime: new Date(),
            duration: 0
        };
        try {
            this.logger.info('[SuggestionCleanupService] Starting cleanup process...');
            const expiredSuggestions = await this.findExpiredSuggestions();
            if (expiredSuggestions.length === 0) {
                this.logger.debug('[SuggestionCleanupService] No expired suggestions found');
                stats.endTime = new Date();
                stats.duration = stats.endTime.getTime() - stats.startTime.getTime();
                return stats;
            }
            this.logger.info(`[SuggestionCleanupService] Found ${expiredSuggestions.length} expired suggestions to clean up`);
            for (let i = 0; i < expiredSuggestions.length; i += this.BATCH_SIZE) {
                const batch = expiredSuggestions.slice(i, i + this.BATCH_SIZE);
                await this.processBatch(batch, stats);
            }
            await this.cleanupOrphanedRecords(stats);
            stats.endTime = new Date();
            stats.duration = stats.endTime.getTime() - stats.startTime.getTime();
            this.logger.info('[SuggestionCleanupService] Cleanup process completed', {
                suggestionsProcessed: stats.suggestionsProcessed,
                messagesDeleted: stats.messagesDeleted,
                databaseRecordsRemoved: stats.databaseRecordsRemoved,
                errors: stats.errors,
                duration: `${stats.duration}ms`
            });
            return stats;
        }
        catch (error) {
            this.handleError(error, { operation: 'run_cleanup' });
            stats.errors++;
            stats.endTime = new Date();
            stats.duration = stats.endTime.getTime() - stats.startTime.getTime();
            return stats;
        }
    }
    async findExpiredSuggestions() {
        try {
            return await Suggestion_1.Suggestion.find({
                expiresAt: { $lt: new Date() }
            }).limit(1000);
        }
        catch (error) {
            this.handleError(error, { operation: 'find_expired_suggestions' });
            return [];
        }
    }
    async processBatch(suggestions, stats) {
        for (const suggestion of suggestions) {
            try {
                stats.suggestionsProcessed++;
                const messageDeleted = await this.deleteDiscordMessage(suggestion);
                if (messageDeleted) {
                    stats.messagesDeleted++;
                }
                await Suggestion_1.Suggestion.deleteOne({ _id: suggestion._id });
                stats.databaseRecordsRemoved++;
                this.logOperation('Cleaned up expired suggestion', {
                    suggestionId: suggestion.suggestionId,
                    guildId: suggestion.guildId,
                    authorId: suggestion.authorId,
                    messageDeleted
                });
            }
            catch (error) {
                stats.errors++;
                this.handleError(error, {
                    operation: 'process_suggestion_cleanup',
                    suggestionId: suggestion.suggestionId,
                    messageId: suggestion.messageId
                });
            }
        }
    }
    async deleteDiscordMessage(suggestion) {
        try {
            const channel = await this.app.client.channels.fetch(suggestion.channelId);
            if (!channel) {
                this.logger.warn('[SuggestionCleanupService] Channel not found', {
                    channelId: suggestion.channelId,
                    suggestionId: suggestion.suggestionId
                });
                return false;
            }
            const message = await channel.messages.fetch(suggestion.messageId);
            if (!message) {
                this.logger.warn('[SuggestionCleanupService] Message not found', {
                    messageId: suggestion.messageId,
                    suggestionId: suggestion.suggestionId
                });
                return false;
            }
            await message.delete();
            return true;
        }
        catch (error) {
            this.logger.debug('[SuggestionCleanupService] Could not delete Discord message', {
                messageId: suggestion.messageId,
                channelId: suggestion.channelId,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            return false;
        }
    }
    async cleanupOrphanedRecords(stats) {
        try {
            const cutoffTime = new Date(Date.now() - 25 * 60 * 60 * 1000);
            const orphanedSuggestions = await Suggestion_1.Suggestion.find({
                createdAt: { $lt: cutoffTime }
            }).limit(100);
            if (orphanedSuggestions.length > 0) {
                this.logger.info(`[SuggestionCleanupService] Found ${orphanedSuggestions.length} orphaned suggestion records`);
                for (const suggestion of orphanedSuggestions) {
                    try {
                        await Suggestion_1.Suggestion.deleteOne({ _id: suggestion._id });
                        stats.databaseRecordsRemoved++;
                        this.logOperation('Cleaned up orphaned suggestion record', {
                            suggestionId: suggestion.suggestionId,
                            age: Date.now() - suggestion.createdAt.getTime()
                        });
                    }
                    catch (error) {
                        stats.errors++;
                        this.handleError(error, {
                            operation: 'cleanup_orphaned_record',
                            suggestionId: suggestion.suggestionId
                        });
                    }
                }
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'cleanup_orphaned_records' });
            stats.errors++;
        }
    }
    async triggerManualCleanup() {
        this.logger.info('[SuggestionCleanupService] Manual cleanup triggered');
        return await this.runCleanup();
    }
    async getCleanupStats() {
        try {
            const totalSuggestions = await Suggestion_1.Suggestion.countDocuments();
            const expiredSuggestions = await Suggestion_1.Suggestion.countDocuments({
                expiresAt: { $lt: new Date() }
            });
            const nextCleanupIn = this.cleanupInterval ? this.CLEANUP_INTERVAL_MS : 0;
            return {
                totalSuggestions,
                expiredSuggestions,
                nextCleanupIn
            };
        }
        catch (error) {
            this.handleError(error, { operation: 'get_cleanup_stats' });
            return {
                totalSuggestions: 0,
                expiredSuggestions: 0,
                nextCleanupIn: 0
            };
        }
    }
}
exports.SuggestionCleanupService = SuggestionCleanupService;
