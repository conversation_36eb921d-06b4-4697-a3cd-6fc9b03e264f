"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const coinDecayConfigurationSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        unique: true,
        index: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    enabled: {
        type: Boolean,
        default: false,
        index: true
    },
    decayPercentage: {
        type: Number,
        required: [true, 'Decay percentage is required'],
        min: [1, 'Decay percentage must be at least 1%'],
        max: [50, 'Decay percentage cannot exceed 50%'],
        validate: {
            validator: function (v) {
                return Number.isInteger(v);
            },
            message: 'Decay percentage must be an integer'
        }
    },
    inactivityThresholdDays: {
        type: Number,
        required: [true, 'Inactivity threshold is required'],
        min: [7, 'Inactivity threshold must be at least 7 days'],
        max: [365, 'Inactivity threshold cannot exceed 365 days'],
        validate: {
            validator: function (v) {
                return Number.isInteger(v);
            },
            message: 'Inactivity threshold must be an integer'
        }
    }
}, {
    timestamps: true
});
coinDecayConfigurationSchema.index({ guildId: 1 }, { unique: true });
coinDecayConfigurationSchema.index({ enabled: 1 });
exports.default = (0, mongoose_1.model)('CoinDecayConfiguration', coinDecayConfigurationSchema);
