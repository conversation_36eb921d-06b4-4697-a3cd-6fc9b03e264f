"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runSuggestionSystemTests = runSuggestionSystemTests;
const child_process_1 = require("child_process");
const validateSuggestionSystem_1 = require("./validateSuggestionSystem");
const logger_1 = require("../core/logger");
const logger = (0, logger_1.getLogger)();
class SuggestionTestRunner {
    constructor() {
        this.results = [];
    }
    async runAllTests() {
        logger.info('[TestRunner] Starting comprehensive suggestion system tests...');
        await this.runJestTests();
        await this.runValidation();
        this.printSummary();
        return this.results;
    }
    async runJestTests() {
        logger.info('[TestRunner] Running Jest test suite...');
        try {
            const output = await this.executeCommand('npm', ['run', 'test:suggestion']);
            this.addResult('Jest Test Suite', true, output);
        }
        catch (error) {
            this.addResult('Jest Test Suite', false, undefined, error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async runValidation() {
        logger.info('[TestRunner] Running validation script...');
        try {
            const validationResults = await (0, validateSuggestionSystem_1.validateSuggestionSystem)();
            const allPassed = validationResults.every(result => result.passed);
            const summary = `${validationResults.filter(r => r.passed).length}/${validationResults.length} validation tests passed`;
            this.addResult('Validation Script', allPassed, summary);
        }
        catch (error) {
            this.addResult('Validation Script', false, undefined, error instanceof Error ? error.message : 'Unknown error');
        }
    }
    executeCommand(command, args) {
        return new Promise((resolve, reject) => {
            const process = (0, child_process_1.spawn)(command, args, { stdio: 'pipe' });
            let output = '';
            let errorOutput = '';
            process.stdout.on('data', (data) => {
                output += data.toString();
            });
            process.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });
            process.on('close', (code) => {
                if (code === 0) {
                    resolve(output);
                }
                else {
                    reject(new Error(`Command failed with code ${code}: ${errorOutput}`));
                }
            });
            process.on('error', (error) => {
                reject(error);
            });
        });
    }
    addResult(name, passed, output, error) {
        this.results.push({ name, passed, output, error });
        const status = passed ? '✅ PASS' : '❌ FAIL';
        logger.info(`[TestRunner] ${status}: ${name}`);
        if (error) {
            logger.error(`[TestRunner] Error: ${error}`);
        }
    }
    printSummary() {
        const totalTests = this.results.length;
        const passedTests = this.results.filter(r => r.passed).length;
        console.log('\n' + '='.repeat(60));
        console.log('SUGGESTION SYSTEM TEST SUMMARY');
        console.log('='.repeat(60));
        this.results.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.name}`);
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        });
        console.log('='.repeat(60));
        console.log(`TOTAL: ${passedTests}/${totalTests} tests passed`);
        if (passedTests === totalTests) {
            console.log('🎉 ALL TESTS PASSED! Suggestion system is ready for production.');
        }
        else {
            console.log('⚠️  SOME TESTS FAILED! Please review the results before deploying.');
        }
        console.log('='.repeat(60));
    }
}
async function runSuggestionSystemTests() {
    const runner = new SuggestionTestRunner();
    try {
        const results = await runner.runAllTests();
        return results.every(result => result.passed);
    }
    catch (error) {
        logger.error('[TestRunner] Test execution failed:', error);
        return false;
    }
}
if (require.main === module) {
    runSuggestionSystemTests()
        .then((success) => {
        process.exit(success ? 0 : 1);
    })
        .catch((error) => {
        console.error('Test execution failed:', error);
        process.exit(1);
    });
}
