"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testHelpSystem = testHelpSystem;
const help_1 = require("../commands/help");
const helpStateManager_1 = require("../utils/helpStateManager");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
async function testHelpSystem() {
    console.log('🧪 Starting Help System Tests...\n');
    const testGuildId = 'test-guild-123';
    const testUserId = 'test-user-456';
    try {
        console.log('📋 Test 1: ConfigurableConstants functionality');
        try {
            const nationName = await configurableConstants_1.default.getNationName(testGuildId);
            const coinName = await configurableConstants_1.default.getCoinName(testGuildId);
            const coinSymbol = await configurableConstants_1.default.getCoinSymbol(testGuildId);
            console.log(`✅ Nation Name: ${nationName}`);
            console.log(`✅ Coin Name: ${coinName}`);
            console.log(`✅ Coin Symbol: ${coinSymbol}`);
        }
        catch (error) {
            console.log(`❌ ConfigurableConstants error: ${error}`);
        }
        console.log('\n📋 Test 2: Command data generation');
        try {
            const commandData = await (0, help_1.getCommandData)(testGuildId);
            console.log(`✅ Generated ${commandData.length} categories`);
            commandData.forEach((category, index) => {
                console.log(`  ${index + 1}. ${category.emoji} ${category.name} (${category.commands.length} commands)`);
            });
        }
        catch (error) {
            console.log(`❌ Command data generation error: ${error}`);
        }
        console.log('\n📋 Test 3: State management');
        try {
            (0, helpStateManager_1.setHelpState)(testUserId, testGuildId, {
                currentPage: 'main',
                userId: testUserId,
                guildId: testGuildId
            });
            const state = (0, helpStateManager_1.getHelpState)(testUserId, testGuildId);
            if (state && state.currentPage === 'main') {
                console.log('✅ State set and retrieved successfully');
            }
            else {
                console.log('❌ State retrieval failed');
            }
            (0, helpStateManager_1.updateHelpState)(testUserId, testGuildId, {
                currentPage: 'category',
                categoryIndex: 0
            });
            const updatedState = (0, helpStateManager_1.getHelpState)(testUserId, testGuildId);
            if (updatedState && updatedState.currentPage === 'category' && updatedState.categoryIndex === 0) {
                console.log('✅ State updated successfully');
            }
            else {
                console.log('❌ State update failed');
            }
        }
        catch (error) {
            console.log(`❌ State management error: ${error}`);
        }
        console.log('\n📋 Test 4: Embed creation');
        try {
            const mainEmbed = await (0, help_1.createMainHelpEmbed)(testGuildId);
            if (mainEmbed && mainEmbed.data.title) {
                console.log(`✅ Main help embed created: ${mainEmbed.data.title}`);
            }
            else {
                console.log('❌ Main help embed creation failed');
            }
            const categoryEmbed = await (0, help_1.createCategoryHelpEmbed)(testGuildId, 0, false);
            if (categoryEmbed && categoryEmbed.data.title) {
                console.log(`✅ Category help embed created: ${categoryEmbed.data.title}`);
            }
            else {
                console.log('❌ Category help embed creation failed');
            }
            const commandEmbed = await (0, help_1.createCommandDetailEmbed)(testGuildId, 0, 0);
            if (commandEmbed && commandEmbed.data.title) {
                console.log(`✅ Command detail embed created: ${commandEmbed.data.title}`);
            }
            else {
                console.log('❌ Command detail embed creation failed');
            }
        }
        catch (error) {
            console.log(`❌ Embed creation error: ${error}`);
        }
        console.log('\n📋 Test 5: Error handling');
        try {
            try {
                await (0, help_1.createCategoryHelpEmbed)(testGuildId, 999, false);
                console.log('❌ Should have thrown error for invalid category index');
            }
            catch (error) {
                console.log('✅ Properly handled invalid category index');
            }
            try {
                await (0, help_1.createCommandDetailEmbed)(testGuildId, 0, 999);
                console.log('❌ Should have thrown error for invalid command index');
            }
            catch (error) {
                console.log('✅ Properly handled invalid command index');
            }
        }
        catch (error) {
            console.log(`❌ Error handling test failed: ${error}`);
        }
        console.log('\n📋 Test 6: State cleanup');
        try {
            (0, helpStateManager_1.cleanupHelpStates)();
            console.log('✅ State cleanup executed successfully');
        }
        catch (error) {
            console.log(`❌ State cleanup error: ${error}`);
        }
        console.log('\n🎉 Help System Tests Completed!');
    }
    catch (error) {
        console.error('💥 Test suite failed:', error);
    }
}
if (require.main === module) {
    testHelpSystem().catch(console.error);
}
