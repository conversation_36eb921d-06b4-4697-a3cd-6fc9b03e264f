"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const coinDecayService_1 = require("../services/coinDecayService");
exports.default = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('coindecay')
        .setDescription('Configure the coin decay system for inactive users')
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator)
        .addBooleanOption(option => option.setName('status')
        .setDescription('Enable or disable the coin decay system')
        .setRequired(true))
        .addIntegerOption(option => option.setName('percentage')
        .setDescription('Percentage of coins to decay (1-50)')
        .setRequired(true)
        .setMinValue(1)
        .setMaxValue(50))
        .addIntegerOption(option => option.setName('inactivity_threshold')
        .setDescription('Days of inactivity before decay triggers (7-365)')
        .setRequired(true)
        .setMinValue(7)
        .setMaxValue(365)),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (!interaction.guild) {
            const embed = (0, embedBuilder_1.createErrorEmbed)('This command can only be used in a server.');
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }
        if (!interaction.member || !interaction.member.permissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            const embed = (0, embedBuilder_1.createErrorEmbed)('This command requires administrator permissions.');
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }
        const guildId = interaction.guild.id;
        const enabled = interaction.options.getBoolean('status', true);
        const percentage = interaction.options.getInteger('percentage', true);
        const inactivityThreshold = interaction.options.getInteger('inactivity_threshold', true);
        try {
            if (!Number.isInteger(percentage) || percentage < 1 || percentage > 50) {
                const embed = (0, embedBuilder_1.createErrorEmbed)('Decay percentage must be an integer between 1 and 50.');
                await interaction.reply({ embeds: [embed], ephemeral: true });
                return;
            }
            if (!Number.isInteger(inactivityThreshold) || inactivityThreshold < 7 || inactivityThreshold > 365) {
                const embed = (0, embedBuilder_1.createErrorEmbed)('Inactivity threshold must be an integer between 7 and 365 days.');
                await interaction.reply({ embeds: [embed], ephemeral: true });
                return;
            }
            const config = await (0, coinDecayService_1.updateCoinDecayConfiguration)(guildId, enabled, percentage, inactivityThreshold);
            const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Coin Decay Settings Updated`, 'The coin decay system configuration has been successfully updated.');
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} Current Configuration`,
                value: [
                    `**Status:** ${enabled ? '✅ Enabled' : '❌ Disabled'}`,
                    `**Decay Rate:** ${percentage}% of user's coins`,
                    `**Inactivity Threshold:** ${inactivityThreshold} days`,
                    '',
                    `**How it works:** Users who haven't sent messages for ${inactivityThreshold} days will lose ${percentage}% of their coins daily until they become active again.`
                ].join('\n'),
                inline: false
            });
            if (enabled) {
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Important Notes`,
                    value: [
                        '• Coin decay runs automatically every day at 3:00 AM UTC',
                        '• Only affects users with positive coin balances',
                        '• Users with 0 coins are not affected',
                        '• Activity is tracked by sending messages in any channel',
                        '• Decay actions are logged for audit purposes'
                    ].join('\n'),
                    inline: false
                });
            }
            await interaction.reply({ embeds: [embed] });
        }
        catch (error) {
            console.error('[Coin Decay Command] Error updating configuration:', error);
            let errorMessage = 'Failed to update coin decay configuration.';
            if (error instanceof errorHandler_1.DatabaseError) {
                errorMessage = `Database error: ${error.message}`;
            }
            else if (error instanceof Error) {
                errorMessage = error.message;
            }
            const embed = (0, embedBuilder_1.createErrorEmbed)(errorMessage);
            await interaction.reply({ embeds: [embed], ephemeral: true });
        }
    })
};
