"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const roleResolver_1 = require("../utils/roleResolver");
const starterBalanceService_1 = require("../services/starterBalanceService");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('starterbalance')
        .setDescription('Manage starter balance rules for roles (admin only)')
        .addStringOption(option => option.setName('action')
        .setDescription('Action to perform')
        .setRequired(true)
        .addChoices({ name: 'Add Rule', value: 'add' }, { name: 'Edit Rule', value: 'edit' }, { name: 'Remove Rule', value: 'remove' }, { name: 'List Rules', value: 'list' }))
        .addStringOption(option => option.setName('role')
        .setDescription('Discord role for the starter balance rule')
        .setRequired(false))
        .addIntegerOption(option => option.setName('amount')
        .setDescription('Amount of PLC to give when role is assigned (1-10000)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(10000))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError('You need Administrator permissions to use this command.');
        }
        const action = interaction.options.getString('action', true);
        const roleInput = interaction.options.getString('role');
        const amount = interaction.options.getInteger('amount');
        try {
            const guildId = interaction.guild.id;
            if (action === 'list') {
                const rules = await (0, starterBalanceService_1.getStarterBalanceRules)(guildId);
                if (rules.length === 0) {
                    const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, 'Starter Balance Rules');
                    embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.INFO} **No Starter Balance Rules**\n\n` +
                        `There are currently no starter balance rules configured for this server.\n\n` +
                        `Use \`/starterbalance action:add\` to create your first rule.`);
                    await interaction.reply({ embeds: [embed], ephemeral: false });
                    return;
                }
                const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, 'Starter Balance Rules');
                embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.LIST} **Current Starter Balance Rules**\n\n` +
                    `The following roles automatically grant coins when assigned:`);
                const maxFields = Math.min(rules.length, 25);
                for (let i = 0; i < maxFields; i++) {
                    const rule = rules[i];
                    embed.addFields({
                        name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} ${rule.roleName}`,
                        value: `**${rule.amount} PLC** granted on assignment`,
                        inline: true
                    });
                }
                if (rules.length > 25) {
                    embed.setFooter({ text: `Showing first 25 of ${rules.length} rules` });
                }
                await interaction.reply({ embeds: [embed], ephemeral: false });
                return;
            }
            if (!roleInput) {
                throw new errorHandler_1.ValidationError(`Role is required for the ${action} action. Please specify which role to ${action}.`);
            }
            const roleResolution = await (0, roleResolver_1.resolveRole)(interaction.guild, roleInput);
            const { role, resolvedBy, confidence } = roleResolution;
            await (0, roleResolver_1.validateRolePermissions)(interaction.guild, role);
            if (action === 'add') {
                if (!amount) {
                    throw new errorHandler_1.ValidationError('Amount is required when adding a starter balance rule. Please specify how many PLC to give (1-10000).');
                }
                if (await (0, starterBalanceService_1.hasStarterBalanceRule)(interaction.guild.id, role.id)) {
                    throw new errorHandler_1.ValidationError(`A starter balance rule already exists for role "${role.name}". Use the edit action to modify it.`);
                }
                await (0, starterBalanceService_1.createStarterBalanceRule)(interaction.guild.id, role.id, role.name, amount);
                const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Starter Balance Rule Added');
                embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Rule Created Successfully**\n\n` +
                    `Users will now automatically receive **${amount} coins** when they are assigned the **${role.name}** role.`)
                    .addFields({
                    name: `${embedBuilder_1.EMOJIS.ADMIN.HAMMER} Configuration`,
                    value: `**Role:** ${role.name}\n` +
                        `**Amount:** ${amount} PLC\n` +
                        `**Action:** Granted when role is assigned`,
                    inline: false
                });
                if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                    embed.setFooter({ text: `Note: Resolved "${roleInput}" to "${role.name}"` });
                }
                await interaction.reply({ embeds: [embed], ephemeral: false });
            }
            else if (action === 'edit') {
                if (!amount) {
                    throw new errorHandler_1.ValidationError('Amount is required when editing a starter balance rule. Please specify the new PLC amount (1-10000).');
                }
                const updatedRule = await (0, starterBalanceService_1.updateStarterBalanceRule)(interaction.guild.id, role.id, amount);
                if (!updatedRule) {
                    throw new errorHandler_1.ValidationError(`No starter balance rule exists for role "${role.name}". Use the add action to create one.`);
                }
                const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Starter Balance Rule Updated');
                embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Rule Updated Successfully**\n\n` +
                    `The starter balance for **${role.name}** has been updated to **${amount} coins**.`)
                    .addFields({
                    name: `${embedBuilder_1.EMOJIS.ADMIN.HAMMER} Updated Configuration`,
                    value: `**Role:** ${role.name}\n` +
                        `**New Amount:** ${amount} PLC\n` +
                        `**Action:** Granted when role is assigned`,
                    inline: false
                });
                if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                    embed.setFooter({ text: `Note: Resolved "${roleInput}" to "${role.name}"` });
                }
                await interaction.reply({ embeds: [embed], ephemeral: false });
            }
            else if (action === 'remove') {
                const removed = await (0, starterBalanceService_1.removeStarterBalanceRule)(interaction.guild.id, role.id);
                if (!removed) {
                    throw new errorHandler_1.ValidationError(`No starter balance rule exists for role "${role.name}".`);
                }
                const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Starter Balance Rule Removed');
                embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Rule Removed Successfully**\n\n` +
                    `The starter balance rule for **${role.name}** has been removed.\n\n` +
                    `${embedBuilder_1.EMOJIS.ADMIN.INFO} Users will no longer receive PLC when assigned this role.`);
                if (resolvedBy === 'fuzzy_name' && confidence < 0.9) {
                    embed.setFooter({ text: `Note: Resolved "${roleInput}" to "${role.name}"` });
                }
                await interaction.reply({ embeds: [embed], ephemeral: false });
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError('Failed to manage starter balance rule. Please try again.', error);
            }
            throw new errorHandler_1.DatabaseError('An unexpected error occurred while managing the starter balance rule.');
        }
    })
};
