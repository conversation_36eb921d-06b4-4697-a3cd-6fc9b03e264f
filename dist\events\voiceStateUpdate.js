"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoiceStateUpdateEventHandler = void 0;
const base_1 = require("./base");
class VoiceStateUpdateEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'voiceStateUpdate');
        this.name = 'voiceStateUpdate';
    }
    async execute(oldState, newState) {
        try {
            if (newState.member?.user.bot)
                return;
            const userId = newState.member?.user.id;
            const guildId = newState.guild.id;
            if (!userId)
                return;
            if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
                await this.trackVoiceActivity(oldState, newState, userId, guildId);
            }
        }
        catch (error) {
            this.handleError(error, {
                userId: newState.member?.user.id,
                guildId: newState.guild.id,
                oldChannelId: oldState.channel?.id,
                newChannelId: newState.channel?.id,
            });
        }
    }
    async trackVoiceActivity(oldState, newState, userId, guildId) {
        try {
            if (!oldState.channel && newState.channel) {
                this.logExecution(`User joined voice channel: ${newState.channel.name}`, {
                    userId,
                    guildId,
                    channelId: newState.channel.id,
                    channelName: newState.channel.name,
                });
                const { checkAndProcessMilestones } = await Promise.resolve().then(() => __importStar(require('../services/milestoneService')));
                const milestoneResults = await checkAndProcessMilestones(this.app.client, userId, guildId, 'voice', {
                    channelId: newState.channel.id,
                    minutes: 1,
                    timestamp: new Date()
                });
                if (milestoneResults.length > 0) {
                    this.logger.info(`[VoiceStateUpdate] User ${newState.member?.displayName} achieved ${milestoneResults.length} milestone(s) from voice activity`);
                }
            }
            else if (oldState.channel && !newState.channel) {
                this.logExecution(`User left voice channel: ${oldState.channel.name}`, {
                    userId,
                    guildId,
                    channelId: oldState.channel.id,
                    channelName: oldState.channel.name,
                });
            }
            else if (oldState.channel && newState.channel && oldState.channel.id !== newState.channel.id) {
                this.logExecution(`User switched voice channels: ${oldState.channel.name} -> ${newState.channel.name}`, {
                    userId,
                    guildId,
                    oldChannelId: oldState.channel.id,
                    newChannelId: newState.channel.id,
                    oldChannelName: oldState.channel.name,
                    newChannelName: newState.channel.name,
                });
            }
        }
        catch (error) {
            this.logger.error('[VoiceStateUpdate] Error processing voice milestones', {
                error,
                userId,
                guildId,
                oldChannelId: oldState.channel?.id,
                newChannelId: newState.channel?.id,
            });
        }
    }
}
exports.VoiceStateUpdateEventHandler = VoiceStateUpdateEventHandler;
