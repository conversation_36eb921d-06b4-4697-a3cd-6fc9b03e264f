"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleHelpButton = void 0;
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const helpStateManager_1 = require("../utils/helpStateManager");
const help_1 = require("../commands/help");
function isUserAdmin(interaction) {
    if (!interaction.memberPermissions)
        return false;
    return interaction.memberPermissions.has(discord_js_1.PermissionFlagsBits.Administrator);
}
const handleHelpButton = async (interaction) => {
    const timestamp = new Date().toISOString();
    console.log(`[HELP BUTTON DEBUG ${timestamp}] ===== BUTTON INTERACTION START =====`);
    console.log(`[HELP BUTTON DEBUG ${timestamp}] Button interaction received from user: ${interaction.user.tag} (${interaction.user.id})`);
    if (!interaction.guild) {
        console.error(`[HELP BUTTON DEBUG ${timestamp}] ❌ No guild found in interaction`);
        throw new errorHandler_1.CommandError('This command can only be used in a server.');
    }
    const customId = interaction.customId;
    const guildId = interaction.guild.id;
    const userId = interaction.user.id;
    const userIsAdmin = isUserAdmin(interaction);
    console.log(`[HELP BUTTON DEBUG ${timestamp}] Interaction details:`);
    console.log(`[HELP BUTTON DEBUG ${timestamp}]   CustomId: "${customId}"`);
    console.log(`[HELP BUTTON DEBUG ${timestamp}]   Guild: ${guildId} (${interaction.guild.name})`);
    console.log(`[HELP BUTTON DEBUG ${timestamp}]   User: ${userId} (${interaction.user.tag})`);
    console.log(`[HELP BUTTON DEBUG ${timestamp}]   IsAdmin: ${userIsAdmin}`);
    console.log(`[HELP BUTTON DEBUG ${timestamp}] Getting help state for user: ${userId}, guild: ${guildId}`);
    let state = (0, helpStateManager_1.getHelpState)(userId, guildId);
    if (!state) {
        console.log(`[HELP BUTTON DEBUG ${timestamp}] ⚠️ No existing state found, creating new state`);
        state = {
            currentPage: 'main',
            userId,
            guildId,
            timestamp: Date.now()
        };
        (0, helpStateManager_1.setHelpState)(userId, guildId, state);
        console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ New state created and set:`, state);
    }
    else {
        console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ Existing state found:`, state);
    }
    try {
        console.log(`[HELP BUTTON DEBUG ${timestamp}] Processing button type: ${customId.split('_')[1]}`);
        if (customId === 'help_back_main') {
            console.log(`[HELP BUTTON DEBUG ${timestamp}] 🏠 Processing back to main menu`);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Updating state to main page...`);
            (0, helpStateManager_1.updateHelpState)(userId, guildId, {
                currentPage: 'main',
                categoryIndex: undefined,
                commandIndex: undefined
            });
            console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ State updated to main page`);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Creating main help embed...`);
            const embed = await (0, help_1.createMainHelpEmbed)(guildId);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ Main help embed created`);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Creating main help buttons...`);
            const buttons = await (0, help_1.createMainHelpButtons)(guildId, userIsAdmin);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ Main help buttons created (${buttons.length} rows)`);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Updating Discord interaction...`);
            await interaction.update({
                embeds: [embed],
                components: buttons
            });
            console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ Main menu update successful`);
        }
        else if (customId === 'help_getting_started') {
            console.log(`[HELP BUTTON DEBUG] Processing getting started guide`);
            (0, helpStateManager_1.updateHelpState)(userId, guildId, {
                currentPage: 'main'
            });
            console.log(`[HELP BUTTON DEBUG] Creating getting started embed...`);
            const embed = await (0, help_1.createGettingStartedEmbed)(guildId);
            console.log(`[HELP BUTTON DEBUG] Creating main help buttons...`);
            const buttons = await (0, help_1.createMainHelpButtons)(guildId, userIsAdmin);
            console.log(`[HELP BUTTON DEBUG] Updating interaction with getting started guide...`);
            await interaction.update({
                embeds: [embed],
                components: buttons
            });
            console.log(`[HELP BUTTON DEBUG] Getting started guide update successful`);
        }
        else if (customId.startsWith('help_category_')) {
            console.log(`[HELP BUTTON DEBUG ${timestamp}] 📂 Processing category button`);
            const parts = customId.split('_');
            const categoryIndex = parseInt(parts[2]);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Parsed category index: ${categoryIndex} from customId: "${customId}"`);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Validating category index - getting command data...`);
            const categories = await (0, help_1.getCommandData)(guildId);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Retrieved ${categories.length} categories for validation`);
            if (isNaN(categoryIndex) || categoryIndex < 0 || categoryIndex >= categories.length) {
                console.error(`[HELP BUTTON DEBUG ${timestamp}] ❌ Invalid category index: ${categoryIndex}, valid range: 0-${categories.length - 1}`);
                throw new errorHandler_1.CommandError(`Invalid category index: ${categoryIndex}`);
            }
            const categoryName = categories[categoryIndex]?.name || 'Unknown';
            console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ Category index valid: ${categoryIndex} ("${categoryName}")`);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Updating state to category page...`);
            (0, helpStateManager_1.updateHelpState)(userId, guildId, {
                currentPage: 'category',
                categoryIndex: categoryIndex,
                commandIndex: undefined
            });
            console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ State updated to category: ${categoryIndex}`);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Creating category embed for "${categoryName}"...`);
            const embed = await (0, help_1.createCategoryHelpEmbed)(guildId, categoryIndex, userIsAdmin);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ Category embed created`);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Creating category buttons...`);
            const buttons = await (0, help_1.createCategoryButtons)(guildId, categoryIndex, userIsAdmin);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ Category buttons created (${buttons.length} rows)`);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Updating Discord interaction...`);
            await interaction.update({
                embeds: [embed],
                components: buttons
            });
            console.log(`[HELP BUTTON DEBUG ${timestamp}] ✅ Category view update successful for "${categoryName}"`);
        }
        else if (customId.startsWith('help_command_')) {
            console.log(`[HELP BUTTON DEBUG] Processing command button`);
            const parts = customId.split('_');
            const categoryIndex = parseInt(parts[2]);
            const commandIndex = parseInt(parts[3]);
            console.log(`[HELP BUTTON DEBUG] Parsed indices - category: ${categoryIndex}, command: ${commandIndex}`);
            console.log(`[HELP BUTTON DEBUG] Getting command data for validation...`);
            const categories = await (0, help_1.getCommandData)(guildId);
            console.log(`[HELP BUTTON DEBUG] Retrieved ${categories.length} categories`);
            if (isNaN(categoryIndex) || categoryIndex < 0 || categoryIndex >= categories.length) {
                console.log(`[HELP BUTTON DEBUG] Invalid category index: ${categoryIndex}, max: ${categories.length - 1}`);
                throw new errorHandler_1.CommandError(`Invalid category index: ${categoryIndex}`);
            }
            const category = categories[categoryIndex];
            console.log(`[HELP BUTTON DEBUG] Category "${category.name}" has ${category.commands.length} commands`);
            if (isNaN(commandIndex) || commandIndex < 0 || commandIndex >= category.commands.length) {
                console.log(`[HELP BUTTON DEBUG] Invalid command index: ${commandIndex}, max: ${category.commands.length - 1}`);
                throw new errorHandler_1.CommandError(`Invalid command index: ${commandIndex} for category ${categoryIndex}`);
            }
            console.log(`[HELP BUTTON DEBUG] Command indices valid, updating state...`);
            (0, helpStateManager_1.updateHelpState)(userId, guildId, {
                currentPage: 'command',
                categoryIndex: categoryIndex,
                commandIndex: commandIndex
            });
            console.log(`[HELP BUTTON DEBUG] Creating command detail embed...`);
            const embed = await (0, help_1.createCommandDetailEmbed)(guildId, categoryIndex, commandIndex);
            console.log(`[HELP BUTTON DEBUG] Creating command buttons...`);
            const buttons = await (0, help_1.createCommandButtons)(guildId, categoryIndex, commandIndex);
            console.log(`[HELP BUTTON DEBUG] Updating interaction with command detail view...`);
            await interaction.update({
                embeds: [embed],
                components: buttons
            });
            console.log(`[HELP BUTTON DEBUG] Command detail view update successful`);
        }
        else {
            console.error(`[HELP BUTTON DEBUG ${timestamp}] ❌ Unknown button customId: "${customId}"`);
            console.log(`[HELP BUTTON DEBUG ${timestamp}] Available button patterns: help_back_main, help_getting_started, help_category_*, help_command_*_*`);
            await interaction.reply({
                content: 'Unknown help button interaction.',
                ephemeral: true
            });
        }
    }
    catch (error) {
        console.error(`[HELP BUTTON DEBUG ${timestamp}] ❌ CRITICAL ERROR in button handler:`);
        console.error(`[HELP BUTTON DEBUG ${timestamp}]   CustomId: "${customId}"`);
        console.error(`[HELP BUTTON DEBUG ${timestamp}]   User: ${userId}`);
        console.error(`[HELP BUTTON DEBUG ${timestamp}]   Guild: ${guildId}`);
        console.error(`[HELP BUTTON DEBUG ${timestamp}]   Error: ${error instanceof Error ? error.message : String(error)}`);
        if (error instanceof Error && error.stack) {
            console.error(`[HELP BUTTON DEBUG ${timestamp}]   Stack:`, error.stack);
        }
        try {
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: 'An error occurred while processing your help request. Please try again.',
                    ephemeral: true
                });
            }
            else if (interaction.deferred) {
                await interaction.editReply({
                    content: 'An error occurred while processing your help request. Please try again.'
                });
            }
        }
        catch (replyError) {
            console.error('Failed to send error message to user:', replyError);
        }
    }
};
exports.handleHelpButton = handleHelpButton;
