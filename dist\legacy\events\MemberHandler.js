"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyMemberHandler = void 0;
const starterBalanceService_1 = require("../../services/starterBalanceService");
const automessageService_1 = require("../../services/automessageService");
const userCleanupService_1 = require("../../services/userCleanupService");
const milestoneService_1 = require("../../services/milestoneService");
class LegacyMemberHandler {
    constructor(client) {
        this.client = client;
    }
    async handleMemberAdd(member) {
        try {
            await this.processJoinMessages(member);
            await this.trackJoinActivity(member);
        }
        catch (error) {
            console.error('[Member Add] Error in guildMemberAdd handler:', error);
        }
    }
    async handleMemberRemove(member) {
        try {
            const fullMember = await this.ensureFullMember(member);
            const userId = member.user?.id;
            const guildName = member.guild?.name || 'Unknown Guild';
            const displayName = fullMember?.displayName || member.user?.username || 'Unknown User';
            if (!userId) {
                console.error('[User Cleanup] No user ID available for member who left');
                return;
            }
            await this.processUserCleanup(member, userId, displayName, guildName);
        }
        catch (error) {
            console.error(`[User Cleanup] Error in guildMemberRemove handler:`, error);
        }
    }
    async handleMemberUpdate(oldMember, newMember) {
        try {
            const fullOldMember = await this.ensureFullMember(oldMember);
            if (!fullOldMember)
                return;
            const addedRoles = newMember.roles.cache.filter(role => !fullOldMember.roles.cache.has(role.id));
            const removedRoles = fullOldMember.roles.cache.filter(role => !newMember.roles.cache.has(role.id));
            if (addedRoles.size > 0) {
                await this.processAddedRoles(newMember, addedRoles);
            }
            if (removedRoles.size > 0) {
                await this.processRemovedRoles(newMember, removedRoles);
            }
        }
        catch (error) {
            console.error('[Member Update] Error in guildMemberUpdate handler:', error);
        }
    }
    async handleVoiceStateUpdate(oldState, newState) {
        try {
            if (newState.member?.user.bot)
                return;
            const userId = newState.member?.user.id;
            const guildId = newState.guild.id;
            if (!userId)
                return;
            if (!oldState.channel && newState.channel) {
                await this.trackVoiceActivity(userId, guildId, newState);
            }
        }
        catch (error) {
            console.error('[Voice State] Error in voiceStateUpdate handler:', error);
        }
    }
    async processJoinMessages(member) {
        try {
            const joinResult = await (0, automessageService_1.processJoinMessage)(member);
            if (joinResult.sent) {
                console.log(`[AutoMessage] Sent ${joinResult.templatesProcessed} join message(s) to ${member.displayName} in ${member.guild.name}`);
            }
            if (joinResult.errors.length > 0) {
                console.error(`[AutoMessage] Errors processing join messages for ${member.displayName}:`, joinResult.errors);
            }
        }
        catch (error) {
            console.error('[Member Add] Error processing join messages:', error);
        }
    }
    async trackJoinActivity(member) {
        try {
            const milestoneResults = await (0, milestoneService_1.checkAndProcessMilestones)(this.client, member.user.id, member.guild.id, 'login', { timestamp: new Date() });
            if (milestoneResults.length > 0) {
                console.log(`[Milestone] New member ${member.displayName} achieved ${milestoneResults.length} milestone(s) on join`);
            }
        }
        catch (error) {
            console.error('[Milestone] Error processing join milestones:', error);
        }
    }
    async processUserCleanup(member, userId, displayName, guildName) {
        try {
            const guildId = member.guild?.id;
            if (!guildId) {
                console.error('[User Cleanup] No guild ID available for cleanup check');
                return;
            }
            const userData = await userCleanupService_1.UserCleanupService.checkUserData(userId, guildId);
            const hasData = userData.hasUserRecord || userData.transactionCount > 0 || userData.reactionRewardCount > 0;
            if (!hasData) {
                console.log(`[User Cleanup] No data found for ${displayName} (${userId}) who left ${guildName}, skipping cleanup`);
                return;
            }
            console.log(`[User Cleanup] User ${displayName} left ${guildName} - found data: ${userData.hasUserRecord ? 'balance' : ''} ${userData.transactionCount > 0 ? `${userData.transactionCount} transactions` : ''} ${userData.reactionRewardCount > 0 ? `${userData.reactionRewardCount} reaction rewards` : ''}`.trim());
            const cleanupResult = await userCleanupService_1.UserCleanupService.cleanupUserData(member);
            if (cleanupResult.success) {
                const removedItems = [];
                if (cleanupResult.userDataRemoved)
                    removedItems.push('user balance');
                if (cleanupResult.transactionsRemoved > 0)
                    removedItems.push(`${cleanupResult.transactionsRemoved} transactions`);
                if (cleanupResult.reactionRewardsRemoved > 0)
                    removedItems.push(`${cleanupResult.reactionRewardsRemoved} reaction rewards`);
                console.log(`[User Cleanup] Successfully cleaned up data for ${displayName}: ${removedItems.join(', ')} (${cleanupResult.timeTaken}ms)`);
            }
            else {
                console.error(`[User Cleanup] Failed to clean up data for ${displayName}:`, cleanupResult.errors);
            }
        }
        catch (error) {
            console.error('[Member Handler] Error processing user cleanup:', error);
        }
    }
    async processAddedRoles(member, addedRoles) {
        for (const [roleId, role] of addedRoles) {
            try {
                const granted = await (0, starterBalanceService_1.processStarterBalance)(member, role);
                if (granted) {
                    console.log(`[Starter Balance] Granted starter balance to ${member.displayName} for role ${role.name}`);
                }
                const roleAddResult = await (0, automessageService_1.processRoleChangeMessage)(member, role, 'role_add');
                if (roleAddResult.sent) {
                    console.log(`[AutoMessage] Sent ${roleAddResult.templatesProcessed} role add message(s) to ${member.displayName} for role ${role.name}`);
                }
                if (roleAddResult.errors.length > 0) {
                    console.error(`[AutoMessage] Errors processing role add messages for ${member.displayName}:`, roleAddResult.errors);
                }
            }
            catch (error) {
                console.error(`[Member Update] Failed to process role add for ${member.displayName} and role ${role.name}:`, error);
            }
        }
    }
    async processRemovedRoles(member, removedRoles) {
        for (const [roleId, role] of removedRoles) {
            try {
                const roleRemoveResult = await (0, automessageService_1.processRoleChangeMessage)(member, role, 'role_remove');
                if (roleRemoveResult.sent) {
                    console.log(`[AutoMessage] Sent ${roleRemoveResult.templatesProcessed} role remove message(s) to ${member.displayName} for role ${role.name}`);
                }
                if (roleRemoveResult.errors.length > 0) {
                    console.error(`[AutoMessage] Errors processing role remove messages for ${member.displayName}:`, roleRemoveResult.errors);
                }
            }
            catch (error) {
                console.error(`[Member Update] Failed to process role remove for ${member.displayName} and role ${role.name}:`, error);
            }
        }
    }
    async trackVoiceActivity(userId, guildId, newState) {
        try {
            const milestoneResults = await (0, milestoneService_1.checkAndProcessMilestones)(this.client, userId, guildId, 'voice', {
                channelId: newState.channel.id,
                minutes: 1,
                timestamp: new Date()
            });
            if (milestoneResults.length > 0) {
                console.log(`[Milestone] User ${newState.member?.displayName} achieved ${milestoneResults.length} milestone(s) from voice activity`);
            }
        }
        catch (error) {
            console.error('[Milestone] Error processing voice milestones:', error);
        }
    }
    async ensureFullMember(member) {
        if (member.partial) {
            try {
                return await member.fetch();
            }
            catch (error) {
                console.error('[Member Handler] Failed to fetch member:', error);
                return null;
            }
        }
        return member;
    }
    getStats() {
        return {
            handlerType: 'LegacyMemberHandler',
            clientReady: this.client.isReady(),
            guildCount: this.client.guilds.cache.size,
        };
    }
}
exports.LegacyMemberHandler = LegacyMemberHandler;
exports.default = LegacyMemberHandler;
