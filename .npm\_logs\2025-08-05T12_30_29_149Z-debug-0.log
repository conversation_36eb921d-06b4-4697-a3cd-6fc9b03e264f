0 verbose cli /usr/local/bin/node /usr/local/bin/npm
1 info using npm@10.9.2
2 info using node@v22.17.1
3 silly config load:file:/usr/local/lib/node_modules/npm/npmrc
4 silly config load:file:/home/<USER>/.npmrc
5 silly config load:file:/home/<USER>/.npm-global/etc/npmrc
6 verbose title npm run start:prod
7 verbose argv "run" "start:prod"
8 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-08-05T12_30_29_149Z-
9 verbose logfile /home/<USER>/.npm/_logs/2025-08-05T12_30_29_149Z-debug-0.log
10 silly logfile done cleaning log files
11 verbose stack Error: command failed
11 verbose stack     at promiseSpawn (/usr/local/lib/node_modules/npm/node_modules/@npmcli/promise-spawn/lib/index.js:22:22)
11 verbose stack     at spawnWithShell (/usr/local/lib/node_modules/npm/node_modules/@npmcli/promise-spawn/lib/index.js:124:10)
11 verbose stack     at promiseSpawn (/usr/local/lib/node_modules/npm/node_modules/@npmcli/promise-spawn/lib/index.js:12:12)
11 verbose stack     at runScriptPkg (/usr/local/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/run-script-pkg.js:77:13)
11 verbose stack     at runScript (/usr/local/lib/node_modules/npm/node_modules/@npmcli/run-script/lib/run-script.js:9:12)
11 verbose stack     at #run (/usr/local/lib/node_modules/npm/lib/commands/run-script.js:131:13)
11 verbose stack     at async RunScript.exec (/usr/local/lib/node_modules/npm/lib/commands/run-script.js:40:7)
11 verbose stack     at async Npm.exec (/usr/local/lib/node_modules/npm/lib/npm.js:207:9)
11 verbose stack     at async module.exports (/usr/local/lib/node_modules/npm/lib/cli/entry.js:74:5)
12 verbose pkgid economy-bot@2.0.0
13 error path /home/<USER>
14 error command failed
15 error signal SIGTERM
16 error command sh -c NODE_ENV=production DISCLOUD=true node dist/main.js
17 verbose cwd /home/<USER>
18 verbose os Linux 5.15.0-130-generic
19 verbose node v22.17.1
20 verbose npm  v10.9.2
21 verbose exit 1
22 verbose code 1
23 error A complete log of this run can be found in: /home/<USER>/.npm/_logs/2025-08-05T12_30_29_149Z-debug-0.log
