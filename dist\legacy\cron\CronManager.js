"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyCronManager = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const taxService_1 = require("../../services/taxService");
class LegacyCronManager {
    constructor(client) {
        this.jobs = new Map();
        this.client = client;
    }
    initializeJobs() {
        this.initializeTaxCollection();
        this.initializeMilestoneTracking();
        console.log('[Legacy Cron] All cron jobs initialized');
    }
    initializeTaxCollection() {
        const job = node_cron_1.default.schedule('0 * * * *', async () => {
            console.log('[Tax Collection] Running scheduled tax collection check...');
            try {
                for (const [guildId, guild] of this.client.guilds.cache) {
                    try {
                        const result = await (0, taxService_1.processTaxCollection)(this.client, guildId);
                        if (result.totalProcessed > 0) {
                            console.log(`[Tax Collection] Guild ${guild.name}: Processed ${result.totalProcessed}, ` +
                                `Taxed ${result.totalTaxed}, Roles Removed ${result.totalRolesRemoved}`);
                            if (result.errors.length > 0) {
                                console.error(`[Tax Collection] Guild ${guild.name} errors:`, result.errors);
                            }
                        }
                    }
                    catch (error) {
                        console.error(`[Tax Collection] Failed for guild ${guild.name}:`, error);
                    }
                }
            }
            catch (error) {
                console.error('[Tax Collection] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.jobs.set('taxCollection', job);
        console.log('[Tax Collection] Cron job initialized - running every hour');
    }
    initializeMilestoneTracking() {
        const job = node_cron_1.default.schedule('0 0 * * *', async () => {
            console.log('[Milestone Tracking] Running daily milestone check...');
            try {
                for (const [guildId, guild] of this.client.guilds.cache) {
                    try {
                        console.log(`[Milestone Tracking] Daily reset processed for guild ${guild.name}`);
                    }
                    catch (error) {
                        console.error(`[Milestone Tracking] Failed for guild ${guild.name}:`, error);
                    }
                }
            }
            catch (error) {
                console.error('[Milestone Tracking] Cron job error:', error);
            }
        }, {
            timezone: "UTC"
        });
        this.jobs.set('milestoneTracking', job);
        console.log('[Milestone Tracking] Daily cron job initialized - running at midnight UTC');
    }
    stopJob(jobName) {
        const job = this.jobs.get(jobName);
        if (job) {
            job.stop();
            console.log(`[Legacy Cron] Stopped job: ${jobName}`);
            return true;
        }
        return false;
    }
    startJob(jobName) {
        const job = this.jobs.get(jobName);
        if (job) {
            job.start();
            console.log(`[Legacy Cron] Started job: ${jobName}`);
            return true;
        }
        return false;
    }
    stopAllJobs() {
        for (const [jobName, job] of this.jobs) {
            job.stop();
            console.log(`[Legacy Cron] Stopped job: ${jobName}`);
        }
    }
    startAllJobs() {
        for (const [jobName, job] of this.jobs) {
            job.start();
            console.log(`[Legacy Cron] Started job: ${jobName}`);
        }
    }
    destroyAllJobs() {
        for (const [jobName, job] of this.jobs) {
            job.destroy();
            console.log(`[Legacy Cron] Destroyed job: ${jobName}`);
        }
        this.jobs.clear();
    }
    getJobStatus(jobName) {
        const job = this.jobs.get(jobName);
        if (!job)
            return null;
        return job ? 'scheduled' : 'stopped';
    }
    getAllJobStatuses() {
        const statuses = {};
        for (const [jobName, job] of this.jobs) {
            statuses[jobName] = job ? 'scheduled' : 'stopped';
        }
        return statuses;
    }
    getJobCount() {
        return this.jobs.size;
    }
}
exports.LegacyCronManager = LegacyCronManager;
exports.default = LegacyCronManager;
