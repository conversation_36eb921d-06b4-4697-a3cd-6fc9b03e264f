"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageCreateEventHandler = void 0;
const base_1 = require("./base");
const SuggestionService_1 = require("../services/suggestion/SuggestionService");
class MessageCreateEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'messageCreate');
        this.name = 'messageCreate';
        this.suggestionService = new SuggestionService_1.SuggestionService(app);
    }
    async execute(message) {
        try {
            if (message.author.bot || !message.guild)
                return;
            await this.handleSuggestionMessage(message);
            await this.handleBotMention(message);
            if (this.isFeatureEnabled('MILESTONE_SYSTEM')) {
                await this.trackMessageActivity(message);
            }
            if (this.isFeatureEnabled('COIN_DECAY_SYSTEM')) {
                await this.trackUserActivityForCoinDecay(message);
            }
        }
        catch (error) {
            this.handleError(error, {
                messageId: message.id,
                channelId: message.channel.id,
                guildId: message.guild?.id,
                userId: message.author.id,
            });
        }
    }
    async handleSuggestionMessage(message) {
        try {
            const guildId = message.guild.id;
            const channelId = message.channel.id;
            const member = message.member;
            const isEnabled = await this.suggestionService.isSuggestionSystemEnabled(guildId);
            if (!isEnabled) {
                return;
            }
            const config = await this.suggestionService.getSuggestionConfig(guildId);
            if (!config || config.channelId !== channelId) {
                return;
            }
            const canCreateSuggestions = await this.suggestionService.canUserCreateSuggestions(guildId, member);
            if (!canCreateSuggestions) {
                const warningMessage = await message.reply({
                    content: '❌ You do not have permission to create suggestions in this channel.',
                });
                setTimeout(async () => {
                    try {
                        await message.delete();
                        await warningMessage.delete();
                    }
                    catch (error) {
                        this.logger.warn('[MessageCreate] Failed to delete unauthorized suggestion message', {
                            messageId: message.id,
                            warningMessageId: warningMessage.id,
                            error
                        });
                    }
                }, 5000);
                return;
            }
            const content = message.content.trim();
            if (!content || content.length < 10) {
                const warningMessage = await message.reply({
                    content: '❌ Suggestions must be at least 10 characters long.',
                });
                setTimeout(async () => {
                    try {
                        await message.delete();
                        await warningMessage.delete();
                    }
                    catch (error) {
                        this.logger.warn('[MessageCreate] Failed to delete short suggestion message', {
                            messageId: message.id,
                            warningMessageId: warningMessage.id,
                            error
                        });
                    }
                }, 5000);
                return;
            }
            if (content.length > 2000) {
                const warningMessage = await message.reply({
                    content: '❌ Suggestions cannot exceed 2000 characters.',
                });
                setTimeout(async () => {
                    try {
                        await message.delete();
                        await warningMessage.delete();
                    }
                    catch (error) {
                        this.logger.warn('[MessageCreate] Failed to delete long suggestion message', {
                            messageId: message.id,
                            warningMessageId: warningMessage.id,
                            error
                        });
                    }
                }, 5000);
                return;
            }
            await this.suggestionService.createSuggestion(guildId, channelId, message.author.id, content, this.app.client);
            try {
                await message.delete();
            }
            catch (error) {
                this.logger.warn('[MessageCreate] Failed to delete original suggestion message', {
                    messageId: message.id,
                    error
                });
            }
            this.logExecution('Created suggestion from message', {
                messageId: message.id,
                channelId,
                userId: message.author.id,
                contentLength: content.length
            });
        }
        catch (error) {
            this.logger.error('[MessageCreate] Error handling suggestion message', {
                error,
                messageId: message.id,
                channelId: message.channel.id,
                userId: message.author.id,
                guildId: message.guild?.id,
            });
        }
    }
    async handleBotMention(message) {
        try {
            const botMentioned = message.mentions.has(this.app.client.user);
            if (botMentioned) {
                await message.react('✅');
                this.logExecution('Bot mentioned, reacted with checkmark', {
                    messageId: message.id,
                    channelId: message.channel.id,
                    userId: message.author.id,
                });
            }
        }
        catch (error) {
            this.logger.error('[MessageCreate] Failed to react to bot mention', {
                error,
                messageId: message.id,
                channelId: message.channel.id,
            });
        }
    }
    async trackMessageActivity(message) {
        try {
            const { checkAndProcessMilestones } = await Promise.resolve().then(() => __importStar(require('../services/milestoneService')));
            const milestoneResults = await checkAndProcessMilestones(this.app.client, message.author.id, message.guild.id, 'message', {
                channelId: message.channel.id,
                messageContent: message.content,
                messageLength: message.content.length,
                timestamp: message.createdAt
            });
            if (milestoneResults.length > 0) {
                this.logger.info(`[MessageCreate] User ${message.author.tag} achieved ${milestoneResults.length} milestone(s) from message activity`);
            }
        }
        catch (error) {
            this.logger.error('[MessageCreate] Error processing message milestones', {
                error,
                messageId: message.id,
                userId: message.author.id,
                guildId: message.guild?.id,
            });
        }
    }
    async trackUserActivityForCoinDecay(message) {
        try {
            const { updateUserActivity } = await Promise.resolve().then(() => __importStar(require('../services/coinDecayService')));
            await updateUserActivity(message.author.id, message.guild.id);
        }
        catch (error) {
            this.logger.error('[MessageCreate] Error tracking user activity for coin decay', {
                error,
                messageId: message.id,
                userId: message.author.id,
                guildId: message.guild?.id,
            });
        }
    }
}
exports.MessageCreateEventHandler = MessageCreateEventHandler;
