"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("dotenv/config");
console.log('🔍 DEPLOYMENT VERIFICATION: Poll system rebuilt at 2025-08-02T20:47:31.237Z');
console.log('🎯 NEW POLL SYSTEM ACTIVE - If you see this, the rebuilt system is running');
console.log('='.repeat(60));
const application_1 = require("./core/application");
const events_1 = require("./events");
const EconomyService_1 = require("./services/economy/EconomyService");
const RoleService_1 = require("./services/role/RoleService");
const TradeService_1 = require("./services/trade/TradeService");
const DisputeService_1 = require("./services/trade/DisputeService");
const TradeBackgroundService_1 = require("./services/trade/TradeBackgroundService");
const ElectionService_1 = require("./services/election/ElectionService");
const SalaryService_1 = require("./services/salary/SalaryService");
const SuggestionService_1 = require("./services/suggestion/SuggestionService");
const PollService_1 = require("./services/poll/PollService");
const MemoryOptimizationService_1 = require("./services/MemoryOptimizationService");
const memoryAnalyzer_1 = require("./utils/memoryAnalyzer");
const memoryConstrainedMode_1 = require("./config/memoryConstrainedMode");
const environment_1 = require("./config/environment");
const logger_1 = require("./core/logger");
const serverConfigurationService_1 = __importDefault(require("./services/serverConfigurationService"));
const auto_deployment_1 = require("./utils/auto-deployment");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
console.log('🔍 DEPLOYMENT VERIFICATION: Poll system rebuilt at 2025-08-02T20:50:39.064Z');
console.log('🎯 NEW POLL SYSTEM ACTIVE - If you see this, the rebuilt system is running');
console.log('📊 Debug markers will help verify which code is actually executing');
console.log('='.repeat(60));
async function main() {
    (0, memoryConstrainedMode_1.initializeMemoryConstrainedMode)();
    const startupLogger = (0, logger_1.getLogger)();
    const isMemoryConstrained = process.env.MEMORY_CONSTRAINED_MODE === 'true';
    const delay = isMemoryConstrained ? 2000 : 5000;
    startupLogger.info(`[Main] Waiting ${delay / 1000} seconds before initialization (${isMemoryConstrained ? 'memory-constrained' : 'optimized'} mode)...`);
    await new Promise(resolve => setTimeout(resolve, delay));
    startupLogger.info('[Main] Startup delay complete. Continuing with initialization.');
    if (!environment_1.isProduction) {
        console.log('[DEBUG] process.env:', process.env);
    }
    else {
        startupLogger.info('[Main] Running in production mode - debug logging disabled');
    }
    const requiredEnv = ['BOT_TOKEN', 'MONGODB_URI'];
    const missingEnv = requiredEnv.filter((key) => !process.env[key]);
    if (missingEnv.length > 0) {
        console.error('[ENV][Missing] The following required environment variables are missing:', missingEnv);
        throw new Error(`Missing required environment variables: ${missingEnv.join(', ')}`);
    }
    await runAutoDeploymentProcess();
    const logger = (0, logger_1.getLogger)();
    try {
        logger.info('[Main] Starting Economy Bot with refactored architecture...');
        const app = await (0, application_1.startApplication)();
        await registerServices(app);
        await loadCommands(app);
        const eventManager = new events_1.EventManager(app);
        eventManager.initialize();
        await initializeServerConfigurations(app);
        global.discordClient = app.client;
        const memoryAnalyzer = memoryAnalyzer_1.MemoryAnalyzer.getInstance();
        memoryAnalyzer.logMemoryAnalysis();
        if (!environment_1.isProduction) {
            memoryAnalyzer.startMemoryMonitoring(60000);
        }
        logger.info('[Main] Economy Bot started successfully!');
        logger.info(`[Main] Bot is ready as ${app.client.user?.tag}`);
    }
    catch (error) {
        console.error('[MAIN][Raw Error]', error);
        logger.error('[Main] Failed to start application', { error });
        process.exit(1);
    }
}
async function runAutoDeploymentProcess() {
    const logger = (0, logger_1.getLogger)();
    try {
        logger.info('[Main] Starting auto-deployment process...');
        const envValidation = (0, auto_deployment_1.validateEnvironment)();
        if (!envValidation.valid) {
            logger.error('[Main] Environment validation failed', { missing: envValidation.missing });
            throw new Error(`Missing required environment variables: ${envValidation.missing.join(', ')}`);
        }
        logger.info('[Main] Environment variables validated successfully');
        const depCheck = await (0, auto_deployment_1.checkDependencies)();
        if (!depCheck.valid) {
            logger.error('[Main] Dependency check failed', { issues: depCheck.issues });
            if ((0, auto_deployment_1.isProductionEnvironment)()) {
                throw new Error(`Dependency issues: ${depCheck.issues.join(', ')}`);
            }
            else {
                logger.warn('[Main] Continuing despite dependency issues (development mode)');
            }
        }
        else {
            logger.info('[Main] Dependencies validated successfully');
        }
        const isProduction = (0, auto_deployment_1.isProductionEnvironment)();
        logger.info(`[Main] Production environment detected: ${isProduction}`);
        const buildNeeded = await (0, auto_deployment_1.needsBuild)();
        logger.info(`[Main] Build needed: ${buildNeeded}`);
        if (buildNeeded) {
            logger.info('[Main] Running TypeScript build process...');
            if (process.env.DISABLE_AUTO_DEPLOY === 'true') {
                logger.warn('[Main] Auto-deployment disabled - skipping build process');
                logger.warn('[Main] Ensure dist/ folder contains pre-built files');
            }
            else {
                try {
                    const buildSuccess = await (0, auto_deployment_1.runBuild)();
                    if (!buildSuccess) {
                        const errorMsg = 'Build process failed - cannot continue startup';
                        logger.error(`[Main] ${errorMsg}`);
                        logger.error('[Main] This may be due to Discloud environment constraints');
                        logger.error('[Main] Consider setting DISABLE_AUTO_DEPLOY=true and uploading pre-built files');
                        if (isProduction) {
                            throw new Error(errorMsg);
                        }
                        else {
                            logger.warn('[Main] Continuing despite build failure (development mode)');
                        }
                    }
                    else {
                        logger.info('[Main] Build process completed successfully');
                    }
                }
                catch (buildError) {
                    logger.error('[Main] Build process encountered an error', {
                        error: buildError.message,
                        stack: buildError.stack,
                        nodeVersion: process.version,
                        cwd: process.cwd(),
                        isDiscloud: process.cwd().includes('/home/<USER>')
                    });
                    if (isProduction) {
                        throw buildError;
                    }
                    else {
                        logger.warn('[Main] Continuing despite build error (development mode)');
                    }
                }
            }
        }
        else {
            logger.info('[Main] Build not needed, using existing compiled files');
        }
        if (process.env.CLIENT_ID && process.env.BOT_TOKEN) {
            logger.info('[Main] Deploying slash commands...');
            const commandsDeployed = await (0, auto_deployment_1.deployCommands)();
            if (!commandsDeployed) {
                const warningMsg = 'Command deployment failed';
                logger.warn(`[Main] ${warningMsg}, but continuing startup...`);
                if (isProduction) {
                    logger.warn('[Main] Command deployment failed in production - bot may not function correctly');
                }
            }
            else {
                logger.info('[Main] Slash commands deployed successfully');
            }
            logger.info('[Main] Deploying role commands...');
            const roleCommandsDeployed = await (0, auto_deployment_1.deployRoleCommands)();
            if (!roleCommandsDeployed) {
                const warningMsg = 'Role command deployment failed';
                logger.warn(`[Main] ${warningMsg}, but continuing startup...`);
                if (isProduction) {
                    logger.warn('[Main] Role command deployment failed in production - some features may not work');
                }
            }
            else {
                logger.info('[Main] Role commands deployed successfully');
            }
        }
        else {
            logger.warn('[Main] Skipping command deployment - CLIENT_ID or BOT_TOKEN not available');
        }
        logger.info('[Main] Auto-deployment process completed successfully');
    }
    catch (error) {
        logger.error('[Main] Auto-deployment process failed', {
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            isProduction: (0, auto_deployment_1.isProductionEnvironment)()
        });
        if ((0, auto_deployment_1.isProductionEnvironment)()) {
            console.error('[MAIN][CRITICAL] Auto-deployment failed in production:', error);
            throw error;
        }
        else {
            logger.warn('[Main] Continuing startup despite deployment failure (development mode)');
            console.warn('[MAIN][DEV] Auto-deployment failed, but continuing in development mode:', error instanceof Error ? error.message : String(error));
        }
    }
}
async function registerServices(app) {
    const logger = (0, logger_1.getLogger)();
    const isMemoryConstrained = process.env.MEMORY_CONSTRAINED_MODE === 'true';
    try {
        logger.info('[Main] Registering services...', { memoryConstrained: isMemoryConstrained });
        const memoryOptimizationService = new MemoryOptimizationService_1.MemoryOptimizationService(app);
        app.registerService(memoryOptimizationService, {
            autoStart: true,
            dependencies: [],
            priority: 1
        });
        const economyService = new EconomyService_1.EconomyService(app);
        app.registerService(economyService, {
            autoStart: true,
            dependencies: ['DatabaseService'],
            priority: 2
        });
        if (isMemoryConstrained) {
            logger.info('[Main] Memory-constrained mode: Registering minimal services only');
            const roleService = new RoleService_1.RoleService(app);
            app.registerService(roleService, {
                autoStart: false,
                dependencies: ['DatabaseService', 'EconomyService'],
                priority: 10
            });
            const pollService = new PollService_1.PollService(app);
            app.registerService(pollService, {
                autoStart: false,
                dependencies: ['DatabaseService', 'EconomyService'],
                priority: 11
            });
            logger.info('[Main] Non-essential services will be lazy-loaded when needed');
        }
        else {
            const roleService = new RoleService_1.RoleService(app);
            app.registerService(roleService, {
                autoStart: true,
                dependencies: ['DatabaseService', 'EconomyService'],
                priority: 3
            });
            const tradeService = new TradeService_1.TradeService(app);
            app.registerService(tradeService, {
                autoStart: true,
                dependencies: ['DatabaseService', 'EconomyService'],
                priority: 4
            });
            const disputeService = new DisputeService_1.DisputeService(app);
            app.registerService(disputeService, {
                autoStart: true,
                dependencies: ['DatabaseService', 'EconomyService', 'TradeService'],
                priority: 5
            });
            const tradeBackgroundService = new TradeBackgroundService_1.TradeBackgroundService(app);
            app.registerService(tradeBackgroundService, {
                autoStart: true,
                dependencies: ['DatabaseService', 'EconomyService', 'TradeService'],
                priority: 6
            });
            const electionService = new ElectionService_1.ElectionService(app);
            app.registerService(electionService, {
                autoStart: true,
                dependencies: ['DatabaseService', 'EconomyService'],
                priority: 7
            });
            const salaryService = new SalaryService_1.SalaryService(app);
            app.registerService(salaryService, {
                autoStart: true,
                dependencies: ['DatabaseService', 'EconomyService'],
                priority: 8
            });
            const suggestionService = new SuggestionService_1.SuggestionService(app);
            app.registerService(suggestionService, {
                autoStart: true,
                dependencies: ['DatabaseService'],
                priority: 9
            });
            const pollService = new PollService_1.PollService(app);
            app.registerService(pollService, {
                autoStart: true,
                dependencies: ['DatabaseService', 'EconomyService'],
                priority: 10
            });
        }
        logger.info('[Main] Services registered', {
            mode: isMemoryConstrained ? 'memory-constrained' : 'normal',
            immediateServices: isMemoryConstrained ? 2 : 8
        });
    }
    catch (error) {
        logger.error('[Main] Failed to register services', { error });
        throw error;
    }
}
async function loadCommands(app) {
    const logger = (0, logger_1.getLogger)();
    try {
        if (!app.client.commands) {
            app.client.commands = new Map();
        }
        let loadedCommands = 0;
        const commandsPath = path_1.default.join(__dirname, 'commands');
        const commandFiles = fs_1.default.readdirSync(commandsPath).filter(file => (file.endsWith('.js') || file.endsWith('.ts')) &&
            !file.endsWith('.d.ts') &&
            !file.includes('index') &&
            !file.includes('Manager') &&
            !file.includes('Base'));
        const skipFiles = new Set([
            'balance.js', 'balance.ts',
            'pay.js', 'pay.ts',
            'give.js', 'give.ts',
            'enhancerole.js', 'enhancerole.ts',
            'updatenames.js', 'updatenames.ts'
        ]);
        for (const file of commandFiles) {
            if (skipFiles.has(file)) {
                logger.debug(`[Main] Skipping ${file} (handled by new architecture)`);
                continue;
            }
            try {
                const filePath = path_1.default.join(commandsPath, file);
                const command = require(filePath);
                if (command.data && command.execute) {
                    app.client.commands.set(command.data.name, command);
                    loadedCommands++;
                    logger.debug(`[Main] Loaded legacy command: ${command.data.name}`);
                }
                else {
                    logger.warn(`[Main] Invalid command file: ${file}`);
                }
            }
            catch (error) {
                logger.error(`[Main] Failed to load command file: ${file}`, { error });
            }
        }
        try {
            const { commandManager } = require('./commands/CommandManager');
            commandManager.setApplicationContext(app);
            const stats = await commandManager.loadCommands();
            const newCommands = commandManager.getDiscordCommands();
            for (const [name, command] of newCommands) {
                if (app.client.commands.has(name)) {
                    logger.warn(`[Main] Skipping new architecture command '${name}' - legacy version already loaded`);
                    continue;
                }
                app.client.commands.set(name, command);
                loadedCommands++;
                logger.debug(`[Main] Loaded new architecture command: ${name}`);
            }
            logger.info(`[Main] CommandManager loaded ${stats.newArchitecture} new architecture commands`);
        }
        catch (error) {
            logger.error('[Main] Failed to load new architecture commands', { error });
        }
        logger.info(`[Main] Loaded ${loadedCommands} commands`);
    }
    catch (error) {
        logger.error('[Main] Failed to load commands', { error });
        throw error;
    }
}
async function initializeServerConfigurations(app) {
    const logger = (0, logger_1.getLogger)();
    try {
        logger.info('[Main] Initializing server configurations...');
        const guildIds = app.client.guilds.cache.map((guild) => guild.id);
        if (guildIds.length > 0) {
            logger.info(`[Main] Found ${guildIds.length} guilds, ensuring configurations exist`);
            let initializedCount = 0;
            for (const guildId of guildIds) {
                try {
                    await serverConfigurationService_1.default.getServerConfig(guildId);
                    initializedCount++;
                }
                catch (error) {
                    logger.warn(`[Main] Failed to initialize configuration for guild ${guildId}`, { error });
                }
            }
            logger.info(`[Main] Server configurations initialized for ${initializedCount}/${guildIds.length} guilds`);
        }
        else {
            logger.info('[Main] No guilds found, skipping server configuration initialization');
        }
    }
    catch (error) {
        logger.error('[Main] Failed to initialize server configurations', { error });
    }
}
process.on('uncaughtException', (error) => {
    const logger = (0, logger_1.getLogger)();
    logger.error('[Main] Uncaught exception', { error });
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    const logger = (0, logger_1.getLogger)();
    logger.error('[Main] Unhandled rejection', { reason, promise });
});
if (require.main === module) {
    main().catch((error) => {
        console.error('[MAIN][Startup Error]', error);
        if (error && error.stack) {
            console.error('[MAIN][Startup Error Stack]', error.stack);
        }
        process.exit(1);
    });
}
exports.default = main;
