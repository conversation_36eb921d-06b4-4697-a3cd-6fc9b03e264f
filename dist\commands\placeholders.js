"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('placeholders')
        .setDescription('Show available placeholders for automated messages'),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        const guildId = interaction.guild.id;
        const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, 'Available Placeholders');
        embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.INFO} **Message Placeholders**\n\nUse these placeholders in your automated message titles, descriptions, and fields. They will be automatically replaced with actual values when the message is sent.`)
            .addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.USER} User Placeholders`,
            value: `\`{user}\` - User's display name\n` +
                `\`{user.mention}\` - Mentions the user (sends a ping notification)\n` +
                `\`{user.username}\` - User's username\n` +
                `\`{user.displayName}\` - User's display name in server\n` +
                `\`{user.id}\` - User's Discord ID\n` +
                `\`{user.tag}\` - User's full tag (username#discriminator)`,
            inline: false
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.GUILD} Server Placeholders`,
            value: `\`{server}\` - Server name\n` +
                `\`{server.name}\` - Server name\n` +
                `\`{server.id}\` - Server ID\n` +
                `\`{server.memberCount}\` - Total member count`,
            inline: false
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.ROLE} Role Placeholders`,
            value: `\`{role}\` - Role name (for role_add/role_remove triggers)\n` +
                `\`{role.name}\` - Role name\n` +
                `\`{role.id}\` - Role ID\n` +
                `\`{role.mention}\` - Mentions the role (@rolename)\n\n` +
                `*Note: Role placeholders only work with role_add and role_remove triggers*`,
            inline: false
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Date/Time Placeholders`,
            value: `\`{date}\` - Current date (MM/DD/YYYY)\n` +
                `\`{time}\` - Current time (HH:MM:SS AM/PM)\n` +
                `\`{datetime}\` - Current date and time`,
            inline: false
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} Usage Examples`,
            value: `**Welcome Message:**\n` +
                `Title: \`Welcome to {server}!\`\n` +
                `Description: \`Hello {user}! You are member #{server.memberCount}!\`\n\n` +
                `**Role Added:**\n` +
                `Title: \`New Role: {role}\`\n` +
                `Description: \`{user.mention} has been given the {role.mention} role!\`\n\n` +
                `**With Timestamp:**\n` +
                `Description: \`Welcome {user}! Joined on {datetime}\`\n\n` +
                `**Mention Example:**\n` +
                `\`{user.mention}\` → <@123456789012345678>`,
            inline: false
        }, {
            name: `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} Related Commands`,
            value: `• \`/automessage action:create\` - Create a new automated message\n` +
                `• \`/automessage action:list\` - View all your automated messages\n` +
                `• \`/automessage action:test\` - Preview how a message will look`,
            inline: false
        });
        await interaction.reply({ embeds: [embed], ephemeral: false });
    })
};
