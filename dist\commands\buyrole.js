"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const errorHandler_1 = require("../utils/errorHandler");
const roleResolver_1 = require("../utils/roleResolver");
const embedBuilder_1 = require("../utils/embedBuilder");
const economyService_1 = require("../services/economyService");
const User_2 = __importDefault(require("../models/User"));
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
const dynamicPricingService_1 = __importDefault(require("../services/dynamicPricingService"));
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('buyrole')
        .setDescription('Purchase an available role achievement (manual backup)')
        .addRoleOption(option => option
        .setName('role')
        .setDescription('The role to purchase')
        .setRequired(true)),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        const role = interaction.options.getRole('role', true);
        const discordId = interaction.user.id;
        const guildId = interaction.guild?.id;
        if (!guildId) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('Guild information not available.');
        }
        try {
            const fullRole = interaction.guild.roles.cache.get(role.id) || await interaction.guild.roles.fetch(role.id);
            if (!fullRole) {
                throw new errorHandler_1.ValidationError('Role not found in guild');
            }
            await (0, roleResolver_1.validateRolePermissions)(interaction.guild, fullRole);
            const roleForSale = await User_1.RoleForSale.findOne({
                roleId: fullRole.id,
                guildId: guildId
            });
            if (!roleForSale) {
                throw new errorHandler_1.ValidationError(`Role "${fullRole.name}" is not available for purchase. Use \`/roles\` to see available role achievements.`);
            }
            const member = await interaction.guild.members.fetch(discordId);
            if (!member) {
                throw new errorHandler_1.ValidationError('Member not found in guild');
            }
            if (member.roles.cache.has(fullRole.id)) {
                throw new errorHandler_1.ValidationError(`You already have the role "${fullRole.name}".`);
            }
            const user = await User_2.default.findOne({ discordId, guildId });
            const currentBalance = user?.balance || 0;
            const priceInfo = await dynamicPricingService_1.default.calculatePrice(roleForSale, guildId);
            const actualPrice = priceInfo.actualPrice;
            if (currentBalance < actualPrice) {
                const coinName = await configurableConstants_1.default.getCoinName(guildId);
                const neededAmount = await (0, embedBuilder_1.formatServerCoins)(guildId, actualPrice - currentBalance);
                const currentBalanceFormatted = await (0, embedBuilder_1.formatServerCoins)(guildId, currentBalance);
                const priceFormatted = await (0, embedBuilder_1.formatServerCoins)(guildId, actualPrice);
                let priceDisplay = priceFormatted;
                if (priceInfo.isPercentageBased) {
                    priceDisplay += ` (${priceInfo.percentageValue}% of economy)`;
                }
                throw new errorHandler_1.ValidationError(`Insufficient funds to purchase "${fullRole.name}".\n\n` +
                    `**Required:** ${priceDisplay}\n` +
                    `**Your Balance:** ${currentBalanceFormatted}\n` +
                    `**Need:** ${neededAmount} more ${coinName}`);
            }
            await (0, economyService_1.adjustBalance)(discordId, guildId, -actualPrice, 'role_achievement', `Manual role purchase: ${fullRole.name} (${priceInfo.isPercentageBased ? `${priceInfo.percentageValue}% of economy` : 'fixed price'})`, interaction.client);
            await member.roles.add(fullRole);
            const updatedUser = await User_2.default.findOne({ discordId, guildId });
            const newBalance = updatedUser?.balance || 0;
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Role Purchased Successfully!');
            const coinName = await configurableConstants_1.default.getCoinName(guildId);
            const priceFormatted = await (0, embedBuilder_1.formatServerCoins)(guildId, actualPrice);
            const newBalanceFormatted = await (0, embedBuilder_1.formatServerCoins)(guildId, newBalance);
            let priceDisplay = priceFormatted;
            if (priceInfo.isPercentageBased) {
                priceDisplay += ` (${priceInfo.percentageValue}% of economy)`;
            }
            embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **${fullRole.name}** role has been purchased and assigned!\n\n` +
                `${embedBuilder_1.EMOJIS.ECONOMY.MONEY} **Cost:** ${priceDisplay}\n` +
                `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **New Balance:** ${newBalanceFormatted}`);
            if (roleForSale.description) {
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Role Description`,
                    value: roleForSale.description,
                    inline: false
                });
            }
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} What's Next?`,
                value: [
                    `• Use \`/balance\` to check your current ${coinName}`,
                    `• Use \`/roles\` to see other available achievements`,
                    `• Use \`/history\` to view your transaction history`
                ].join('\n'),
                inline: false
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(`Failed to purchase role: ${error.message}`);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to purchase role.');
            }
        }
    })
};
