"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateSuggestionSystem = validateSuggestionSystem;
const SuggestionService_1 = require("../services/suggestion/SuggestionService");
const SuggestionConfig_1 = require("../models/SuggestionConfig");
const Suggestion_1 = require("../models/Suggestion");
const logger_1 = require("../core/logger");
const mongoose_1 = __importDefault(require("mongoose"));
const logger = (0, logger_1.getLogger)();
class SuggestionSystemValidator {
    constructor() {
        this.results = [];
    }
    async initialize() {
        logger.info('[Validator] Initializing database connection for validation...');
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/economy-bot';
        await mongoose_1.default.connect(mongoUri);
        this.suggestionService = new SuggestionService_1.SuggestionService();
        logger.info('[Validator] Validation environment initialized successfully');
    }
    async runValidation() {
        logger.info('[Validator] Starting suggestion system validation...');
        await this.validateModelsRegistration();
        await this.validateServiceRegistration();
        await this.validateDatabaseConnection();
        await this.validateConfigurationOperations();
        await this.validateSuggestionOperations();
        await this.validateCleanupSystem();
        await this.validateIndexes();
        logger.info('[Validator] Validation completed');
        return this.results;
    }
    async validateModelsRegistration() {
        try {
            logger.info('[Validator] Validating model registration...');
            const testConfig = new SuggestionConfig_1.SuggestionConfig({
                guildId: '123456789012345678',
                enabled: true,
                channelId: '987654321098765432',
                allowedRoles: []
            });
            await testConfig.validate();
            const testSuggestion = new Suggestion_1.Suggestion({
                suggestionId: 'test-id',
                guildId: '123456789012345678',
                channelId: '987654321098765432',
                messageId: '111222333444555666',
                authorId: '777888999000111222',
                content: 'Test suggestion content',
                votes: [],
                upvoteCount: 0,
                downvoteCount: 0
            });
            await testSuggestion.validate();
            this.addResult('Models Registration', true, 'All models are properly registered and validated');
        }
        catch (error) {
            this.addResult('Models Registration', false, error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async validateServiceRegistration() {
        try {
            logger.info('[Validator] Validating service registration...');
            if (!this.suggestionService) {
                throw new Error('SuggestionService not found in application services');
            }
            if (this.suggestionService.name !== 'SuggestionService') {
                throw new Error('SuggestionService has incorrect name');
            }
            if (typeof this.suggestionService.configureSuggestionSystem !== 'function') {
                throw new Error('configureSuggestionSystem method not available');
            }
            if (typeof this.suggestionService.createSuggestion !== 'function') {
                throw new Error('createSuggestion method not available');
            }
            this.addResult('Service Registration', true, 'SuggestionService is properly registered and functional');
        }
        catch (error) {
            this.addResult('Service Registration', false, error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async validateDatabaseConnection() {
        try {
            logger.info('[Validator] Validating database connection...');
            const testGuildId = 'validation-test-guild';
            await SuggestionConfig_1.SuggestionConfig.deleteMany({ guildId: testGuildId });
            await Suggestion_1.Suggestion.deleteMany({ guildId: testGuildId });
            const config = new SuggestionConfig_1.SuggestionConfig({
                guildId: testGuildId,
                enabled: true,
                channelId: '123456789012345678',
                allowedRoles: []
            });
            await config.save();
            const savedConfig = await SuggestionConfig_1.SuggestionConfig.findOne({ guildId: testGuildId });
            if (!savedConfig) {
                throw new Error('Failed to read saved configuration');
            }
            savedConfig.enabled = false;
            await savedConfig.save();
            await SuggestionConfig_1.SuggestionConfig.deleteOne({ guildId: testGuildId });
            this.addResult('Database Connection', true, 'Database operations are working correctly');
        }
        catch (error) {
            this.addResult('Database Connection', false, error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async validateConfigurationOperations() {
        try {
            logger.info('[Validator] Validating configuration operations...');
            const testGuildId = 'config-test-guild';
            const testChannelId = '123456789012345678';
            const testRoleId = '987654321098765432';
            await SuggestionConfig_1.SuggestionConfig.deleteMany({ guildId: testGuildId });
            const config = await this.suggestionService.configureSuggestionSystem({
                guildId: testGuildId,
                enabled: true,
                channelId: testChannelId,
                allowedRoles: [testRoleId]
            });
            if (!config || config.guildId !== testGuildId) {
                throw new Error('Configuration creation failed');
            }
            const retrievedConfig = await this.suggestionService.getSuggestionConfig(testGuildId);
            if (!retrievedConfig || !retrievedConfig.enabled) {
                throw new Error('Configuration retrieval failed');
            }
            const isEnabled = await this.suggestionService.isSuggestionSystemEnabled(testGuildId);
            if (!isEnabled) {
                throw new Error('System enabled check failed');
            }
            await SuggestionConfig_1.SuggestionConfig.deleteMany({ guildId: testGuildId });
            this.addResult('Configuration Operations', true, 'All configuration operations work correctly');
        }
        catch (error) {
            this.addResult('Configuration Operations', false, error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async validateSuggestionOperations() {
        try {
            logger.info('[Validator] Validating suggestion operations...');
            const testGuildId = 'suggestion-test-guild';
            const testChannelId = '123456789012345678';
            const testUserId = '987654321098765432';
            await SuggestionConfig_1.SuggestionConfig.deleteMany({ guildId: testGuildId });
            await Suggestion_1.Suggestion.deleteMany({ guildId: testGuildId });
            await this.suggestionService.configureSuggestionSystem({
                guildId: testGuildId,
                enabled: true,
                channelId: testChannelId,
                allowedRoles: []
            });
            const mockClient = {
                channels: {
                    fetch: () => Promise.resolve({
                        send: () => Promise.resolve({ id: 'mock-message-id' })
                    })
                }
            };
            const suggestion = await this.suggestionService.createSuggestion(testGuildId, testChannelId, testUserId, 'Test suggestion for validation', mockClient);
            if (!suggestion || !suggestion.suggestionId) {
                throw new Error('Suggestion creation failed');
            }
            const retrievedSuggestion = await this.suggestionService.getSuggestion(suggestion.suggestionId);
            if (!retrievedSuggestion) {
                throw new Error('Suggestion retrieval failed');
            }
            mockClient.channels.fetch = () => Promise.resolve({
                messages: {
                    fetch: () => Promise.resolve({
                        edit: () => Promise.resolve()
                    })
                }
            });
            const voteResult = await this.suggestionService.voteOnSuggestion(suggestion.suggestionId, 'voter123', 'upvote', mockClient);
            if (!voteResult.success || voteResult.upvoteCount !== 1) {
                throw new Error('Voting operation failed');
            }
            const editedSuggestion = await this.suggestionService.editSuggestion(suggestion.suggestionId, 'Updated test suggestion', mockClient);
            if (!editedSuggestion.isEdited || editedSuggestion.content !== 'Updated test suggestion') {
                throw new Error('Suggestion editing failed');
            }
            const deleted = await this.suggestionService.deleteSuggestion(suggestion.suggestionId, mockClient);
            if (!deleted) {
                throw new Error('Suggestion deletion failed');
            }
            await SuggestionConfig_1.SuggestionConfig.deleteMany({ guildId: testGuildId });
            await Suggestion_1.Suggestion.deleteMany({ guildId: testGuildId });
            this.addResult('Suggestion Operations', true, 'All suggestion operations work correctly');
        }
        catch (error) {
            this.addResult('Suggestion Operations', false, error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async validateCleanupSystem() {
        try {
            logger.info('[Validator] Validating cleanup system...');
            const stats = await this.suggestionService.getCleanupStats();
            if (typeof stats.totalSuggestions !== 'number' || typeof stats.expiredSuggestions !== 'number') {
                throw new Error('Cleanup stats format is incorrect');
            }
            const cleanupResult = await this.suggestionService.triggerCleanup();
            if (!cleanupResult || typeof cleanupResult.suggestionsProcessed !== 'number') {
                throw new Error('Manual cleanup trigger failed');
            }
            this.addResult('Cleanup System', true, 'Cleanup system is functional');
        }
        catch (error) {
            this.addResult('Cleanup System', false, error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async validateIndexes() {
        try {
            logger.info('[Validator] Validating database indexes...');
            const suggestionIndexes = await Suggestion_1.Suggestion.collection.getIndexes();
            const configIndexes = await SuggestionConfig_1.SuggestionConfig.collection.getIndexes();
            if (Object.keys(suggestionIndexes).length < 2) {
                throw new Error('Suggestion collection missing indexes');
            }
            if (Object.keys(configIndexes).length < 2) {
                throw new Error('SuggestionConfig collection missing indexes');
            }
            const hasTTLIndex = Object.values(suggestionIndexes).some((index) => index.expireAfterSeconds !== undefined);
            if (!hasTTLIndex) {
                throw new Error('Suggestion collection missing TTL index');
            }
            this.addResult('Database Indexes', true, 'Database indexes are properly configured');
        }
        catch (error) {
            this.addResult('Database Indexes', false, error instanceof Error ? error.message : 'Unknown error');
        }
    }
    addResult(test, passed, details, error) {
        this.results.push({
            test,
            passed,
            details,
            error
        });
        const status = passed ? '✅ PASS' : '❌ FAIL';
        logger.info(`[Validator] ${status}: ${test} - ${details}`);
        if (error) {
            logger.error(`[Validator] Error details: ${error}`);
        }
    }
    async cleanup() {
        try {
            await mongoose_1.default.disconnect();
            logger.info('[Validator] Database connection closed');
        }
        catch (error) {
            logger.error('[Validator] Error during cleanup:', error);
        }
    }
}
async function validateSuggestionSystem() {
    const validator = new SuggestionSystemValidator();
    try {
        await validator.initialize();
        const results = await validator.runValidation();
        const passedTests = results.filter(r => r.passed).length;
        const totalTests = results.length;
        logger.info(`[Validator] Validation Summary: ${passedTests}/${totalTests} tests passed`);
        if (passedTests === totalTests) {
            logger.info('[Validator] 🎉 All tests passed! Suggestion system is ready for production.');
        }
        else {
            logger.error('[Validator] ⚠️ Some tests failed. Please review the results before deploying.');
        }
        return results;
    }
    catch (error) {
        logger.error('[Validator] Validation failed with error:', error);
        throw error;
    }
    finally {
        await validator.cleanup();
    }
}
if (require.main === module) {
    validateSuggestionSystem()
        .then((results) => {
        console.log('\n=== VALIDATION RESULTS ===');
        results.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.test}: ${result.details}`);
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
        });
        const passedCount = results.filter(r => r.passed).length;
        console.log(`\nSummary: ${passedCount}/${results.length} tests passed`);
        process.exit(passedCount === results.length ? 0 : 1);
    })
        .catch((error) => {
        console.error('Validation failed:', error);
        process.exit(1);
    });
}
