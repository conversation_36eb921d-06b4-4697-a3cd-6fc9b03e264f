# Quick Fix Commands - Banker Role Deployment

## Immediate Resolution Steps

Based on your project's package.json, here are the exact commands to fix both issues:

### 1. Build the TypeScript Code

```bash
# Navigate to your project directory
cd "c:\Users\<USER>\Desktop\DOCUMENTS NON ATTACHED WITH ONE DRIVE\Programming\The Banker"

# Build the TypeScript code
npm run build
```

**Expected Output:**
```
> economy-bot@2.0.0 build
> tsc

# Should complete without errors
```

### 2. Verify Build Success

```bash
# Check if new files were created
dir dist\commands\admin\BankersCommand.js
dir dist\utils\permissions\BankerPermissions.js

# Verify bot restriction was removed from fine.js
findstr /i "cannot fine a bot" dist\commands\fine.js
```

**Expected Results:**
- Both `.js` files should exist
- `findstr` should return nothing (no matches found)

### 3. Stop Current Bot Process

```bash
# Find running Node processes
tasklist /fi "imagename eq node.exe"

# Kill the bot process (replace PID with actual process ID)
taskkill /pid <PID> /f

# Or if using PM2
pm2 stop all
pm2 delete all
```

### 4. Deploy Slash Commands to Discord

```bash
# Register the new /bankers command with Discord API
npm run deploy-commands
```

**Expected Output:**
```
Successfully registered application commands.
✅ Registered command: bankers
✅ Registered command: give  
✅ Registered command: fine
```

### 5. Start the Bot

```bash
# Option A: Production mode
npm run start

# Option B: Development mode (recommended for testing)
npm run dev

# Option C: If using PM2
pm2 start dist/main.js --name "banker-bot"
```

### 6. Verify in Discord

**Test 1: Check /bankers command**
- Type `/bankers` in Discord
- Should see autocomplete with subcommands: `set`, `remove`, `view`

**Test 2: Test bot fining**
```
/fine @SomeBot 1
```
- Should work without "cannot fine a bot" error

## Alternative Build Commands

If `npm run build` fails, try these alternatives:

```bash
# Direct TypeScript compilation
npx tsc

# Clean build
npm run clean
npm run build

# Production build
npm run build:prod
```

## Troubleshooting Common Issues

### Issue: "tsc is not recognized"
```bash
# Install TypeScript globally
npm install -g typescript

# Or use the local version
npx tsc
```

### Issue: Build errors about missing modules
```bash
# Install dependencies
npm install

# Clear cache and reinstall
npm cache clean --force
rmdir /s node_modules
del package-lock.json
npm install
```

### Issue: Permission denied
```bash
# Run as administrator
# Right-click Command Prompt -> "Run as administrator"
npm run build
```

### Issue: Bot doesn't restart
```bash
# Force kill all Node processes
taskkill /f /im node.exe

# Wait 5 seconds, then restart
npm run dev
```

## Verification Checklist

After running the commands above:

- [ ] `dist/commands/admin/BankersCommand.js` exists
- [ ] `dist/utils/permissions/BankerPermissions.js` exists  
- [ ] `findstr "cannot fine a bot" dist\commands\fine.js` returns no results
- [ ] Bot console shows "Successfully registered application commands"
- [ ] `/bankers` appears in Discord autocomplete
- [ ] `/fine @BotName 1` works without bot restriction error

## Quick Test Sequence

Once deployed, test in this order:

```
1. /bankers view
   → Should show "No banker role configured"

2. /bankers set @YourTestRole
   → Should confirm role was set

3. /bankers view  
   → Should show the configured role

4. /give @TestUser 10
   → Should work if you have admin or banker role

5. /fine @SomeBot 5
   → Should work without bot restriction error

6. /bankers remove
   → Should clear the banker role
```

## Emergency Rollback

If something goes wrong:

```bash
# Stop the bot
taskkill /f /im node.exe

# Revert to previous commit
git log --oneline -5
git checkout HEAD~1

# Rebuild and restart
npm run build
npm run deploy-commands
npm run start
```

## Success Indicators

You'll know it's working when you see:

**Console Output:**
```
✅ BankersCommand loaded successfully
✅ Banker permissions system initialized
✅ Command registered: bankers (admin)
✅ Bot restriction removed from fine command
```

**Discord Behavior:**
- `/bankers` command available to admins
- `/fine @BotName amount` works without errors
- Users with banker role can use `/give` and `/fine`

Run these commands in order, and both issues should be resolved!
