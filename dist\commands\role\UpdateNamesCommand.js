"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateNamesCommand = void 0;
const discord_js_1 = require("discord.js");
const BaseCommand_1 = require("../base/BaseCommand");
const User_1 = require("../../models/User");
const errorHandler_1 = require("../../utils/errorHandler");
const embedBuilder_1 = require("../../utils/embedBuilder");
class UpdateNamesCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'updatenames',
            description: 'Ensure all members have correct prefixes based on their current roles',
            category: BaseCommand_1.CommandCategory.ROLE,
            adminOnly: false,
            guildOnly: true,
            cooldown: 30,
            requiredPermissions: ['ManageNicknames'],
        });
    }
    customizeCommand(command) {
    }
    async executeCommand(context) {
        const { interaction, guild, member } = context;
        await interaction.deferReply();
        if (!guild || !member) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        const botMember = await guild.members.fetch(interaction.client.user.id);
        if (!botMember.permissions.has(discord_js_1.PermissionFlagsBits.ManageNicknames)) {
            throw new errorHandler_1.PermissionError('I need the "Manage Nicknames" permission to use this command.');
        }
        try {
            const rolePrefixes = await User_1.RolePrefix.find({ guildId: guild.id });
            if (rolePrefixes.length === 0) {
                const embed = (0, embedBuilder_1.createErrorEmbed)('No Role Prefixes Found', 'No role prefixes have been configured for this server. Use `/enhancerole` to set up role prefixes first.');
                await interaction.editReply({ embeds: [embed] });
                return;
            }
            await guild.members.fetch();
            const allMembers = guild.members.cache;
            let checkedCount = 0;
            let updatedCount = 0;
            const errors = [];
            for (const [, guildMember] of allMembers) {
                if (guildMember.user.bot)
                    continue;
                try {
                    const prefixInfo = await this.analyzeMemberPrefix(guildMember, rolePrefixes, guild);
                    checkedCount++;
                    if (prefixInfo && prefixInfo.needsUpdate) {
                        const result = await this.updateMemberNickname(prefixInfo, botMember);
                        if (result.updated) {
                            updatedCount++;
                        }
                    }
                }
                catch (error) {
                    errors.push(`${guildMember.displayName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
            const guildId = interaction.guild.id;
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, '✅ Name Update Complete');
            embed.setDescription(`Checked **${checkedCount}** members, updated **${updatedCount}** nicknames`);
            embed.addFields({
                name: '📊 Statistics',
                value: [
                    `👥 Members checked: ${checkedCount}`,
                    `✏️ Nicknames updated: ${updatedCount}`,
                    `🏷️ Role prefixes configured: ${rolePrefixes.length}`
                ].join('\n'),
                inline: false
            });
            if (errors.length > 0 && errors.length <= 5) {
                embed.addFields({
                    name: '❌ Errors',
                    value: errors.slice(0, 5).join('\n'),
                    inline: false
                });
            }
            else if (errors.length > 5) {
                embed.addFields({
                    name: '❌ Errors',
                    value: `${errors.length} errors occurred. Check logs for details.`,
                    inline: false
                });
            }
            await interaction.editReply({ embeds: [embed] });
        }
        catch (error) {
            this.logger.error('Failed to update names', { error, guildId: guild.id });
            throw new errorHandler_1.DatabaseError(`Failed to update names: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async analyzeMemberPrefix(member, rolePrefixes, guild) {
        const memberRolesWithPrefixes = rolePrefixes.filter(rp => member.roles.cache.has(rp.roleId));
        if (memberRolesWithPrefixes.length === 0) {
            const currentNickname = member.nickname || member.user.username;
            const hasAnyPrefix = rolePrefixes.some(rp => currentNickname.startsWith(rp.prefix));
            if (hasAnyPrefix && member.nickname) {
                return {
                    member,
                    expectedPrefix: '',
                    currentNickname,
                    baseUsername: member.user.username,
                    needsUpdate: true,
                    highestRole: member.roles.highest
                };
            }
            return null;
        }
        let highestRole = null;
        let highestRolePrefix = '';
        for (const rolePrefix of memberRolesWithPrefixes) {
            const role = await guild.roles.fetch(rolePrefix.roleId);
            if (role && (!highestRole || role.position > highestRole.position)) {
                highestRole = role;
                highestRolePrefix = rolePrefix.prefix;
            }
        }
        if (!highestRole) {
            return null;
        }
        const currentNickname = member.nickname || member.user.username;
        let baseUsername = currentNickname;
        let prefixRemoved = true;
        while (prefixRemoved) {
            prefixRemoved = false;
            for (const rp of rolePrefixes) {
                if (baseUsername.startsWith(rp.prefix)) {
                    baseUsername = baseUsername.substring(rp.prefix.length);
                    prefixRemoved = true;
                    break;
                }
            }
        }
        if (baseUsername.trim() === '') {
            baseUsername = member.user.username;
        }
        const expectedNickname = `${highestRolePrefix}${baseUsername}`;
        const needsUpdate = currentNickname !== expectedNickname;
        return {
            member,
            expectedPrefix: highestRolePrefix,
            currentNickname,
            baseUsername,
            needsUpdate,
            highestRole
        };
    }
    async updateMemberNickname(prefixInfo, botMember) {
        const { member, expectedPrefix, baseUsername } = prefixInfo;
        if (member.roles.highest.position >= botMember.roles.highest.position && member.id !== member.guild.ownerId) {
            return { updated: false, reason: 'Higher role hierarchy' };
        }
        let newNickname = null;
        if (expectedPrefix === '') {
            newNickname = null;
        }
        else {
            newNickname = `${expectedPrefix}${baseUsername}`;
            if (newNickname.length > 32) {
                const maxNameLength = 32 - expectedPrefix.length;
                const truncatedName = baseUsername.substring(0, maxNameLength);
                newNickname = `${expectedPrefix}${truncatedName}`;
            }
        }
        try {
            await member.setNickname(newNickname, 'Name update: role prefix sync');
            return { updated: true };
        }
        catch (error) {
            throw new Error(`Failed to update nickname: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
exports.UpdateNamesCommand = UpdateNamesCommand;
