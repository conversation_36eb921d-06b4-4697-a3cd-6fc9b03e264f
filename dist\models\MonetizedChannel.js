"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonetizedChannel = void 0;
const mongoose_1 = require("mongoose");
const monetizedChannelSchema = new mongoose_1.Schema({
    channelId: {
        type: String,
        required: [true, 'Channel ID is required'],
        unique: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0);
            },
            message: 'Channel ID cannot be empty'
        }
    },
    enabled: {
        type: Boolean,
        default: true,
        required: true
    }
}, {
    timestamps: true
});
monetizedChannelSchema.index({ channelId: 1 });
exports.MonetizedChannel = (0, mongoose_1.model)('MonetizedChannel', monetizedChannelSchema);
