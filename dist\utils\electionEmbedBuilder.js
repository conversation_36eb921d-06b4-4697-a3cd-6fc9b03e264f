"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createElectionEmbed = createElectionEmbed;
exports.createElectionButtons = createElectionButtons;
exports.createElectionResultsEmbed = createElectionResultsEmbed;
exports.createCandidateConfirmationEmbed = createCandidateConfirmationEmbed;
exports.createVoteConfirmationEmbed = createVoteConfirmationEmbed;
exports.createWinnerAnnouncementEmbed = createWinnerAnnouncementEmbed;
exports.createElectionArchiveEmbed = createElectionArchiveEmbed;
exports.createElectionErrorEmbed = createElectionErrorEmbed;
const discord_js_1 = require("discord.js");
const embedBuilder_1 = require("./embedBuilder");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
async function createElectionEmbed(election, candidates, stats) {
    const embedColor = await configurableConstants_1.default.getEmbedColor(election.guildId);
    const embed = new discord_js_1.EmbedBuilder()
        .setColor(embedColor)
        .setTitle(`🗳️ ${election.title}`)
        .setTimestamp();
    if (election.description) {
        embed.setDescription(election.description);
    }
    const formattedTotalWeight = await (0, embedBuilder_1.formatServerCoins)(election.guildId, stats.totalVoteWeight);
    embed.addFields({
        name: '📊 Election Statistics',
        value: [
            `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **Total Vote Weight:** ${formattedTotalWeight}`,
            `👥 **Total Votes:** ${stats.totalVotes}`,
            `🏆 **Candidates:** ${stats.activeCandidates}`,
            `📅 **Started:** <t:${Math.floor(election.createdAt.getTime() / 1000)}:R>`
        ].join('\n'),
        inline: false
    });
    if (candidates.length > 0) {
        const candidateList = await Promise.all(candidates.slice(0, 10).map(async (candidate, index) => {
            const rankEmoji = getRankEmoji(candidate.rank);
            const percentage = candidate.percentage.toFixed(1);
            const voteDisplay = election.showVoteWeights
                ? `${await (0, embedBuilder_1.formatServerCoins)(election.guildId, candidate.voteWeight)} (${percentage}%)`
                : `${candidate.voteCount} votes`;
            return `${rankEmoji} **${candidate.displayName}** - ${voteDisplay}`;
        }));
        const candidateListText = candidateList.join('\n');
        embed.addFields({
            name: '🏆 Candidates & Results',
            value: candidateListText || 'No candidates yet. Be the first to join!',
            inline: false
        });
        if (candidates.length > 10) {
            embed.addFields({
                name: '📝 Note',
                value: `Showing top 10 candidates. ${candidates.length - 10} more candidates not displayed.`,
                inline: false
            });
        }
    }
    else {
        embed.addFields({
            name: '🏆 Candidates & Results',
            value: 'No candidates specified for this election.',
            inline: false
        });
    }
    embed.addFields({
        name: '📋 How to Vote',
        value: [
            '🗳️ **Vote:** Click a candidate button to cast your vote',
            '💰 **Vote Weight:** Your vote power equals your coin balance',
            election.allowMultipleVotes ? '🔄 **Change Vote:** You can change your vote anytime' : '⚠️ **One Vote:** You cannot change your vote once cast'
        ].join('\n'),
        inline: false
    });
    embed.setFooter({
        text: `Election ID: ${election.electionId} | Status: ${election.status}`
    });
    return embed;
}
function createElectionButtons(electionId, candidates, currentUserId, userVote, isUserCandidate, canEndElection) {
    const rows = [];
    if (candidates.length > 0) {
        const candidateButtons = candidates.slice(0, 20).map((candidate, index) => {
            const isVotedFor = userVote === candidate.discordId;
            const style = isVotedFor ? discord_js_1.ButtonStyle.Success : discord_js_1.ButtonStyle.Secondary;
            const emoji = getRankEmoji(candidate.rank);
            return new discord_js_1.ButtonBuilder()
                .setCustomId(`election_vote_${electionId}_${candidate.discordId}`)
                .setLabel(`${candidate.displayName}`)
                .setEmoji(emoji)
                .setStyle(style);
        });
        for (let i = 0; i < candidateButtons.length; i += 5) {
            const row = new discord_js_1.ActionRowBuilder()
                .addComponents(candidateButtons.slice(i, i + 5));
            rows.push(row);
        }
    }
    const actionButtons = [];
    if (isUserCandidate) {
        actionButtons.push(new discord_js_1.ButtonBuilder()
            .setCustomId(`election_resign_${electionId}`)
            .setLabel('Resign as Candidate')
            .setEmoji('🚪')
            .setStyle(discord_js_1.ButtonStyle.Danger));
    }
    actionButtons.push(new discord_js_1.ButtonBuilder()
        .setCustomId(`election_refresh_${electionId}`)
        .setLabel('Refresh')
        .setEmoji('🔄')
        .setStyle(discord_js_1.ButtonStyle.Secondary));
    if (actionButtons.length > 0) {
        const actionRow = new discord_js_1.ActionRowBuilder()
            .addComponents(actionButtons);
        rows.push(actionRow);
    }
    if (canEndElection) {
        const adminButtons = [
            new discord_js_1.ButtonBuilder()
                .setCustomId(`election_end_${electionId}`)
                .setLabel('End Election')
                .setEmoji('🏁')
                .setStyle(discord_js_1.ButtonStyle.Danger)
        ];
        const adminRow = new discord_js_1.ActionRowBuilder()
            .addComponents(adminButtons);
        rows.push(adminRow);
    }
    return rows;
}
async function createElectionResultsEmbed(election, candidates, stats, participationRate) {
    const embedColor = await configurableConstants_1.default.getEmbedColor(election.guildId);
    const embed = new discord_js_1.EmbedBuilder()
        .setColor(embedColor)
        .setTitle(`🏁 ${election.title} - FINAL RESULTS`)
        .setTimestamp();
    if (election.description) {
        embed.setDescription(`📋 ${election.description}\n\n🎊 **Election has concluded!**`);
    }
    else {
        embed.setDescription('🎊 **Election has concluded!**');
    }
    if (candidates.length > 0) {
        const winner = candidates[0];
        const winnerText = [
            `🎉 **WINNER: ${winner.displayName}**`,
            `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **${(0, embedBuilder_1.formatCoins)(winner.voteWeight)}** vote weight`,
            `📊 **${winner.percentage.toFixed(1)}%** of total votes`,
            `👥 **${winner.voteCount}** individual votes`
        ].join('\n');
        embed.addFields({
            name: '👑 Election Winner',
            value: winnerText,
            inline: false
        });
    }
    if (candidates.length > 0) {
        const leaderboard = candidates.slice(0, 10).map((candidate, index) => {
            const rankEmoji = getRankEmoji(candidate.rank);
            const percentage = candidate.percentage.toFixed(1);
            const voteDisplay = election.showVoteWeights
                ? `${(0, embedBuilder_1.formatCoins)(candidate.voteWeight)} (${percentage}%)`
                : `${candidate.voteCount} votes (${percentage}%)`;
            return `${rankEmoji} **${candidate.displayName}** - ${voteDisplay}`;
        }).join('\n');
        embed.addFields({
            name: '📊 Final Leaderboard',
            value: leaderboard,
            inline: false
        });
        if (candidates.length > 10) {
            embed.addFields({
                name: '📝 Note',
                value: `Showing top 10 candidates. ${candidates.length - 10} more candidates participated.`,
                inline: false
            });
        }
    }
    else {
        embed.addFields({
            name: '📊 Final Results',
            value: 'No candidates participated in this election.',
            inline: false
        });
    }
    const statsText = [
        `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **Total Vote Weight:** ${(0, embedBuilder_1.formatCoins)(stats.totalVoteWeight)}`,
        `👥 **Total Votes Cast:** ${stats.totalVotes}`,
        `🏆 **Total Candidates:** ${stats.totalCandidates}`,
        participationRate ? `📈 **Participation Rate:** ${participationRate.toFixed(1)}%` : null
    ].filter(Boolean).join('\n');
    embed.addFields({
        name: '📈 Election Statistics',
        value: statsText,
        inline: false
    });
    const timelineText = [
        `📅 **Started:** <t:${Math.floor(election.createdAt.getTime() / 1000)}:F>`,
        `🏁 **Ended:** <t:${Math.floor((election.endedAt || new Date()).getTime() / 1000)}:F>`,
        `⏱️ **Duration:** ${calculateElectionDuration(election.createdAt, election.endedAt || new Date())}`
    ].join('\n');
    embed.addFields({
        name: '⏰ Election Timeline',
        value: timelineText,
        inline: false
    });
    embed.setFooter({
        text: `Election ID: ${election.electionId} | Created by: ${election.createdBy} | Status: ENDED`
    });
    return embed;
}
function createCandidateConfirmationEmbed(election, userDisplayName) {
    return new discord_js_1.EmbedBuilder()
        .setColor(embedBuilder_1.COLORS.SUCCESS)
        .setTitle('🏃 Candidacy Confirmed!')
        .setDescription(`**${userDisplayName}**, you are now a candidate in the election: **${election.title}**`)
        .addFields({
        name: '📋 Next Steps',
        value: [
            '🗳️ Other users can now vote for you',
            '💰 Vote weight is based on voter coin balances',
            '🚪 You can resign anytime using the "Resign" button',
            '📊 Watch your progress in the election embed'
        ].join('\n'),
        inline: false
    })
        .setTimestamp()
        .setFooter({
        text: `Election ID: ${election.electionId}`
    });
}
function createVoteConfirmationEmbed(election, candidateName, voteWeight, isUpdate = false) {
    const action = isUpdate ? 'updated' : 'cast';
    return new discord_js_1.EmbedBuilder()
        .setColor(embedBuilder_1.COLORS.SUCCESS)
        .setTitle(`🗳️ Vote ${action.charAt(0).toUpperCase() + action.slice(1)}!`)
        .setDescription(`Your vote has been ${action} for **${candidateName}** in: **${election.title}**`)
        .addFields({
        name: '💰 Your Vote Weight',
        value: `${(0, embedBuilder_1.formatCoins)(voteWeight)} (based on your current coin balance)`,
        inline: false
    })
        .setTimestamp()
        .setFooter({
        text: `Election ID: ${election.electionId}`
    });
}
function getRankEmoji(rank) {
    switch (rank) {
        case 1: return '🥇';
        case 2: return '🥈';
        case 3: return '🥉';
        case 4: return '4️⃣';
        case 5: return '5️⃣';
        case 6: return '6️⃣';
        case 7: return '7️⃣';
        case 8: return '8️⃣';
        case 9: return '9️⃣';
        case 10: return '🔟';
        default: return '📍';
    }
}
function createWinnerAnnouncementEmbed(election, winner, stats) {
    return new discord_js_1.EmbedBuilder()
        .setColor(embedBuilder_1.COLORS.SUCCESS)
        .setTitle(`🎉 Election Winner Announced!`)
        .setDescription(`**${election.title}** has concluded!`)
        .addFields({
        name: '👑 Winner',
        value: `**${winner.displayName}** is the winner!`,
        inline: false
    }, {
        name: '📊 Final Score',
        value: [
            `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **${(0, embedBuilder_1.formatCoins)(winner.voteWeight)}** vote weight`,
            `📈 **${winner.percentage.toFixed(1)}%** of total votes`,
            `👥 **${winner.voteCount}** individual votes`
        ].join('\n'),
        inline: true
    }, {
        name: '📈 Election Stats',
        value: [
            `Total Votes: ${stats.totalVotes}`,
            `Total Weight: ${(0, embedBuilder_1.formatCoins)(stats.totalVoteWeight)}`,
            `Candidates: ${stats.totalCandidates}`
        ].join('\n'),
        inline: true
    })
        .setTimestamp()
        .setFooter({
        text: `Congratulations to ${winner.displayName}! 🎊`
    });
}
function createElectionArchiveEmbed(election) {
    return new discord_js_1.EmbedBuilder()
        .setColor(embedBuilder_1.COLORS.PRIMARY)
        .setTitle('📚 Election Archived')
        .setDescription(`The election "${election.title}" has been archived and moved to historical records.`)
        .addFields({
        name: '📋 Archive Details',
        value: [
            `🆔 **Election ID:** ${election.electionId}`,
            `📅 **Archived:** <t:${Math.floor(Date.now() / 1000)}:F>`,
            `💾 **Status:** Data preserved for audit trail`
        ].join('\n'),
        inline: false
    })
        .setTimestamp()
        .setFooter({
        text: 'Election data has been safely archived'
    });
}
function calculateElectionDuration(startDate, endDate) {
    const durationMs = endDate.getTime() - startDate.getTime();
    const days = Math.floor(durationMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((durationMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
    if (days > 0) {
        return `${days}d ${hours}h ${minutes}m`;
    }
    else if (hours > 0) {
        return `${hours}h ${minutes}m`;
    }
    else {
        return `${minutes}m`;
    }
}
function createElectionErrorEmbed(title, message) {
    return new discord_js_1.EmbedBuilder()
        .setColor(embedBuilder_1.COLORS.ERROR)
        .setTitle(`❌ ${title}`)
        .setDescription(message)
        .setTimestamp();
}
