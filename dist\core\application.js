"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Application = void 0;
exports.getApplication = getApplication;
exports.startApplication = startApplication;
const discord_js_1 = require("discord.js");
const database_1 = require("./database");
const logger_1 = require("./logger");
const config_1 = require("../config");
class Application {
    constructor() {
        this.services = new Map();
        this.serviceRegistrations = new Map();
        this.isShuttingDown = false;
        this.logger = (0, logger_1.getLogger)();
        this.client = this.createDiscordClient();
        this.setupGracefulShutdown();
        this.logger.info('[Application] Application instance created');
    }
    createDiscordClient() {
        const config = (0, config_1.getDiscordConfig)();
        const intents = config.intents.map(intent => {
            switch (intent) {
                case 'Guilds': return discord_js_1.GatewayIntentBits.Guilds;
                case 'GuildMessages': return discord_js_1.GatewayIntentBits.GuildMessages;
                case 'MessageContent': return discord_js_1.GatewayIntentBits.MessageContent;
                case 'GuildMembers': return discord_js_1.GatewayIntentBits.GuildMembers;
                case 'GuildMessageReactions': return discord_js_1.GatewayIntentBits.GuildMessageReactions;
                default: throw new Error(`Unknown intent: ${intent}`);
            }
        });
        const client = new discord_js_1.Client({
            intents,
            allowedMentions: {
                parse: [],
                repliedUser: false
            },
            partials: [],
            makeCache: () => new discord_js_1.Collection(),
            sweepers: {
                messages: {
                    interval: 60,
                    lifetime: 300,
                },
                users: {
                    interval: 300,
                    filter: () => (user) => user.bot && user.id !== this.client.user?.id,
                },
                guildMembers: {
                    interval: 300,
                    filter: () => () => true,
                },
                threads: {
                    interval: 300,
                    lifetime: 600,
                }
            },
            rest: {
                timeout: 15000,
                retries: 1,
            }
        });
        client.commands = new discord_js_1.Collection();
        return client;
    }
    async initialize() {
        try {
            this.logger.info('[Application] Initializing application...');
            await this.registerCoreServices();
            await this.initializeServices();
            await this.connectToDiscord();
            this.logger.info('[Application] Application initialized successfully');
        }
        catch (error) {
            this.logger.error('[Application] Failed to initialize application', { error });
            throw error;
        }
    }
    async registerCoreServices() {
        const databaseService = new database_1.DatabaseService(this.logger);
        this.registerService(databaseService, { autoStart: true, priority: 1 });
    }
    registerService(service, options = {}) {
        const defaultOptions = {
            autoStart: true,
            dependencies: [],
            priority: 10,
        };
        const finalOptions = { ...defaultOptions, ...options };
        this.serviceRegistrations.set(service.name, {
            service,
            options: finalOptions,
            initialized: false,
        });
        this.services.set(service.name, service);
        this.logger.debug(`[Application] Registered service: ${service.name}`, {
            autoStart: finalOptions.autoStart,
            dependencies: finalOptions.dependencies,
            priority: finalOptions.priority,
        });
    }
    getService(name) {
        const service = this.services.get(name);
        if (!service) {
            throw new Error(`Service not found: ${name}`);
        }
        return service;
    }
    async initializeServices() {
        const sortedServices = Array.from(this.serviceRegistrations.entries())
            .sort(([, a], [, b]) => (a.options.priority || 10) - (b.options.priority || 10));
        for (const [name, registration] of sortedServices) {
            if (!registration.options.autoStart) {
                continue;
            }
            await this.initializeService(name, registration);
        }
    }
    async initializeService(name, registration) {
        if (registration.initialized) {
            return;
        }
        if (registration.options.dependencies) {
            for (const dependency of registration.options.dependencies) {
                const depRegistration = this.serviceRegistrations.get(dependency);
                if (!depRegistration) {
                    throw new Error(`Dependency not found: ${dependency} for service ${name}`);
                }
                if (!depRegistration.initialized) {
                    await this.initializeService(dependency, depRegistration);
                }
            }
        }
        try {
            this.logger.info(`[Application] Initializing service: ${name}`);
            if (registration.service.initialize) {
                await registration.service.initialize();
            }
            registration.initialized = true;
            this.logger.info(`[Application] Service initialized: ${name}`);
        }
        catch (error) {
            this.logger.error(`[Application] Failed to initialize service: ${name}`, { error });
            throw error;
        }
    }
    async connectToDiscord() {
        const config = (0, config_1.getDiscordConfig)();
        this.logger.info('[Application] Connecting to Discord...');
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Discord connection timeout'));
            }, 30000);
            this.client.once('ready', () => {
                clearTimeout(timeout);
                this.logger.info(`[Application] Connected to Discord as ${this.client.user?.tag}`);
                resolve();
            });
            this.client.login(config.token).catch(reject);
        });
    }
    setupGracefulShutdown() {
        const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
        signals.forEach(signal => {
            process.on(signal, async () => {
                this.logger.info(`[Application] Received ${signal}, shutting down gracefully...`);
                await this.shutdown();
                process.exit(0);
            });
        });
        process.on('uncaughtException', (error) => {
            this.logger.error('[Application] Uncaught exception', { error });
            this.shutdown().then(() => process.exit(1));
        });
        process.on('unhandledRejection', (reason, promise) => {
            this.logger.error('[Application] Unhandled rejection', { reason, promise });
        });
    }
    async shutdown() {
        if (this.isShuttingDown) {
            return;
        }
        this.isShuttingDown = true;
        this.logger.info('[Application] Shutting down application...');
        try {
            if (this.client.isReady()) {
                this.client.destroy();
                this.logger.info('[Application] Discord client destroyed');
            }
            const sortedServices = Array.from(this.serviceRegistrations.entries())
                .sort(([, a], [, b]) => (b.options.priority || 10) - (a.options.priority || 10));
            for (const [name, registration] of sortedServices) {
                if (registration.initialized && registration.service.shutdown) {
                    try {
                        this.logger.info(`[Application] Shutting down service: ${name}`);
                        await registration.service.shutdown();
                        registration.initialized = false;
                    }
                    catch (error) {
                        this.logger.error(`[Application] Error shutting down service: ${name}`, { error });
                    }
                }
            }
            this.logger.info('[Application] Application shutdown complete');
        }
        catch (error) {
            this.logger.error('[Application] Error during shutdown', { error });
        }
    }
    async getHealthStatus() {
        const status = {
            application: 'healthy',
            discord: this.client.isReady() ? 'connected' : 'disconnected',
            services: {},
            uptime: process.uptime(),
            memory: process.memoryUsage(),
        };
        for (const [name, registration] of this.serviceRegistrations.entries()) {
            if (registration.initialized) {
                status.services[name] = 'initialized';
            }
            else {
                status.services[name] = 'not_initialized';
            }
        }
        return status;
    }
}
exports.Application = Application;
let globalApp = null;
function getApplication() {
    if (!globalApp) {
        globalApp = new Application();
    }
    return globalApp;
}
async function startApplication() {
    const app = getApplication();
    await app.initialize();
    return app;
}
exports.default = Application;
