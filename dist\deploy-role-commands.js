"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
async function deployRoleCommands() {
    try {
        console.log('🔄 Deploying role prefix commands...');
        const enhanceRoleCommand = new discord_js_1.SlashCommandBuilder()
            .setName('enhancerole')
            .setDescription('Assign a prefix to all users with a specified role')
            .addRoleOption(option => option
            .setName('role')
            .setDescription('Discord role to target')
            .setRequired(true))
            .addStringOption(option => option
            .setName('prefix')
            .setDescription('Text/emoji prefix to prepend (max 10 characters)')
            .setRequired(true)
            .setMaxLength(10))
            .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.ManageNicknames);
        const updateNamesCommand = new discord_js_1.SlashCommandBuilder()
            .setName('updatenames')
            .setDescription('Ensure all members have correct prefixes based on their current roles')
            .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.ManageNicknames);
        const commands = [
            enhanceRoleCommand.toJSON(),
            updateNamesCommand.toJSON()
        ];
        console.log(`📊 Commands to deploy: ${commands.length}`);
        console.log('   • /enhancerole - Assign prefix to role members');
        console.log('   • /updatenames - Sync all member prefixes');
        const rest = new discord_js_1.REST({ version: '10' }).setToken(process.env.BOT_TOKEN);
        console.log('🚀 Started refreshing role commands...');
        const existingCommands = await rest.get(discord_js_1.Routes.applicationCommands(process.env.CLIENT_ID));
        console.log(`📋 Found ${existingCommands.length} existing commands`);
        const allCommands = [...existingCommands];
        const commandsToRemove = ['enhancerole', 'updatenames'];
        const filteredCommands = allCommands.filter(cmd => !commandsToRemove.includes(cmd.name));
        const finalCommands = [...filteredCommands, ...commands];
        const data = await rest.put(discord_js_1.Routes.applicationCommands(process.env.CLIENT_ID), { body: finalCommands });
        console.log(`✅ Successfully deployed ${data.length} total commands.`);
        const newCommands = data.filter(cmd => commandsToRemove.includes(cmd.name));
        console.log('\n🎉 New role prefix commands deployed:');
        for (const cmd of newCommands) {
            console.log(`   • /${cmd.name} - ${cmd.description}`);
        }
    }
    catch (error) {
        console.error('❌ Error deploying role commands:', error);
        process.exit(1);
    }
}
deployRoleCommands();
