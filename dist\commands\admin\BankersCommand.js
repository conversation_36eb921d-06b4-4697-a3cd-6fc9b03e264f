"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BankersCommand = void 0;
const BaseCommand_1 = require("../base/BaseCommand");
const embedBuilder_1 = require("../../utils/embedBuilder");
const errorHandler_1 = require("../../utils/errorHandler");
const BankerPermissions_1 = require("../../utils/permissions/BankerPermissions");
class BankersCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'bankers',
            description: 'Set the banker role for this server (admin only)',
            category: BaseCommand_1.CommandCategory.ADMIN,
            adminOnly: true,
            requiredFeatures: ['ECONOMY_SYSTEM'],
            requiredPermissions: ['Administrator'],
        });
    }
    customizeCommand(command) {
        command
            .addSubcommand(subcommand => subcommand
            .setName('set')
            .setDescription('Set the banker role for this server')
            .addRoleOption(option => option.setName('role')
            .setDescription('The role that should have banker permissions')
            .setRequired(true)))
            .addSubcommand(subcommand => subcommand
            .setName('remove')
            .setDescription('Remove the banker role (only admins will have banker permissions)'))
            .addSubcommand(subcommand => subcommand
            .setName('view')
            .setDescription('View the current banker role for this server'));
    }
    async executeCommand(context) {
        const { interaction } = context;
        const subcommand = interaction.options.getSubcommand();
        const guildId = interaction.guild.id;
        switch (subcommand) {
            case 'set':
                await this.handleSetBankerRole(context);
                break;
            case 'remove':
                await this.handleRemoveBankerRole(context);
                break;
            case 'view':
                await this.handleViewBankerRole(context);
                break;
            default:
                throw new errorHandler_1.ValidationError('Invalid subcommand');
        }
    }
    async handleSetBankerRole(context) {
        const { interaction } = context;
        const roleOption = interaction.options.getRole('role', true);
        const guildId = interaction.guild.id;
        const member = interaction.member;
        try {
            await (0, BankerPermissions_1.validateBankerRole)(member, roleOption.id);
            const role = await interaction.guild.roles.fetch(roleOption.id);
            if (!role) {
                throw new errorHandler_1.ValidationError('Role not found in server.');
            }
            await (0, BankerPermissions_1.setBankerRole)(guildId, role.id);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Banker Role Set Successfully!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.KEY} **Banker Role Configuration Updated**\n\n` +
                `The role **${role.name}** has been designated as the banker role for this server.\n\n` +
                `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **Permissions Granted:**\n` +
                `• Users with this role can now use the \`/give\` command\n` +
                `• Users with this role can now use the \`/fine\` command\n` +
                `• Administrators retain all banker permissions`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.HAMMER} Administrator`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.ROLE} Banker Role`,
                value: `**${role.name}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.USER} Role Members`,
                value: `${role.members.size} member(s)`,
                inline: true
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            this.logger.info(`Admin ${interaction.user.username} set banker role to ${role.name}`, {
                adminId: interaction.user.id,
                roleId: role.id,
                roleName: role.name,
                guildId: interaction.guild?.id,
            });
        }
        catch (error) {
            this.logger.error('Error setting banker role', {
                error,
                adminId: interaction.user.id,
                roleId: roleOption.id,
                roleName: roleOption.name
            });
            throw error;
        }
    }
    async handleRemoveBankerRole(context) {
        const { interaction } = context;
        const guildId = interaction.guild.id;
        try {
            const currentRoleId = await (0, BankerPermissions_1.getBankerRoleId)(guildId);
            let currentRoleName = 'None';
            if (currentRoleId) {
                const currentRole = await interaction.guild.roles.fetch(currentRoleId).catch(() => null);
                currentRoleName = currentRole?.name || 'Unknown Role';
            }
            await (0, BankerPermissions_1.setBankerRole)(guildId, null);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Banker Role Removed Successfully!');
            embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Banker Role Configuration Cleared**\n\n` +
                `The banker role has been removed from this server.\n\n` +
                `${embedBuilder_1.EMOJIS.ADMIN.SCALES} **Current Permissions:**\n` +
                `• Only administrators can use the \`/give\` command\n` +
                `• Only administrators can use the \`/fine\` command`)
                .addFields({
                name: `${embedBuilder_1.EMOJIS.ADMIN.HAMMER} Administrator`,
                value: `**${interaction.user.displayName}**`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.ROLE} Previous Banker Role`,
                value: `**${currentRoleName}**`,
                inline: true
            });
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: false
            });
            this.logger.info(`Admin ${interaction.user.username} removed banker role`, {
                adminId: interaction.user.id,
                previousRoleId: currentRoleId,
                previousRoleName: currentRoleName,
                guildId: interaction.guild?.id,
            });
        }
        catch (error) {
            this.logger.error('Error removing banker role', {
                error,
                adminId: interaction.user.id
            });
            throw error;
        }
    }
    async handleViewBankerRole(context) {
        const { interaction } = context;
        const guildId = interaction.guild.id;
        try {
            const currentRoleId = await (0, BankerPermissions_1.getBankerRoleId)(guildId);
            let embed;
            if (currentRoleId) {
                const currentRole = await interaction.guild.roles.fetch(currentRoleId).catch(() => null);
                if (currentRole) {
                    embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Current Banker Role');
                    embed.setDescription(`${embedBuilder_1.EMOJIS.MISC.ROLE} **Current Banker Role Configuration**\n\n` +
                        `The role **${currentRole.name}** is currently designated as the banker role.\n\n` +
                        `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **Permissions:**\n` +
                        `• Members with this role can use \`/give\` and \`/fine\` commands\n` +
                        `• Administrators also retain all banker permissions`)
                        .addFields({
                        name: `${embedBuilder_1.EMOJIS.MISC.ROLE} Banker Role`,
                        value: `**${currentRole.name}**`,
                        inline: true
                    }, {
                        name: `${embedBuilder_1.EMOJIS.MISC.USER} Role Members`,
                        value: `${currentRole.members.size} member(s)`,
                        inline: true
                    }, {
                        name: `${embedBuilder_1.EMOJIS.MISC.ID} Role ID`,
                        value: `\`${currentRole.id}\``,
                        inline: true
                    });
                }
                else {
                    embed = await (0, embedBuilder_1.createServerErrorEmbed)(guildId, 'Banker Role Configuration Issue');
                    embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.WARNING} **Configuration Issue Detected**\n\n` +
                        `A banker role is configured but the role no longer exists.\n` +
                        `Please use \`/bankers remove\` to clear the configuration or \`/bankers set\` to set a new role.`);
                }
            }
            else {
                embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'No Banker Role Set');
                embed.setDescription(`${embedBuilder_1.EMOJIS.ADMIN.SCALES} **No Banker Role Configured**\n\n` +
                    `Currently, only administrators can use \`/give\` and \`/fine\` commands.\n\n` +
                    `Use \`/bankers set @role\` to designate a banker role.`);
            }
            (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
            await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });
        }
        catch (error) {
            this.logger.error('Error viewing banker role', {
                error,
                adminId: interaction.user.id
            });
            throw error;
        }
    }
}
exports.BankersCommand = BankersCommand;
