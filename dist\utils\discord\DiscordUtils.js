"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiscordUtils = exports.ButtonUtils = exports.EmbedUtils = exports.ChannelUtils = exports.RoleUtils = exports.MemberUtils = exports.GuildUtils = void 0;
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../errorHandler");
const constants_1 = require("../../config/constants");
class GuildUtils {
    static async fetchGuild(client, guildId) {
        try {
            return await client.guilds.fetch(guildId);
        }
        catch (error) {
            return null;
        }
    }
    static async validateGuildAccess(client, guildId) {
        const guild = await this.fetchGuild(client, guildId);
        if (!guild) {
            throw new errorHandler_1.ValidationError(`Guild not found or bot doesn't have access: ${guildId}`);
        }
        return guild;
    }
    static async getMemberCount(guild) {
        try {
            await guild.members.fetch();
            return guild.memberCount;
        }
        catch (error) {
            return guild.memberCount;
        }
    }
    static hasFeature(guild, feature) {
        return guild.features.includes(feature);
    }
}
exports.GuildUtils = GuildUtils;
class MemberUtils {
    static async fetchMember(guild, userId) {
        try {
            return await guild.members.fetch(userId);
        }
        catch (error) {
            return null;
        }
    }
    static async validateMemberAccess(guild, userId) {
        const member = await this.fetchMember(guild, userId);
        if (!member) {
            throw new errorHandler_1.ValidationError(`Member not found in guild: ${userId}`);
        }
        return member;
    }
    static hasPermission(member, permission) {
        return member.permissions.has(discord_js_1.PermissionFlagsBits[permission]);
    }
    static hasAnyPermission(member, permissions) {
        return permissions.some(permission => this.hasPermission(member, permission));
    }
    static isAdmin(member) {
        return this.hasPermission(member, 'Administrator');
    }
    static getHighestRole(member) {
        return member.roles.highest;
    }
    static canManageMember(manager, target) {
        if (manager.id === target.id)
            return false;
        if (this.isAdmin(manager))
            return true;
        const managerHighest = this.getHighestRole(manager);
        const targetHighest = this.getHighestRole(target);
        if (!managerHighest || !targetHighest)
            return false;
        return managerHighest.position > targetHighest.position;
    }
}
exports.MemberUtils = MemberUtils;
class RoleUtils {
    static async fetchRole(guild, roleId) {
        try {
            return await guild.roles.fetch(roleId);
        }
        catch (error) {
            return null;
        }
    }
    static findRoleByName(guild, roleName) {
        return guild.roles.cache.find(role => role.name.toLowerCase() === roleName.toLowerCase()) || null;
    }
    static canManageRole(guild, role) {
        const botMember = guild.members.me;
        if (!botMember)
            return false;
        const botHighestRole = MemberUtils.getHighestRole(botMember);
        if (!botHighestRole)
            return false;
        return botHighestRole.position > role.position;
    }
    static getManageableRoles(guild) {
        return guild.roles.cache.filter(role => this.canManageRole(guild, role) && role.name !== '@everyone').map(role => role);
    }
}
exports.RoleUtils = RoleUtils;
class ChannelUtils {
    static async fetchChannel(client, channelId) {
        try {
            const channel = await client.channels.fetch(channelId);
            return channel?.isTextBased() ? channel : null;
        }
        catch (error) {
            return null;
        }
    }
    static canSendMessages(channel) {
        const permissions = channel.permissionsFor(channel.guild.members.me);
        return permissions?.has([discord_js_1.PermissionFlagsBits.SendMessages, discord_js_1.PermissionFlagsBits.ViewChannel]) || false;
    }
    static canEmbedLinks(channel) {
        const permissions = channel.permissionsFor(channel.guild.members.me);
        return permissions?.has(discord_js_1.PermissionFlagsBits.EmbedLinks) || false;
    }
    static async safeSendMessage(channel, content) {
        try {
            if (!this.canSendMessages(channel)) {
                return false;
            }
            await channel.send(content);
            return true;
        }
        catch (error) {
            return false;
        }
    }
}
exports.ChannelUtils = ChannelUtils;
class EmbedUtils {
    static validateEmbed(embed) {
        const data = embed.toJSON();
        if (data.title && data.title.length > 256) {
            throw new errorHandler_1.ValidationError('Embed title cannot exceed 256 characters');
        }
        if (data.description && data.description.length > constants_1.DISCORD.MAX_EMBED_DESCRIPTION_LENGTH) {
            throw new errorHandler_1.ValidationError(`Embed description cannot exceed ${constants_1.DISCORD.MAX_EMBED_DESCRIPTION_LENGTH} characters`);
        }
        if (data.fields && data.fields.length > constants_1.DISCORD.MAX_EMBED_FIELDS) {
            throw new errorHandler_1.ValidationError(`Embed cannot have more than ${constants_1.DISCORD.MAX_EMBED_FIELDS} fields`);
        }
        const totalLength = (data.title?.length || 0) +
            (data.description?.length || 0) +
            (data.fields?.reduce((sum, field) => sum + field.name.length + field.value.length, 0) || 0);
        if (totalLength > 6000) {
            throw new errorHandler_1.ValidationError('Total embed content cannot exceed 6000 characters');
        }
    }
    static truncateFieldValue(value, maxLength = 1024) {
        if (value.length <= maxLength)
            return value;
        return value.substring(0, maxLength - 3) + '...';
    }
    static splitIntoFields(text, fieldName, maxLength = 1024) {
        const fields = [];
        if (text.length <= maxLength) {
            fields.push({ name: fieldName, value: text });
            return fields;
        }
        let currentText = text;
        let partNumber = 1;
        while (currentText.length > 0) {
            let chunk = currentText.substring(0, maxLength);
            if (currentText.length > maxLength) {
                const lastNewline = chunk.lastIndexOf('\n');
                if (lastNewline > maxLength * 0.5) {
                    chunk = chunk.substring(0, lastNewline);
                }
            }
            fields.push({
                name: partNumber === 1 ? fieldName : `${fieldName} (${partNumber})`,
                value: chunk
            });
            currentText = currentText.substring(chunk.length);
            partNumber++;
        }
        return fields;
    }
}
exports.EmbedUtils = EmbedUtils;
class ButtonUtils {
    static createActionRow(buttons) {
        if (buttons.length > 5) {
            throw new errorHandler_1.ValidationError('Action row cannot have more than 5 buttons');
        }
        return new discord_js_1.ActionRowBuilder().addComponents(buttons);
    }
    static createButton(customId, label, style = discord_js_1.ButtonStyle.Primary, emoji) {
        if (label.length > constants_1.DISCORD.MAX_BUTTON_LABEL_LENGTH) {
            throw new errorHandler_1.ValidationError(`Button label cannot exceed ${constants_1.DISCORD.MAX_BUTTON_LABEL_LENGTH} characters`);
        }
        const button = new discord_js_1.ButtonBuilder()
            .setCustomId(customId)
            .setLabel(label)
            .setStyle(style);
        if (emoji) {
            button.setEmoji(emoji);
        }
        return button;
    }
    static createLinkButton(url, label, emoji) {
        const button = new discord_js_1.ButtonBuilder()
            .setURL(url)
            .setLabel(label)
            .setStyle(discord_js_1.ButtonStyle.Link);
        if (emoji) {
            button.setEmoji(emoji);
        }
        return button;
    }
}
exports.ButtonUtils = ButtonUtils;
class DiscordUtils {
    static parseId(input) {
        const match = input.match(/^<[@#&]!?(\d+)>$/);
        return match ? match[1] : input;
    }
    static isDiscordId(input) {
        return /^\d{17,20}$/.test(input);
    }
    static formatUserMention(userId) {
        return `<@${userId}>`;
    }
    static formatRoleMention(roleId) {
        return `<@&${roleId}>`;
    }
    static formatChannelMention(channelId) {
        return `<#${channelId}>`;
    }
}
exports.DiscordUtils = DiscordUtils;
DiscordUtils.guild = GuildUtils;
DiscordUtils.member = MemberUtils;
DiscordUtils.role = RoleUtils;
DiscordUtils.channel = ChannelUtils;
DiscordUtils.embed = EmbedUtils;
DiscordUtils.button = ButtonUtils;
exports.default = DiscordUtils;
