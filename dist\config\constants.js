"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SCHEDULES = exports.VALIDATION = exports.FEATURES = exports.LOGGING = exports.ERROR_HANDLING = exports.DATABASE = exports.DISCORD = exports.TRADE = exports.DYNASTY = exports.COIN_DECAY = exports.MILESTONES = exports.ECONOMY = void 0;
exports.ECONOMY = {
    CURRENCY_NAME: 'Phalanx Loyalty Coins',
    CURRENCY_SYMBOL: 'PLC',
    CURRENCY_EMOJI: '🪙',
    REACTION_REWARD_AMOUNT: 5,
    REACTION_RATE_LIMIT_SECONDS: 30,
    REACTION_MESSAGE_AGE_LIMIT_HOURS: 24,
    TAX_COLLECTION_INTERVAL_HOURS: 1,
    DEFAULT_STARTER_BALANCE: 0,
};
exports.MILESTONES = {
    MAX_WEEKLY_MILESTONE_REWARDS: 50,
    MAX_DAILY_MILESTONE_REWARDS: 10,
    SUSPICIOUS_ACTIVITY_THRESHOLD: 5,
    BLA<PERSON>KLIST_DURATION_HOURS: 24,
    DEFAULT_LOGIN_STREAK_REWARD: 10,
    DEFAULT_MESSAGE_COUNT_REWARD: 15,
    DEFAULT_VOICE_TIME_REWARD: 20,
    DEFAULT_DIMINISHING_FACTOR: 0.9,
};
exports.COIN_DECAY = {
    DEFAULT_DECAY_PERCENTAGE: 5,
    DEFAULT_INACTIVITY_THRESHOLD_DAYS: 30,
    MIN_DECAY_PERCENTAGE: 1,
    MAX_DECAY_PERCENTAGE: 50,
    MIN_INACTIVITY_THRESHOLD: 7,
    MAX_INACTIVITY_THRESHOLD: 365,
    BATCH_SIZE: 100,
    MAX_PROCESSING_TIME_MS: 300000,
};
exports.DYNASTY = {
    MIN_PLC_REQUIREMENT: 5000,
    MIN_TENURE_DAYS: 30,
    MILESTONE_BONUS_PERCENTAGE: 10,
    MAX_LEVEL: 10,
    BASE_LEVEL_REQUIREMENT: 1000,
    LEVEL_MULTIPLIER: 1.5,
};
exports.TRADE = {
    MIN_TRADE_AMOUNT: 100,
    MAX_TRADE_AMOUNT: 100000,
    MAX_ACTIVE_TRADES_PER_USER: 5,
    MAX_TRADE_PROPOSALS_PER_DAY: 10,
    TRADE_EXPIRATION_HOURS: 48,
    WARNING_HOURS: [6, 1],
    PARTIAL_CONFIRMATION_EXTENSION_HOURS: 12,
    APPEAL_WINDOW_HOURS: 24,
    TRADE_COOLDOWN_SECONDS: 30,
    DISPUTE_COOLDOWN_HOURS: 1,
    DISPUTE_RATIO_THRESHOLD: 0.2,
    TRADE_RESTRICTION_DAYS: 7,
    MIN_TRADES_FOR_REPUTATION: 5,
    STATES: {
        PROPOSED: 'PROPOSED',
        ACCEPTED: 'ACCEPTED',
        ACTIVE: 'ACTIVE',
        COMPLETED: 'COMPLETED',
        CANCELLED: 'CANCELLED',
        EXPIRED: 'EXPIRED',
        DISPUTED: 'DISPUTED',
    },
    MAX_EVIDENCE_FILES: 5,
    EVIDENCE_FILE_SIZE_MB: 10,
};
exports.DISCORD = {
    REQUIRED_INTENTS: [
        'Guilds',
        'GuildMessages',
        'MessageContent',
        'GuildMembers',
        'GuildMessageReactions'
    ],
    ADMIN_PERMISSIONS: ['Administrator', 'ManageGuild', 'ManageRoles'],
    COMMAND_COOLDOWN_SECONDS: 3,
    ADMIN_COMMAND_COOLDOWN_SECONDS: 2,
    MAX_EMBED_FIELDS: 25,
    MAX_EMBED_DESCRIPTION_LENGTH: 4096,
    MAX_BUTTON_LABEL_LENGTH: 80,
};
exports.DATABASE = {
    CONNECTION_TIMEOUT_MS: 30000,
    OPERATION_TIMEOUT_MS: 10000,
    USER_CLEANUP_GRACE_PERIOD_MS: 5000,
    REQUIRED_INDEXES: [
        { collection: 'users', index: { discordId: 1, guildId: 1 }, unique: true },
        { collection: 'users', index: { guildId: 1 } },
        { collection: 'users', index: { guildId: 1, balance: -1 } },
        { collection: 'transactions', index: { guildId: 1, discordId: 1, timestamp: -1 } },
        { collection: 'transactions', index: { guildId: 1, timestamp: -1 } },
        { collection: 'transactions', index: { guildId: 1, type: 1, timestamp: -1 } },
        { collection: 'roleforsales', index: { guildId: 1, roleId: 1 }, unique: true },
        { collection: 'roleforsales', index: { guildId: 1 } },
        { collection: 'reactionrewards', index: { userId: 1, timestamp: -1 } },
        { collection: 'milestoneconfigurations', index: { guildId: 1, milestoneType: 1 }, unique: true },
        { collection: 'suggestionconfigs', index: { guildId: 1 }, unique: true },
        { collection: 'suggestions', index: { suggestionId: 1 }, unique: true },
        { collection: 'suggestions', index: { guildId: 1, channelId: 1 } },
        { collection: 'suggestions', index: { authorId: 1 } },
        { collection: 'suggestions', index: { messageId: 1 } },
        { collection: 'suggestions', index: { expiresAt: 1 }, expireAfterSeconds: 0 },
        { collection: 'suggestions', index: { createdAt: 1 } },
    ],
};
exports.ERROR_HANDLING = {
    CATEGORIES: {
        DATABASE: 'Database Error',
        VALIDATION: 'Validation Error',
        PERMISSION: 'Permission Error',
        RATE_LIMIT: 'Rate Limit Error',
        NETWORK: 'Network Error',
        UNKNOWN: 'Unknown Error',
    },
    MAX_RETRIES: 3,
    RETRY_DELAY_MS: 1000,
    EXPONENTIAL_BACKOFF: true,
};
exports.LOGGING = {
    LEVELS: {
        ERROR: 'error',
        WARN: 'warn',
        INFO: 'info',
        DEBUG: 'debug',
    },
    CATEGORIES: {
        ECONOMY: 'economy',
        MILESTONE: 'milestone',
        DYNASTY: 'dynasty',
        ADMIN: 'admin',
        DATABASE: 'database',
        DISCORD: 'discord',
    },
};
exports.FEATURES = {
    ECONOMY_SYSTEM: true,
    MILESTONE_SYSTEM: true,
    DYNASTY_SYSTEM: true,
    REACTION_REWARDS: true,
    TRADE_SYSTEM: true,
    TAX_SYSTEM: true,
    STARTER_BALANCE: true,
    AUTO_MESSAGES: true,
    SALARY_SYSTEM: true,
    COIN_DECAY_SYSTEM: true,
    ROLE_AUTOMATION: true,
    USER_CLEANUP: true,
    AUDIT_LOGGING: true,
    DEBUG_MODE: false,
    VERBOSE_LOGGING: false,
};
exports.VALIDATION = {
    DISCORD_ID_REGEX: /^\d{17,20}$/,
    MIN_TRANSACTION_AMOUNT: 1,
    MAX_TRANSACTION_AMOUNT: 1000000,
    MAX_DESCRIPTION_LENGTH: 500,
    MAX_REASON_LENGTH: 200,
    MAX_NAME_LENGTH: 100,
    MAX_ROLES_PER_GUILD: 100,
    MAX_USERS_PER_OPERATION: 100,
};
exports.SCHEDULES = {
    TAX_COLLECTION: '0 * * * *',
    MILESTONE_RESET: '0 0 * * *',
    USER_CLEANUP: '0 2 * * *',
    AUDIT_CLEANUP: '0 3 * * 0',
    SALARY_DAILY: '0 1 * * *',
    SALARY_WEEKLY: '0 1 * * 0',
    COIN_DECAY: '0 3 * * *',
};
