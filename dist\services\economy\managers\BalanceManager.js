"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BalanceManager = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../../../utils/errorHandler");
const features_1 = require("../../../config/features");
const User_1 = __importDefault(require("../../../models/User"));
const Transaction_1 = __importDefault(require("../../../models/Transaction"));
class BalanceManager {
    constructor(logger) {
        this.logger = logger;
    }
    async adjustBalance(discordId, guildId, amount, type, details, client, dynastyId) {
        if (mongoose_1.default.connection.readyState !== 1) {
            throw new errorHandler_1.DatabaseError('Database is not connected. Please try again in a moment.');
        }
        const session = await mongoose_1.default.startSession();
        this.logOperation('Starting balance adjustment transaction', { discordId, guildId, amount, type, details });
        try {
            await session.withTransaction(async () => {
                const trimmedDiscordId = discordId.trim();
                const trimmedGuildId = guildId.trim();
                if (!trimmedDiscordId) {
                    throw new Error('Discord ID cannot be empty after trimming');
                }
                if (!trimmedGuildId) {
                    throw new Error('Guild ID cannot be empty after trimming');
                }
                const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId, guildId: trimmedGuildId }, {
                    $inc: { balance: amount },
                    $setOnInsert: { discordId: trimmedDiscordId, guildId: trimmedGuildId }
                }, {
                    new: true,
                    upsert: true,
                    runValidators: true,
                    session
                });
                this.logOperation('Creating transaction record', {
                    discordId: trimmedDiscordId,
                    guildId: trimmedGuildId,
                    type,
                    amount,
                    details,
                    dynastyId
                });
                await Transaction_1.default.create([{
                        discordId: trimmedDiscordId,
                        guildId: trimmedGuildId,
                        type,
                        amount,
                        details,
                        dynastyId,
                        timestamp: new Date()
                    }], { session });
                this.logOperation('Transaction complete', {
                    userId: user?._id,
                    newBalance: user?.balance
                });
                if (amount > 0 && client && guildId && user) {
                    setImmediate(async () => {
                        try {
                            await this.checkRoleAchievements(client, trimmedDiscordId, guildId, user.balance);
                        }
                        catch (error) {
                            this.handleError(error, { operation: 'role_achievement_check' });
                        }
                    });
                }
            });
        }
        catch (error) {
            this.handleError(error, { discordId, amount, type });
            throw new errorHandler_1.DatabaseError(`Failed to adjust balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        finally {
            await session.endSession();
        }
    }
    async getBalance(discordId) {
        try {
            const user = await this.ensureUser(discordId);
            return user.balance;
        }
        catch (error) {
            this.handleError(error, { discordId });
            throw new errorHandler_1.DatabaseError(`Failed to get balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async ensureUser(discordId) {
        try {
            const trimmedDiscordId = discordId.trim();
            if (!trimmedDiscordId) {
                throw new Error('Discord ID cannot be empty');
            }
            const user = await User_1.default.findOneAndUpdate({ discordId: trimmedDiscordId }, { $setOnInsert: { discordId: trimmedDiscordId, balance: 0 } }, { new: true, upsert: true, runValidators: true });
            return user;
        }
        catch (error) {
            this.handleError(error, { discordId });
            throw new errorHandler_1.DatabaseError(`Failed to ensure user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async checkRoleAchievements(client, discordId, guildId, balance) {
        try {
            const { checkAndAssignRoles, sendRoleAchievementNotifications } = await Promise.resolve().then(() => __importStar(require('../../role/RoleService')));
            const roleResult = await checkAndAssignRoles(client, discordId, guildId, balance);
            if (roleResult) {
                await sendRoleAchievementNotifications(roleResult, client, guildId);
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'role_achievement_check', discordId, guildId, balance });
        }
    }
    logOperation(operation, details) {
        this.logger.debug(`[BalanceManager] ${operation}`, details);
    }
    handleError(error, context) {
        this.logger.error('[BalanceManager] Error', {
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
            } : error,
            context,
        });
    }
}
exports.BalanceManager = BalanceManager;
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, String, String, discord_js_1.Client, String]),
    __metadata("design:returntype", Promise)
], BalanceManager.prototype, "adjustBalance", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BalanceManager.prototype, "getBalance", null);
