"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const errorHandler_1 = require("../utils/errorHandler");
const roleResolver_1 = require("../utils/roleResolver");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('removerole')
        .setDescription('Remove a role achievement (admin only)')
        .addRoleOption(option => option.setName('role').setDescription('Role to remove from achievements').setRequired(true))
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        if (!interaction.memberPermissions?.has(discord_js_1.PermissionFlagsBits.Administrator)) {
            throw new errorHandler_1.PermissionError();
        }
        const role = interaction.options.getRole('role', true);
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        try {
            const fullRole = interaction.guild.roles.cache.get(role.id) || await interaction.guild.roles.fetch(role.id);
            if (!fullRole) {
                throw new errorHandler_1.ValidationError('Role not found in guild');
            }
            await (0, roleResolver_1.validateRolePermissions)(interaction.guild, fullRole);
            const shopRole = await User_1.RoleForSale.findOne({
                roleId: fullRole.id,
                guildId: interaction.guild.id,
                roleType: 'balance_threshold'
            });
            if (!shopRole) {
                throw new errorHandler_1.ValidationError(`Role achievement "${fullRole.name}" does not exist in this server.`);
            }
            const roleName = shopRole.name;
            await User_1.RoleForSale.findOneAndDelete({
                roleId: fullRole.id,
                guildId: interaction.guild.id,
                roleType: 'balance_threshold'
            });
            const successMessage = `Role achievement **${roleName}** has been removed! 🗑️`;
            await interaction.reply({
                content: successMessage,
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to remove role achievement.');
            }
        }
    })
};
