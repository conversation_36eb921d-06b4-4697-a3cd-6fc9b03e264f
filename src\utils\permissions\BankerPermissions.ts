/**
 * Banker Permission Utilities
 * Handles permission checking for banker role functionality
 */

import { GuildMember, PermissionFlagsBits } from 'discord.js';
import ServerConfiguration from '../../models/ServerConfiguration';
import { ValidationError } from '../errorHandler';

/**
 * Check if a member has banker permissions (admin OR banker role)
 */
export async function hasBankerPermissions(member: GuildMember): Promise<boolean> {
  try {
    // Check if user has admin permissions first
    if (member.permissions.has(PermissionFlagsBits.Administrator)) {
      return true;
    }

    // Get server configuration to check for banker role
    const serverConfig = await ServerConfiguration.findByGuildId(member.guild.id);
    
    // If no banker role is configured, only admins can use banker commands
    if (!serverConfig?.bankerRoleId) {
      return false;
    }

    // Check if user has the banker role
    return member.roles.cache.has(serverConfig.bankerRoleId);
  } catch (error) {
    console.error('Error checking banker permissions:', error);
    return false;
  }
}

/**
 * Validate that a member has banker permissions, throwing an error if not
 */
export async function validateBankerPermissions(member: GuildMember): Promise<void> {
  const hasPermissions = await hasBankerPermissions(member);
  
  if (!hasPermissions) {
    throw new ValidationError('This command requires administrator permissions or the designated banker role.');
  }
}

/**
 * Get the current banker role ID for a guild
 */
export async function getBankerRoleId(guildId: string): Promise<string | null> {
  try {
    const serverConfig = await ServerConfiguration.findByGuildId(guildId);
    return serverConfig?.bankerRoleId || null;
  } catch (error) {
    console.error('Error getting banker role ID:', error);
    return null;
  }
}

/**
 * Set the banker role for a guild
 */
export async function setBankerRole(guildId: string, roleId: string | null): Promise<void> {
  try {
    await ServerConfiguration.createOrUpdate(guildId, { bankerRoleId: roleId });
  } catch (error) {
    console.error('Error setting banker role:', error);
    throw new ValidationError('Failed to update banker role configuration.');
  }
}

/**
 * Check if a role exists and is valid for banker permissions
 */
export async function validateBankerRole(member: GuildMember, roleId: string): Promise<void> {
  const guild = member.guild;
  
  // Fetch the role to ensure it exists
  const role = await guild.roles.fetch(roleId).catch(() => null);
  
  if (!role) {
    throw new ValidationError('The specified role does not exist in this server.');
  }

  // Check if the role is @everyone (which shouldn't be used as banker role)
  if (role.id === guild.id) {
    throw new ValidationError('The @everyone role cannot be used as a banker role.');
  }

  // Check if the bot can see/manage this role (optional validation)
  const botMember = guild.members.me;
  if (botMember && role.position >= botMember.roles.highest.position && !botMember.permissions.has(PermissionFlagsBits.Administrator)) {
    throw new ValidationError('The bot cannot manage this role due to role hierarchy. Please choose a role below the bot\'s highest role.');
  }
}
