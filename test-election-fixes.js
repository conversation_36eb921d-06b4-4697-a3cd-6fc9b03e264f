/**
 * Test script to verify election system fixes
 */

require('dotenv').config();
const { Client, GatewayIntentBits, PermissionFlagsBits } = require('discord.js');

// Mock objects for testing
const mockGuild = {
  id: '123456789012345678',
  ownerId: '987654321098765432'
};

const mockMember = {
  guild: mockGuild,
  permissions: {
    has: (permission) => {
      // Simulate having Administrator permission
      return permission === PermissionFlagsBits.Administrator;
    }
  }
};

const mockElection = {
  electionId: 'election_test123',
  guildId: mockGuild.id,
  createdBy: '111111111111111111',
  status: 'ACTIVE',
  title: 'Test Election'
};

async function testPermissionValidation() {
  console.log('🧪 Testing Permission Validation Fixes...');
  
  try {
    // Import the ElectionService
    const { ElectionService } = require('./dist/services/election/ElectionService');
    
    // Create a mock application context
    const mockApp = {
      getService: () => null,
      isFeatureEnabled: () => true
    };
    
    const electionService = new ElectionService(mockApp);
    
    // Mock the Election.findOne method
    const originalFindOne = require('./dist/models/Election').default.findOne;
    require('./dist/models/Election').default.findOne = jest.fn().mockResolvedValue(mockElection);
    
    // Test 1: Creator should be able to end election
    console.log('  ✓ Testing creator permission...');
    const creatorCanEnd = await electionService.canUserEndElection(
      mockElection.electionId,
      mockElection.createdBy,
      mockMember
    );
    console.log(`    Creator can end: ${creatorCanEnd}`);
    
    // Test 2: Admin should be able to end election
    console.log('  ✓ Testing admin permission...');
    const adminCanEnd = await electionService.canUserEndElection(
      mockElection.electionId,
      '999999999999999999', // Different user ID
      mockMember
    );
    console.log(`    Admin can end: ${adminCanEnd}`);
    
    // Test 3: Regular user should NOT be able to end election
    console.log('  ✓ Testing regular user permission...');
    const mockRegularMember = {
      ...mockMember,
      permissions: {
        has: () => false // No special permissions
      }
    };
    
    const regularCanEnd = await electionService.canUserEndElection(
      mockElection.electionId,
      '888888888888888888', // Different user ID
      mockRegularMember
    );
    console.log(`    Regular user can end: ${regularCanEnd}`);
    
    // Restore original method
    require('./dist/models/Election').default.findOne = originalFindOne;
    
    console.log('✅ Permission validation tests completed!');
    return true;
    
  } catch (error) {
    console.error('❌ Permission validation test failed:', error.message);
    return false;
  }
}

async function testServiceInitialization() {
  console.log('🧪 Testing Service Initialization Fixes...');
  
  try {
    // Import the ElectionsCommand
    const { ElectionsCommand } = require('./dist/commands/election/ElectionsCommand');
    
    const electionsCommand = new ElectionsCommand();
    
    // Test 1: Command should handle missing service gracefully
    console.log('  ✓ Testing missing service handling...');
    
    const mockInteraction = {
      user: { id: '123456789' },
      guild: { id: '987654321' },
      options: {
        getString: (key) => {
          const mockData = {
            'title': 'Test Election',
            'eligible_voter_roles': '@everyone',
            'eligible_candidate_roles': '@everyone',
            'roles_to_ping': '@everyone'
          };
          return mockData[key] || null;
        }
      },
      channel: { id: '555555555' },
      deferReply: async () => {},
      editReply: async () => ({ id: 'message123' })
    };
    
    const mockContext = {
      interaction: mockInteraction,
      guild: mockInteraction.guild,
      member: mockMember
    };
    
    // This should throw a ValidationError with our improved error message
    try {
      await electionsCommand.executeCommand(mockContext);
      console.log('    ❌ Expected error for missing service, but none was thrown');
      return false;
    } catch (error) {
      if (error.message.includes('ERR-MEH22HZJ-IXZW2')) {
        console.log('    ✅ Proper error handling for missing service detected');
      } else {
        console.log(`    ⚠️  Different error: ${error.message}`);
      }
    }
    
    console.log('✅ Service initialization tests completed!');
    return true;
    
  } catch (error) {
    console.error('❌ Service initialization test failed:', error.message);
    return false;
  }
}

async function testInteractionHandling() {
  console.log('🧪 Testing Interaction Handling Fixes...');
  
  try {
    console.log('  ✓ Checking interaction pattern implementation...');
    
    // Read the ElectionsCommand source to verify the pattern
    const fs = require('fs');
    const commandSource = fs.readFileSync('./dist/commands/election/ElectionsCommand.js', 'utf8');
    
    // Check for deferReply pattern
    const hasDeferReply = commandSource.includes('deferReply()');
    const hasEditReply = commandSource.includes('editReply');
    const hasProperErrorHandling = commandSource.includes('interaction.deferred');
    
    console.log(`    Has deferReply pattern: ${hasDeferReply}`);
    console.log(`    Has editReply pattern: ${hasEditReply}`);
    console.log(`    Has proper error handling: ${hasProperErrorHandling}`);
    
    if (hasDeferReply && hasEditReply && hasProperErrorHandling) {
      console.log('✅ Interaction handling pattern correctly implemented!');
      return true;
    } else {
      console.log('❌ Interaction handling pattern incomplete');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Interaction handling test failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Election System Fixes Verification\n');
  
  const results = [];
  
  results.push(await testPermissionValidation());
  results.push(await testServiceInitialization());
  results.push(await testInteractionHandling());
  
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All election system fixes verified successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Test the bot in Discord with real interactions');
    console.log('2. Create an election and verify candidate registration works');
    console.log('3. Test permission validation with different user roles');
    console.log('4. Verify no double-acknowledgment errors occur');
  } else {
    console.log('⚠️  Some tests failed. Please review the implementation.');
  }
  
  process.exit(passedTests === totalTests ? 0 : 1);
}

// Run tests
runAllTests().catch(error => {
  console.error('💥 Test execution failed:', error);
  process.exit(1);
});
