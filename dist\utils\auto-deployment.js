"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isProductionEnvironment = isProductionEnvironment;
exports.needsBuild = needsBuild;
exports.runBuild = runBuild;
exports.deployCommands = deployCommands;
exports.deployRoleCommands = deployRoleCommands;
exports.validateEnvironment = validateEnvironment;
exports.checkDependencies = checkDependencies;
const discord_js_1 = require("discord.js");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const child_process_1 = require("child_process");
const util_1 = require("util");
const logger_1 = require("../core/logger");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
const logger = (0, logger_1.getLogger)();
function isProductionEnvironment() {
    return process.env.NODE_ENV === 'production' ||
        process.env.DISCLOUD_ENVIRONMENT === 'true' ||
        process.env.DISCLOUD === 'true';
}
async function needsBuild() {
    try {
        const srcExists = fs_1.default.existsSync(path_1.default.join(process.cwd(), 'src'));
        if (!srcExists) {
            logger.warn('[AutoDeploy] src directory not found, assuming pre-built deployment');
            return false;
        }
        const distExists = fs_1.default.existsSync(path_1.default.join(process.cwd(), 'dist'));
        if (!distExists) {
            logger.info('[AutoDeploy] dist directory not found, build needed');
            return true;
        }
        const mainJsExists = fs_1.default.existsSync(path_1.default.join(process.cwd(), 'dist', 'main.js'));
        if (!mainJsExists) {
            logger.info('[AutoDeploy] dist/main.js not found, build needed');
            return true;
        }
        const srcStat = fs_1.default.statSync(path_1.default.join(process.cwd(), 'src', 'main.ts'));
        const distStat = fs_1.default.statSync(path_1.default.join(process.cwd(), 'dist', 'main.js'));
        if (srcStat.mtime > distStat.mtime) {
            logger.info('[AutoDeploy] Source files are newer than compiled files, build needed');
            return true;
        }
        logger.info('[AutoDeploy] Compiled files are up to date, no build needed');
        return false;
    }
    catch (error) {
        logger.error('[AutoDeploy] Error checking build status', { error });
        return true;
    }
}
async function runBuild() {
    logger.info('[AutoDeploy] Starting TypeScript build process...');
    try {
        const isDiscloud = process.env.DISCLOUD === 'true' ||
            process.env.DISCLOUD_ENVIRONMENT === 'true' ||
            process.cwd().includes('/home/<USER>');
        if (isDiscloud) {
            logger.info('[AutoDeploy] Detected Discloud environment');
        }
        const tscPaths = [
            path_1.default.join(process.cwd(), 'node_modules', '.bin', 'tsc'),
            path_1.default.join(process.cwd(), 'node_modules', 'typescript', 'bin', 'tsc'),
            'tsc'
        ];
        let tscPath = null;
        for (const tryPath of tscPaths) {
            if (fs_1.default.existsSync(tryPath) || tryPath === 'tsc') {
                tscPath = tryPath;
                break;
            }
        }
        if (!tscPath) {
            logger.error('[AutoDeploy] TypeScript compiler not found in any expected location');
            logger.error('[AutoDeploy] Tried paths:', tscPaths);
            return false;
        }
        logger.info(`[AutoDeploy] Using TypeScript compiler: ${tscPath}`);
        const buildCommand = tscPath === 'tsc' ? 'tsc' : `"${tscPath}"`;
        logger.info(`[AutoDeploy] Running command: ${buildCommand}`);
        const { stdout, stderr } = await execAsync(buildCommand, {
            cwd: process.cwd(),
            timeout: 60000,
            env: { ...process.env, NODE_ENV: 'production' }
        });
        if (stdout && stdout.trim() !== '') {
            logger.info('[AutoDeploy] TypeScript compiler stdout:', { stdout });
        }
        if (stderr && stderr.trim() !== '') {
            logger.warn('[AutoDeploy] TypeScript compiler stderr:', { stderr });
            if (stderr.toLowerCase().includes('error')) {
                logger.error('[AutoDeploy] TypeScript compilation errors detected');
                return false;
            }
        }
        const mainJsPath = path_1.default.join(process.cwd(), 'dist', 'main.js');
        const mainJsExists = fs_1.default.existsSync(mainJsPath);
        if (!mainJsExists) {
            logger.error('[AutoDeploy] Build failed - dist/main.js not found after compilation');
            logger.error('[AutoDeploy] Current working directory:', process.cwd());
            logger.error('[AutoDeploy] Expected main.js at:', mainJsPath);
            const distPath = path_1.default.join(process.cwd(), 'dist');
            if (fs_1.default.existsSync(distPath)) {
                const distContents = fs_1.default.readdirSync(distPath);
                logger.error('[AutoDeploy] Dist directory contents:', distContents);
            }
            else {
                logger.error('[AutoDeploy] Dist directory does not exist');
            }
            return false;
        }
        logger.info('[AutoDeploy] TypeScript build completed successfully');
        logger.info(`[AutoDeploy] main.js size: ${fs_1.default.statSync(mainJsPath).size} bytes`);
        return true;
    }
    catch (error) {
        logger.error('[AutoDeploy] Build process failed', {
            error: error.message,
            stack: error.stack,
            cwd: process.cwd(),
            nodeVersion: process.version
        });
        return false;
    }
}
async function deployCommands() {
    logger.info('[AutoDeploy] Starting slash command deployment...');
    try {
        if (!process.env.BOT_TOKEN || !process.env.CLIENT_ID) {
            logger.error('[AutoDeploy] Missing required environment variables: BOT_TOKEN and/or CLIENT_ID');
            return false;
        }
        const commands = [];
        const commandsPath = path_1.default.join(process.cwd(), 'dist', 'commands');
        if (!fs_1.default.existsSync(commandsPath)) {
            logger.error('[AutoDeploy] Commands directory not found:', commandsPath);
            return false;
        }
        const commandFiles = fs_1.default.readdirSync(commandsPath).filter(file => file.endsWith('.js') &&
            !file.includes('index') &&
            !file.includes('Manager') &&
            !file.includes('Base'));
        const skipFiles = new Set([
            'enhancerole.js',
            'updatenames.js'
        ]);
        for (const file of commandFiles) {
            if (skipFiles.has(file)) {
                logger.debug(`[AutoDeploy] Skipping ${file} (handled by new architecture)`);
                continue;
            }
            try {
                const command = require(path_1.default.join(commandsPath, file));
                if (command.data) {
                    commands.push(command.data.toJSON());
                    logger.debug(`[AutoDeploy] Loaded legacy command: ${command.data.name}`);
                }
            }
            catch (error) {
                logger.warn(`[AutoDeploy] Failed to load ${file}:`, error);
            }
        }
        try {
            const { commandManager } = require(path_1.default.join(process.cwd(), 'dist', 'commands', 'CommandManager'));
            await commandManager.loadCommands();
            const existingCommandNames = new Set(commands.map(cmd => cmd.name));
            const newCommands = commandManager.getDiscordCommands();
            for (const [name, command] of newCommands) {
                if (command.data) {
                    if (existingCommandNames.has(name)) {
                        logger.debug(`[AutoDeploy] Skipping new architecture command '${name}' - legacy version already loaded`);
                        continue;
                    }
                    commands.push(command.data.toJSON());
                    logger.debug(`[AutoDeploy] Loaded new command: ${name}`);
                }
            }
        }
        catch (error) {
            logger.warn('[AutoDeploy] Failed to load new architecture commands:', error);
        }
        logger.info(`[AutoDeploy] Total commands to deploy: ${commands.length}`);
        if (commands.length === 0) {
            logger.error('[AutoDeploy] No commands found to deploy!');
            return false;
        }
        const rest = new discord_js_1.REST({ version: '10' }).setToken(process.env.BOT_TOKEN);
        logger.info('[AutoDeploy] Started refreshing application (/) commands...');
        const data = await rest.put(discord_js_1.Routes.applicationCommands(process.env.CLIENT_ID), { body: commands });
        logger.info(`[AutoDeploy] Successfully reloaded ${data.length} application (/) commands.`);
        return true;
    }
    catch (error) {
        logger.error('[AutoDeploy] Error deploying commands:', error);
        return false;
    }
}
async function deployRoleCommands() {
    logger.info('[AutoDeploy] Starting role command deployment...');
    try {
        if (!process.env.BOT_TOKEN || !process.env.CLIENT_ID) {
            logger.error('[AutoDeploy] Missing required environment variables: BOT_TOKEN and/or CLIENT_ID');
            return false;
        }
        const { REST, Routes, SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');
        const enhanceRoleCommand = new SlashCommandBuilder()
            .setName('enhancerole')
            .setDescription('Assign a prefix to all users with a specified role')
            .addRoleOption((option) => option
            .setName('role')
            .setDescription('Discord role to target')
            .setRequired(true))
            .addStringOption((option) => option
            .setName('prefix')
            .setDescription('Text/emoji prefix to prepend (max 10 characters)')
            .setRequired(true)
            .setMaxLength(10))
            .setDefaultMemberPermissions(PermissionFlagsBits.ManageNicknames);
        const updateNamesCommand = new SlashCommandBuilder()
            .setName('updatenames')
            .setDescription('Ensure all members have correct prefixes based on their current roles')
            .setDefaultMemberPermissions(PermissionFlagsBits.ManageNicknames);
        const commands = [
            enhanceRoleCommand.toJSON(),
            updateNamesCommand.toJSON()
        ];
        logger.info(`[AutoDeploy] Role commands to deploy: ${commands.length}`);
        const rest = new REST({ version: '10' }).setToken(process.env.BOT_TOKEN);
        const existingCommands = await rest.get(Routes.applicationCommands(process.env.CLIENT_ID));
        logger.debug(`[AutoDeploy] Found ${existingCommands.length} existing commands`);
        const allCommands = [...existingCommands];
        const commandsToRemove = ['enhancerole', 'updatenames'];
        const filteredCommands = allCommands.filter(cmd => !commandsToRemove.includes(cmd.name));
        const finalCommands = [...filteredCommands, ...commands];
        const data = await rest.put(Routes.applicationCommands(process.env.CLIENT_ID), { body: finalCommands });
        logger.info(`[AutoDeploy] Successfully deployed ${data.length} total commands.`);
        const newCommands = data.filter(cmd => commandsToRemove.includes(cmd.name));
        logger.info(`[AutoDeploy] New role prefix commands deployed: ${newCommands.length}`);
        return true;
    }
    catch (error) {
        logger.error('[AutoDeploy] Error deploying role commands:', error);
        if (error?.code === 50001) {
            logger.error('[AutoDeploy] Missing access - check bot permissions');
        }
        else if (error?.code === 50013) {
            logger.error('[AutoDeploy] Missing permissions - bot needs application.commands scope');
        }
        else if (error?.status === 401) {
            logger.error('[AutoDeploy] Invalid bot token');
        }
        else if (error?.status === 429) {
            logger.error('[AutoDeploy] Rate limited - too many requests');
        }
        return false;
    }
}
function validateEnvironment() {
    const required = ['BOT_TOKEN', 'CLIENT_ID', 'MONGODB_URI'];
    const missing = required.filter(key => !process.env[key]);
    return {
        valid: missing.length === 0,
        missing
    };
}
async function checkDependencies() {
    const issues = [];
    try {
        const tscPath = path_1.default.join(process.cwd(), 'node_modules', '.bin', 'tsc');
        if (!fs_1.default.existsSync(tscPath)) {
            issues.push('TypeScript compiler not found in node_modules');
        }
        try {
            require('discord.js');
        }
        catch (error) {
            issues.push('Discord.js not found or not properly installed');
        }
        const srcExists = fs_1.default.existsSync(path_1.default.join(process.cwd(), 'src'));
        if (!srcExists) {
            issues.push('Source directory (src/) not found');
        }
        const packageJsonExists = fs_1.default.existsSync(path_1.default.join(process.cwd(), 'package.json'));
        if (!packageJsonExists) {
            issues.push('package.json not found');
        }
    }
    catch (error) {
        issues.push(`Dependency check failed: ${error?.message || 'Unknown error'}`);
    }
    return {
        valid: issues.length === 0,
        issues
    };
}
