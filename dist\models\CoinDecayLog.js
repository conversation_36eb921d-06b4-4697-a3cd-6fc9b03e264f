"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const coinDecayLogSchema = new mongoose_1.Schema({
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    discordId: {
        type: String,
        required: [true, 'Discord ID is required'],
        index: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Discord ID must be a valid Discord snowflake'
        }
    },
    decayAmount: {
        type: Number,
        required: [true, 'Decay amount is required'],
        min: [0, 'Decay amount cannot be negative']
    },
    coinsBeforeDecay: {
        type: Number,
        required: [true, 'Coins before decay is required'],
        min: [0, 'Coins before decay cannot be negative']
    },
    coinsAfterDecay: {
        type: Number,
        required: [true, 'Coins after decay is required'],
        min: [0, 'Coins after decay cannot be negative']
    },
    decayPercentage: {
        type: Number,
        required: [true, 'Decay percentage is required'],
        min: [1, 'Decay percentage must be at least 1%'],
        max: [50, 'Decay percentage cannot exceed 50%']
    },
    inactivityDays: {
        type: Number,
        required: [true, 'Inactivity days is required'],
        min: [0, 'Inactivity days cannot be negative']
    },
    lastActivityDate: {
        type: Date,
        required: [true, 'Last activity date is required'],
        index: true
    },
    decayTimestamp: {
        type: Date,
        required: [true, 'Decay timestamp is required'],
        default: Date.now,
        index: true
    }
}, {
    timestamps: true
});
coinDecayLogSchema.index({ guildId: 1, discordId: 1 });
coinDecayLogSchema.index({ guildId: 1, decayTimestamp: -1 });
coinDecayLogSchema.index({ discordId: 1, decayTimestamp: -1 });
coinDecayLogSchema.index({ decayTimestamp: -1 });
exports.default = (0, mongoose_1.model)('CoinDecayLog', coinDecayLogSchema);
