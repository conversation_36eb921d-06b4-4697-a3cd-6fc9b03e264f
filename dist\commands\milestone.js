"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const MilestoneConfiguration_1 = __importDefault(require("../models/MilestoneConfiguration"));
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const milestoneService_1 = require("../services/milestoneService");
const milestoneAuditService_1 = require("../services/milestoneAuditService");
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('milestone')
        .setDescription('Manage automated milestone reward system')
        .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.Administrator)
        .addSubcommand(subcommand => subcommand
        .setName('status')
        .setDescription('View milestone system status and configurations'))
        .addSubcommand(subcommand => subcommand
        .setName('enable')
        .setDescription('Enable a milestone category or specific type')
        .addStringOption(option => option
        .setName('category')
        .setDescription('Milestone category to enable')
        .setRequired(false)
        .addChoices({ name: 'Time-Based Milestones', value: 'time_based' }, { name: 'Participation Diversity', value: 'participation_diversity' }, { name: 'Loyalty Milestones', value: 'loyalty' }, { name: 'Engagement Milestones', value: 'engagement' }))
        .addStringOption(option => option
        .setName('type')
        .setDescription('Specific milestone type to enable')
        .setRequired(false)
        .addChoices({ name: 'Login Streak', value: 'login_streak' }, { name: 'Channel Diversity (Daily)', value: 'channel_diversity_daily' }, { name: 'Channel Diversity (Weekly)', value: 'channel_diversity_weekly' }, { name: 'Reaction Diversity (Daily)', value: 'reaction_diversity_daily' }, { name: 'Reaction Diversity (Weekly)', value: 'reaction_diversity_weekly' }, { name: 'Voice Diversity (Daily)', value: 'voice_diversity_daily' }, { name: 'Voice Diversity (Weekly)', value: 'voice_diversity_weekly' }, { name: 'Server Anniversary', value: 'server_anniversary' }, { name: 'Voice Time (Daily)', value: 'voice_time_daily' }, { name: 'Voice Time (Weekly)', value: 'voice_time_weekly' }, { name: 'Message Count (Daily)', value: 'message_count_daily' }, { name: 'Message Count (Weekly)', value: 'message_count_weekly' })))
        .addSubcommand(subcommand => subcommand
        .setName('disable')
        .setDescription('Disable a milestone category or specific type')
        .addStringOption(option => option
        .setName('category')
        .setDescription('Milestone category to disable')
        .setRequired(false)
        .addChoices({ name: 'Time-Based Milestones', value: 'time_based' }, { name: 'Participation Diversity', value: 'participation_diversity' }, { name: 'Loyalty Milestones', value: 'loyalty' }, { name: 'Engagement Milestones', value: 'engagement' }))
        .addStringOption(option => option
        .setName('type')
        .setDescription('Specific milestone type to disable')
        .setRequired(false)
        .addChoices({ name: 'Login Streak', value: 'login_streak' }, { name: 'Channel Diversity (Daily)', value: 'channel_diversity_daily' }, { name: 'Channel Diversity (Weekly)', value: 'channel_diversity_weekly' }, { name: 'Reaction Diversity (Daily)', value: 'reaction_diversity_daily' }, { name: 'Reaction Diversity (Weekly)', value: 'reaction_diversity_weekly' }, { name: 'Voice Diversity (Daily)', value: 'voice_diversity_daily' }, { name: 'Voice Diversity (Weekly)', value: 'voice_diversity_weekly' }, { name: 'Server Anniversary', value: 'server_anniversary' }, { name: 'Voice Time (Daily)', value: 'voice_time_daily' }, { name: 'Voice Time (Weekly)', value: 'voice_time_weekly' }, { name: 'Message Count (Daily)', value: 'message_count_daily' }, { name: 'Message Count (Weekly)', value: 'message_count_weekly' })))
        .addSubcommand(subcommand => subcommand
        .setName('configure')
        .setDescription('Configure milestone settings')
        .addStringOption(option => option
        .setName('type')
        .setDescription('Milestone type to configure')
        .setRequired(true)
        .addChoices({ name: 'Login Streak', value: 'login_streak' }, { name: 'Channel Diversity (Daily)', value: 'channel_diversity_daily' }, { name: 'Channel Diversity (Weekly)', value: 'channel_diversity_weekly' }, { name: 'Reaction Diversity (Daily)', value: 'reaction_diversity_daily' }, { name: 'Reaction Diversity (Weekly)', value: 'reaction_diversity_weekly' }, { name: 'Voice Diversity (Daily)', value: 'voice_diversity_daily' }, { name: 'Voice Diversity (Weekly)', value: 'voice_diversity_weekly' }, { name: 'Server Anniversary', value: 'server_anniversary' }, { name: 'Voice Time (Daily)', value: 'voice_time_daily' }, { name: 'Voice Time (Weekly)', value: 'voice_time_weekly' }, { name: 'Message Count (Daily)', value: 'message_count_daily' }, { name: 'Message Count (Weekly)', value: 'message_count_weekly' }))
        .addIntegerOption(option => option
        .setName('reward')
        .setDescription('PLC reward amount (1-100)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(100))
        .addIntegerOption(option => option
        .setName('threshold')
        .setDescription('Achievement threshold (e.g., days for streak, channels for diversity)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(100))
        .addIntegerOption(option => option
        .setName('daily_limit')
        .setDescription('Maximum rewards per day for this type (1-10)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(10))
        .addIntegerOption(option => option
        .setName('weekly_limit')
        .setDescription('Maximum rewards per week for this type (1-50)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(50))
        .addIntegerOption(option => option
        .setName('cooldown_hours')
        .setDescription('Hours between same milestone achievements (1-168)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(168))
        .addBooleanOption(option => option
        .setName('diminishing_returns')
        .setDescription('Enable diminishing returns for repeated achievements')
        .setRequired(false)))
        .addSubcommand(subcommand => subcommand
        .setName('leaderboard')
        .setDescription('View milestone achievement leaderboard')
        .addIntegerOption(option => option
        .setName('limit')
        .setDescription('Number of users to show (1-20)')
        .setRequired(false)
        .setMinValue(1)
        .setMaxValue(20)))
        .addSubcommand(subcommand => subcommand
        .setName('setup')
        .setDescription('Initialize milestone system with default configurations')),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        const subcommand = interaction.options.getSubcommand();
        const guildId = interaction.guild.id;
        switch (subcommand) {
            case 'status':
                await handleStatus(interaction, guildId);
                break;
            case 'enable':
                await handleEnable(interaction, guildId);
                break;
            case 'disable':
                await handleDisable(interaction, guildId);
                break;
            case 'configure':
                await handleConfigure(interaction, guildId);
                break;
            case 'leaderboard':
                await handleLeaderboard(interaction, guildId);
                break;
            case 'setup':
                await handleSetup(interaction, guildId);
                break;
            default:
                throw new Error('Unknown subcommand');
        }
    })
};
async function handleStatus(interaction, guildId) {
    const configs = await MilestoneConfiguration_1.default.find({ guildId }).sort({ category: 1, milestoneType: 1 });
    if (configs.length === 0) {
        const embed = await (0, embedBuilder_1.createServerErrorEmbed)(guildId, 'No Milestone Configurations', 'No milestone configurations found. Use `/milestone setup` to initialize the system with default settings.');
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, `${embedBuilder_1.EMOJIS.MILESTONE.TROPHY} Milestone System Status`, 'Current milestone configurations and settings');
    const categories = ['time_based', 'participation_diversity', 'loyalty', 'engagement'];
    for (const category of categories) {
        const categoryConfigs = configs.filter(c => c.category === category);
        if (categoryConfigs.length === 0)
            continue;
        const categoryName = category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        const enabledCount = categoryConfigs.filter(c => c.enabled).length;
        const totalCount = categoryConfigs.length;
        let fieldValue = `**Status:** ${enabledCount}/${totalCount} enabled\n`;
        for (const config of categoryConfigs) {
            const status = config.enabled ? '✅' : '❌';
            const typeName = config.milestoneType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            const formattedReward = await (0, embedBuilder_1.formatServerCoins)(guildId, config.rewardAmount);
            fieldValue += `${status} ${typeName} - ${formattedReward} (Threshold: ${config.requirements.threshold || 'N/A'})\n`;
        }
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MILESTONE.STAR} ${categoryName}`,
            value: fieldValue,
            inline: false
        });
    }
    await interaction.reply({ embeds: [embed] });
}
async function handleEnable(interaction, guildId) {
    const category = interaction.options.getString('category');
    const type = interaction.options.getString('type');
    if (!category && !type) {
        const embed = (0, embedBuilder_1.createErrorEmbed)('Missing Parameters', 'Please specify either a category or specific milestone type to enable.');
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    let updateQuery = { guildId };
    let updateCount = 0;
    if (type) {
        updateQuery.milestoneType = type;
        const result = await MilestoneConfiguration_1.default.updateOne(updateQuery, { enabled: true });
        updateCount = result.modifiedCount;
    }
    else if (category) {
        updateQuery.category = category;
        const result = await MilestoneConfiguration_1.default.updateMany(updateQuery, { enabled: true });
        updateCount = result.modifiedCount;
    }
    if (updateCount === 0) {
        const embed = (0, embedBuilder_1.createErrorEmbed)('No Changes Made', 'No milestone configurations were found to enable. Use `/milestone setup` to initialize the system first.');
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    const target = type ? `milestone type "${type}"` : `category "${category}"`;
    const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Milestone System Updated`, `Successfully enabled ${target}. Updated ${updateCount} configuration(s).`);
    await milestoneAuditService_1.MilestoneAuditService.logAdminAction(guildId, interaction.user.id, 'system_enabled', `Enabled ${target}`, { target, updateCount });
    await interaction.reply({ embeds: [embed] });
}
async function handleDisable(interaction, guildId) {
    const category = interaction.options.getString('category');
    const type = interaction.options.getString('type');
    if (!category && !type) {
        const embed = (0, embedBuilder_1.createErrorEmbed)('Missing Parameters', 'Please specify either a category or specific milestone type to disable.');
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    let updateQuery = { guildId };
    let updateCount = 0;
    if (type) {
        updateQuery.milestoneType = type;
        const result = await MilestoneConfiguration_1.default.updateOne(updateQuery, { enabled: false });
        updateCount = result.modifiedCount;
    }
    else if (category) {
        updateQuery.category = category;
        const result = await MilestoneConfiguration_1.default.updateMany(updateQuery, { enabled: false });
        updateCount = result.modifiedCount;
    }
    if (updateCount === 0) {
        const embed = (0, embedBuilder_1.createErrorEmbed)('No Changes Made', 'No milestone configurations were found to disable.');
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    const target = type ? `milestone type "${type}"` : `category "${category}"`;
    const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Milestone System Updated`, `Successfully disabled ${target}. Updated ${updateCount} configuration(s).`);
    await milestoneAuditService_1.MilestoneAuditService.logAdminAction(guildId, interaction.user.id, 'system_disabled', `Disabled ${target}`, { target, updateCount });
    await interaction.reply({ embeds: [embed] });
}
async function handleConfigure(interaction, guildId) {
    const type = interaction.options.getString('type', true);
    const reward = interaction.options.getInteger('reward');
    const threshold = interaction.options.getInteger('threshold');
    const dailyLimit = interaction.options.getInteger('daily_limit');
    const weeklyLimit = interaction.options.getInteger('weekly_limit');
    const cooldownHours = interaction.options.getInteger('cooldown_hours');
    const diminishingReturns = interaction.options.getBoolean('diminishing_returns');
    const config = await MilestoneConfiguration_1.default.findOne({ guildId, milestoneType: type });
    if (!config) {
        const embed = (0, embedBuilder_1.createErrorEmbed)('Configuration Not Found', `No configuration found for milestone type "${type}". Use \`/milestone setup\` to initialize the system first.`);
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    const updates = {};
    if (reward !== null)
        updates.rewardAmount = reward;
    if (threshold !== null)
        updates['requirements.threshold'] = threshold;
    if (dailyLimit !== null)
        updates.maxRewardsPerDay = dailyLimit;
    if (weeklyLimit !== null)
        updates.maxRewardsPerWeek = weeklyLimit;
    if (cooldownHours !== null)
        updates['requirements.cooldownHours'] = cooldownHours;
    if (diminishingReturns !== null)
        updates.diminishingReturns = diminishingReturns;
    if (Object.keys(updates).length === 0) {
        const embed = (0, embedBuilder_1.createErrorEmbed)('No Changes Specified', 'Please specify at least one setting to update.');
        await interaction.reply({ embeds: [embed], ephemeral: true });
        return;
    }
    await MilestoneConfiguration_1.default.updateOne({ guildId, milestoneType: type }, updates);
    const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Milestone Configuration Updated`, `Successfully updated settings for "${type}" milestone.`);
    let changesText = '';
    if (reward !== null) {
        const formattedReward = await (0, embedBuilder_1.formatServerCoins)(guildId, reward);
        changesText += `• Reward Amount: ${formattedReward}\n`;
    }
    if (threshold !== null)
        changesText += `• Threshold: ${threshold}\n`;
    if (dailyLimit !== null)
        changesText += `• Daily Limit: ${dailyLimit}\n`;
    if (weeklyLimit !== null)
        changesText += `• Weekly Limit: ${weeklyLimit}\n`;
    if (cooldownHours !== null)
        changesText += `• Cooldown: ${cooldownHours} hours\n`;
    if (diminishingReturns !== null)
        changesText += `• Diminishing Returns: ${diminishingReturns ? 'Enabled' : 'Disabled'}\n`;
    embed.addFields({
        name: 'Changes Made',
        value: changesText,
        inline: false
    });
    await milestoneAuditService_1.MilestoneAuditService.logAdminAction(guildId, interaction.user.id, 'config_updated', `Updated configuration for ${type}`, { milestoneType: type, changes: updates });
    await interaction.reply({ embeds: [embed] });
}
async function handleLeaderboard(interaction, guildId) {
    const limit = interaction.options.getInteger('limit') || 10;
    try {
        const leaderboard = await (0, milestoneService_1.getMilestoneLeaderboard)(guildId, limit);
        if (leaderboard.length === 0) {
            const embed = (0, embedBuilder_1.createErrorEmbed)('No Milestone Data', 'No milestone achievements found for this server yet.');
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }
        const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, `${embedBuilder_1.EMOJIS.MILESTONE.TROPHY} Milestone Leaderboard`, `Top ${leaderboard.length} milestone achievers in this server`);
        let leaderboardText = '';
        for (let i = 0; i < leaderboard.length; i++) {
            const entry = leaderboard[i];
            const rank = i + 1;
            const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `${rank}.`;
            try {
                const user = await interaction.client.users.fetch(entry._id);
                const username = user.username;
                const formattedRewards = await (0, embedBuilder_1.formatServerCoins)(guildId, entry.totalRewards);
                leaderboardText += `${medal} **${username}** - ${formattedRewards} from ${entry.totalAchievements} achievements\n`;
            }
            catch (error) {
                const formattedRewards = await (0, embedBuilder_1.formatServerCoins)(guildId, entry.totalRewards);
                leaderboardText += `${medal} **Unknown User** - ${formattedRewards} from ${entry.totalAchievements} achievements\n`;
            }
        }
        embed.addFields({
            name: 'Rankings',
            value: leaderboardText,
            inline: false
        });
        await interaction.reply({ embeds: [embed] });
    }
    catch (error) {
        console.error('[Milestone Command] Error getting leaderboard:', error);
        throw new errorHandler_1.DatabaseError('Failed to retrieve milestone leaderboard');
    }
}
async function handleSetup(interaction, guildId) {
    try {
        const existingConfigs = await MilestoneConfiguration_1.default.countDocuments({ guildId });
        if (existingConfigs > 0) {
            const embed = (0, embedBuilder_1.createErrorEmbed)('System Already Initialized', `Found ${existingConfigs} existing milestone configurations. Use \`/milestone status\` to view them or \`/milestone configure\` to modify settings.`);
            await interaction.reply({ embeds: [embed], ephemeral: true });
            return;
        }
        await (0, milestoneService_1.createDefaultMilestoneConfigurations)(guildId);
        const embed = await (0, embedBuilder_1.createServerAdminEmbed)(guildId, `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Milestone System Initialized`, 'Successfully set up the milestone system with default configurations!');
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MILESTONE.STAR} Default Milestones Created`,
            value: `• **Login Streak** - 10 PLC for 3+ day streaks\n• **Server Anniversary** - 50 PLC monthly\n• **Channel Diversity** - 5 PLC for using 5+ channels daily\n• **Reaction Diversity** - 15 PLC for 10+ reaction types weekly\n• **Voice Activity** - 20 PLC for 60+ minutes weekly`,
            inline: false
        }, {
            name: `${embedBuilder_1.EMOJIS.ADMIN.SETTINGS} Next Steps`,
            value: `• Use \`/milestone status\` to view all configurations\n• Use \`/milestone configure\` to adjust settings\n• Use \`/milestone enable/disable\` to control categories\n• Use \`/milestones\` for users to view their progress`,
            inline: false
        });
        await milestoneAuditService_1.MilestoneAuditService.logAdminAction(guildId, interaction.user.id, 'config_created', 'Initialized milestone system with default configurations', { action: 'setup', configurationsCreated: 'default_set' });
        await interaction.reply({ embeds: [embed] });
    }
    catch (error) {
        console.error('[Milestone Command] Error setting up system:', error);
        throw new errorHandler_1.DatabaseError('Failed to initialize milestone system');
    }
}
