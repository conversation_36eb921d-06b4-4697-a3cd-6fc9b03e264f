"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const userActivityTrackerSchema = new mongoose_1.Schema({
    discordId: {
        type: String,
        required: [true, 'Discord ID is required'],
        index: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Discord ID must be a valid Discord snowflake'
        }
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        index: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    lastMessageTimestamp: {
        type: Date,
        required: [true, 'Last message timestamp is required'],
        default: Date.now,
        index: true
    }
}, {
    timestamps: true
});
userActivityTrackerSchema.index({ discordId: 1, guildId: 1 }, { unique: true });
userActivityTrackerSchema.index({ guildId: 1, lastMessageTimestamp: 1 });
userActivityTrackerSchema.index({ lastMessageTimestamp: 1 });
exports.default = (0, mongoose_1.model)('UserActivityTracker', userActivityTrackerSchema);
