/**
 * Migration 001: Create Trade System
 * Creates all trade-related collections and indexes
 */

import mongoose from 'mongoose';
import { ILogger } from '../src/core/interfaces';

export interface MigrationContext {
  db: mongoose.Connection;
  logger: ILogger;
  dryRun?: boolean;
}

export interface MigrationResult {
  success: boolean;
  message: string;
  changes: string[];
  errors: string[];
}

export class CreateTradeSystemMigration {
  private context: MigrationContext;

  constructor(context: MigrationContext) {
    this.context = context;
  }

  /**
   * Execute the migration
   */
  async up(): Promise<MigrationResult> {
    const { db, logger, dryRun = false } = this.context;
    const changes: string[] = [];
    const errors: string[] = [];

    try {
      logger.info('[Migration 001] Starting trade system creation...');

      // 1. Create Trade collection and indexes
      await this.createTradeCollection(changes, dryRun);

      // 2. Create EscrowTransaction collection and indexes
      await this.createEscrowTransactionCollection(changes, dryRun);

      // 3. Create DisputeCase collection and indexes
      await this.createDisputeCaseCollection(changes, dryRun);

      // 4. Create TradeConfirmation collection and indexes
      await this.createTradeConfirmationCollection(changes, dryRun);

      // 5. Create UserTradeStats collection and indexes
      await this.createUserTradeStatsCollection(changes, dryRun);

      // 6. Add trade-related indexes to existing User collection
      await this.addUserTradeIndexes(changes, dryRun);

      // 7. Add trade-related indexes to existing Transaction collection
      await this.addTransactionTradeIndexes(changes, dryRun);

      logger.info(`[Migration 001] Trade system creation completed. Changes: ${changes.length}`);

      return {
        success: true,
        message: 'Trade system created successfully',
        changes,
        errors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);
      logger.error('[Migration 001] Failed to create trade system', { error });

      return {
        success: false,
        message: `Migration failed: ${errorMessage}`,
        changes,
        errors
      };
    }
  }

  /**
   * Rollback the migration
   */
  async down(): Promise<MigrationResult> {
    const { db, logger, dryRun = false } = this.context;
    const changes: string[] = [];
    const errors: string[] = [];

    try {
      logger.info('[Migration 001] Starting trade system rollback...');

      if (!dryRun) {
        // Drop trade-related collections
        const collections = ['trades', 'escrowtransactions', 'disputecases', 'tradeconfirmations', 'usertradestats'];
        
        for (const collectionName of collections) {
          try {
            await db.dropCollection(collectionName);
            changes.push(`Dropped collection: ${collectionName}`);
          } catch (error) {
            if (error instanceof Error && error.message.includes('ns not found')) {
              // Collection doesn't exist, that's fine
              changes.push(`Collection ${collectionName} did not exist`);
            } else {
              throw error;
            }
          }
        }

        // Remove trade-related indexes from existing collections
        try {
          await db.collection('users').dropIndex('discordId_1_guildId_1');
          changes.push('Removed user trade index');
        } catch (error) {
          // Index might not exist
        }

        try {
          await db.collection('transactions').dropIndex('tradeId_1');
          changes.push('Removed transaction trade index');
        } catch (error) {
          // Index might not exist
        }
      } else {
        changes.push('DRY RUN: Would drop trade collections and indexes');
      }

      logger.info(`[Migration 001] Trade system rollback completed. Changes: ${changes.length}`);

      return {
        success: true,
        message: 'Trade system rollback completed successfully',
        changes,
        errors
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);
      logger.error('[Migration 001] Failed to rollback trade system', { error });

      return {
        success: false,
        message: `Rollback failed: ${errorMessage}`,
        changes,
        errors
      };
    }
  }

  private async createTradeCollection(changes: string[], dryRun: boolean): Promise<void> {
    const { db } = this.context;

    if (!dryRun) {
      // Create indexes for Trade collection
      const tradeIndexes = [
        { key: { tradeId: 1 }, options: { unique: true } },
        { key: { guildId: 1, state: 1 }, options: {} },
        { key: { sellerId: 1, guildId: 1 }, options: {} },
        { key: { buyerId: 1, guildId: 1 }, options: {} },
        { key: { expiresAt: 1 }, options: {} },
        { key: { createdAt: -1 }, options: {} },
        { key: { disputeId: 1 }, options: { sparse: true } },
        { key: { guildId: 1, sellerId: 1, buyerId: 1, state: 1 }, options: {} }
      ];

      for (const index of tradeIndexes) {
        await db.collection('trades').createIndex(index.key, index.options);
        changes.push(`Created trade index: ${JSON.stringify(index.key)}`);
      }
    } else {
      changes.push('DRY RUN: Would create Trade collection with 8 indexes');
    }
  }

  private async createEscrowTransactionCollection(changes: string[], dryRun: boolean): Promise<void> {
    const { db } = this.context;

    if (!dryRun) {
      const escrowIndexes = [
        { key: { escrowId: 1 }, options: { unique: true } },
        { key: { tradeId: 1 }, options: {} },
        { key: { discordId: 1, guildId: 1 }, options: {} },
        { key: { status: 1 }, options: {} },
        { key: { timestamp: -1 }, options: {} },
        { key: { transactionType: 1 }, options: {} }
      ];

      for (const index of escrowIndexes) {
        await db.collection('escrowtransactions').createIndex(index.key, index.options);
        changes.push(`Created escrow index: ${JSON.stringify(index.key)}`);
      }
    } else {
      changes.push('DRY RUN: Would create EscrowTransaction collection with 6 indexes');
    }
  }

  private async createDisputeCaseCollection(changes: string[], dryRun: boolean): Promise<void> {
    const { db } = this.context;

    if (!dryRun) {
      const disputeIndexes = [
        { key: { disputeId: 1 }, options: { unique: true } },
        { key: { tradeId: 1 }, options: { unique: true } },
        { key: { guildId: 1, status: 1 }, options: {} },
        { key: { initiatorId: 1 }, options: {} },
        { key: { respondentId: 1 }, options: {} },
        { key: { priority: 1, createdAt: -1 }, options: {} },
        { key: { status: 1, createdAt: -1 }, options: {} },
        { key: { evidenceDeadline: 1 }, options: {} }
      ];

      for (const index of disputeIndexes) {
        await db.collection('disputecases').createIndex(index.key, index.options);
        changes.push(`Created dispute index: ${JSON.stringify(index.key)}`);
      }
    } else {
      changes.push('DRY RUN: Would create DisputeCase collection with 8 indexes');
    }
  }

  private async createTradeConfirmationCollection(changes: string[], dryRun: boolean): Promise<void> {
    const { db } = this.context;

    if (!dryRun) {
      const confirmationIndexes = [
        { key: { confirmationId: 1 }, options: { unique: true } },
        { key: { tradeId: 1, discordId: 1 }, options: {} },
        { key: { discordId: 1, guildId: 1 }, options: {} },
        { key: { confirmationType: 1 }, options: {} },
        { key: { confirmedAt: -1 }, options: {} }
      ];

      for (const index of confirmationIndexes) {
        await db.collection('tradeconfirmations').createIndex(index.key, index.options);
        changes.push(`Created confirmation index: ${JSON.stringify(index.key)}`);
      }
    } else {
      changes.push('DRY RUN: Would create TradeConfirmation collection with 5 indexes');
    }
  }

  private async createUserTradeStatsCollection(changes: string[], dryRun: boolean): Promise<void> {
    const { db } = this.context;

    if (!dryRun) {
      const statsIndexes = [
        { key: { discordId: 1, guildId: 1 }, options: { unique: true } },
        { key: { guildId: 1, totalVolumeTraded: -1 }, options: {} },
        { key: { guildId: 1, reputationScore: -1 }, options: {} },
        { key: { isRestricted: 1, restrictedUntil: 1 }, options: {} },
        { key: { lastResetDate: 1 }, options: {} },
        { key: { guildId: 1, totalTrades: -1 }, options: {} }
      ];

      for (const index of statsIndexes) {
        await db.collection('usertradestats').createIndex(index.key, index.options);
        changes.push(`Created stats index: ${JSON.stringify(index.key)}`);
      }
    } else {
      changes.push('DRY RUN: Would create UserTradeStats collection with 6 indexes');
    }
  }

  private async addUserTradeIndexes(changes: string[], dryRun: boolean): Promise<void> {
    const { db } = this.context;

    if (!dryRun) {
      // Add compound index for user lookups in trade context
      try {
        await db.collection('users').createIndex({ discordId: 1, guildId: 1 });
        changes.push('Added user compound index for trades');
      } catch (error) {
        // Index might already exist
        changes.push('User compound index already exists');
      }
    } else {
      changes.push('DRY RUN: Would add user compound index');
    }
  }

  private async addTransactionTradeIndexes(changes: string[], dryRun: boolean): Promise<void> {
    const { db } = this.context;

    if (!dryRun) {
      // Add trade ID index to transactions
      try {
        await db.collection('transactions').createIndex({ tradeId: 1 });
        changes.push('Added transaction trade index');
      } catch (error) {
        // Index might already exist
        changes.push('Transaction trade index already exists');
      }
    } else {
      changes.push('DRY RUN: Would add transaction trade index');
    }
  }
}
