"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const transactionSchema = new mongoose_1.Schema({
    discordId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Discord ID must be a valid Discord snowflake'
        }
    },
    guildId: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return !!(v && v.trim().length > 0 && /^\d{17,20}$/.test(v.trim()));
            },
            message: 'Guild ID must be a valid Discord snowflake'
        }
    },
    type: { type: String, enum: ['pay', 'role_achievement', 'give', 'fine', 'reaction', 'tax', 'starter_balance', 'content_submission', 'content_reward', 'milestone', 'trade_escrow', 'trade_release', 'trade_refund', 'election_vote', 'salary', 'coin_decay'], required: true },
    amount: { type: Number, required: true },
    timestamp: { type: Date, default: Date.now },
    details: { type: String },
    tradeId: { type: String, index: true },
    electionId: { type: String, index: true },
    roleId: { type: String, index: true }
}, {
    timestamps: true
});
transactionSchema.index({ guildId: 1, discordId: 1, timestamp: -1 });
transactionSchema.index({ guildId: 1, timestamp: -1 });
transactionSchema.index({ guildId: 1, type: 1, timestamp: -1 });
transactionSchema.index({ guildId: 1, amount: -1, timestamp: -1 });
exports.default = mongoose_1.default.model('Transaction', transactionSchema);
