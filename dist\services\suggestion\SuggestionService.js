"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SuggestionService = void 0;
const discord_js_1 = require("discord.js");
const BaseService_1 = require("../base/BaseService");
const SuggestionConfig_1 = require("../../models/SuggestionConfig");
const Suggestion_1 = require("../../models/Suggestion");
const errorHandler_1 = require("../../utils/errorHandler");
const SuggestionCleanupService_1 = require("./SuggestionCleanupService");
const crypto_1 = require("crypto");
const configurableConstants_1 = __importDefault(require("../../config/configurableConstants"));
class SuggestionService extends BaseService_1.BaseService {
    constructor(app) {
        super(app);
        this.name = 'SuggestionService';
        if (app) {
            this.cleanupService = new SuggestionCleanupService_1.SuggestionCleanupService(app);
        }
    }
    async onInitialize() {
        this.logger.info('[SuggestionService] Initializing suggestion system...');
        if (this.app && this.cleanupService) {
            await this.cleanupService.initialize?.();
        }
        this.logger.info('[SuggestionService] Suggestion system initialized');
    }
    async onShutdown() {
        this.logger.info('[SuggestionService] Shutting down suggestion system...');
        if (this.cleanupService) {
            await this.cleanupService.shutdown?.();
        }
        this.logger.info('[SuggestionService] Suggestion system shutdown complete');
    }
    async configureSuggestionSystem(config) {
        try {
            this.validateSuggestionConfig(config);
            const existingConfig = await SuggestionConfig_1.SuggestionConfig.findOne({ guildId: config.guildId });
            if (existingConfig) {
                existingConfig.enabled = config.enabled;
                existingConfig.channelId = config.channelId;
                existingConfig.allowedRoles = config.allowedRoles;
                await existingConfig.save();
                this.logOperation('Updated suggestion system configuration', {
                    guildId: config.guildId,
                    enabled: config.enabled,
                    channelId: config.channelId,
                    allowedRolesCount: config.allowedRoles.length
                });
                return existingConfig;
            }
            else {
                const newConfig = new SuggestionConfig_1.SuggestionConfig(config);
                await newConfig.save();
                this.logOperation('Created suggestion system configuration', {
                    guildId: config.guildId,
                    enabled: config.enabled,
                    channelId: config.channelId,
                    allowedRolesCount: config.allowedRoles.length
                });
                return newConfig;
            }
        }
        catch (error) {
            this.handleError(error, { operation: 'configure_suggestion_system', config });
            throw error;
        }
    }
    async getSuggestionConfig(guildId) {
        try {
            return await SuggestionConfig_1.SuggestionConfig.findOne({ guildId });
        }
        catch (error) {
            this.handleError(error, { operation: 'get_suggestion_config', guildId });
            throw error;
        }
    }
    async isSuggestionSystemEnabled(guildId) {
        try {
            const config = await this.getSuggestionConfig(guildId);
            return config?.enabled === true && !!config.channelId;
        }
        catch (error) {
            this.handleError(error, { operation: 'is_suggestion_system_enabled', guildId });
            return false;
        }
    }
    async canUserCreateSuggestions(guildId, member) {
        try {
            const config = await this.getSuggestionConfig(guildId);
            if (!config || !config.enabled) {
                return false;
            }
            if (config.allowedRoles.length === 0) {
                return true;
            }
            return config.allowedRoles.some(roleId => member.roles.cache.has(roleId));
        }
        catch (error) {
            this.handleError(error, { operation: 'can_user_create_suggestions', guildId, userId: member.id });
            return false;
        }
    }
    async createSuggestion(guildId, channelId, authorId, content, client) {
        try {
            const suggestionId = (0, crypto_1.randomUUID)();
            const embed = await this.createSuggestionEmbed(suggestionId, authorId, content, 0, 0, client, guildId);
            const actionRow = this.createSuggestionButtons(suggestionId, authorId);
            const channel = await client.channels.fetch(channelId);
            const message = await channel.send({ embeds: [embed], components: [actionRow] });
            const suggestion = new Suggestion_1.Suggestion({
                suggestionId,
                guildId,
                channelId,
                messageId: message.id,
                authorId,
                content,
                votes: [],
                upvoteCount: 0,
                downvoteCount: 0,
                isEdited: false,
                editHistory: []
            });
            await suggestion.save();
            this.logOperation('Created suggestion', {
                suggestionId,
                guildId,
                channelId,
                authorId,
                messageId: message.id
            });
            return suggestion;
        }
        catch (error) {
            this.handleError(error, { operation: 'create_suggestion', guildId, channelId, authorId });
            throw error;
        }
    }
    async voteOnSuggestion(suggestionId, userId, voteType, client) {
        try {
            const suggestion = await Suggestion_1.Suggestion.findOne({ suggestionId });
            if (!suggestion) {
                throw new errorHandler_1.ValidationError('Suggestion not found');
            }
            const existingVoteIndex = suggestion.votes.findIndex(vote => vote.userId === userId);
            let previousVote = null;
            if (existingVoteIndex !== -1) {
                previousVote = suggestion.votes[existingVoteIndex].type;
                if (previousVote === voteType) {
                    suggestion.votes.splice(existingVoteIndex, 1);
                    if (voteType === 'upvote') {
                        suggestion.upvoteCount = Math.max(0, suggestion.upvoteCount - 1);
                    }
                    else {
                        suggestion.downvoteCount = Math.max(0, suggestion.downvoteCount - 1);
                    }
                    await suggestion.save();
                    await this.updateSuggestionEmbed(suggestion, client);
                    return {
                        success: true,
                        previousVote,
                        newVote: voteType,
                        upvoteCount: suggestion.upvoteCount,
                        downvoteCount: suggestion.downvoteCount
                    };
                }
                else {
                    suggestion.votes[existingVoteIndex].type = voteType;
                    suggestion.votes[existingVoteIndex].timestamp = new Date();
                    if (voteType === 'upvote') {
                        suggestion.upvoteCount++;
                        suggestion.downvoteCount = Math.max(0, suggestion.downvoteCount - 1);
                    }
                    else {
                        suggestion.downvoteCount++;
                        suggestion.upvoteCount = Math.max(0, suggestion.upvoteCount - 1);
                    }
                }
            }
            else {
                suggestion.votes.push({
                    userId,
                    type: voteType,
                    timestamp: new Date()
                });
                if (voteType === 'upvote') {
                    suggestion.upvoteCount++;
                }
                else {
                    suggestion.downvoteCount++;
                }
            }
            await suggestion.save();
            await this.updateSuggestionEmbed(suggestion, client);
            this.logOperation('Voted on suggestion', {
                suggestionId,
                userId,
                voteType,
                previousVote,
                upvoteCount: suggestion.upvoteCount,
                downvoteCount: suggestion.downvoteCount
            });
            return {
                success: true,
                previousVote,
                newVote: voteType,
                upvoteCount: suggestion.upvoteCount,
                downvoteCount: suggestion.downvoteCount
            };
        }
        catch (error) {
            this.handleError(error, { operation: 'vote_on_suggestion', suggestionId, userId, voteType });
            throw error;
        }
    }
    async editSuggestion(suggestionId, newContent, client) {
        try {
            const suggestion = await Suggestion_1.Suggestion.findOne({ suggestionId });
            if (!suggestion) {
                throw new errorHandler_1.ValidationError('Suggestion not found');
            }
            suggestion.editHistory.push({
                previousContent: suggestion.content,
                editedAt: new Date()
            });
            suggestion.content = newContent;
            suggestion.isEdited = true;
            await suggestion.save();
            await this.updateSuggestionEmbed(suggestion, client);
            this.logOperation('Edited suggestion', {
                suggestionId,
                newContentLength: newContent.length,
                editCount: suggestion.editHistory.length
            });
            return suggestion;
        }
        catch (error) {
            this.handleError(error, { operation: 'edit_suggestion', suggestionId });
            throw error;
        }
    }
    async deleteSuggestion(suggestionId, client) {
        try {
            const suggestion = await Suggestion_1.Suggestion.findOne({ suggestionId });
            if (!suggestion) {
                throw new errorHandler_1.ValidationError('Suggestion not found');
            }
            try {
                const channel = await client.channels.fetch(suggestion.channelId);
                const message = await channel.messages.fetch(suggestion.messageId);
                await message.delete();
            }
            catch (discordError) {
                this.logger.warn('Failed to delete Discord message for suggestion', {
                    suggestionId,
                    messageId: suggestion.messageId,
                    error: discordError
                });
            }
            await Suggestion_1.Suggestion.deleteOne({ suggestionId });
            this.logOperation('Deleted suggestion', {
                suggestionId,
                guildId: suggestion.guildId,
                authorId: suggestion.authorId
            });
            return true;
        }
        catch (error) {
            this.handleError(error, { operation: 'delete_suggestion', suggestionId });
            throw error;
        }
    }
    async getSuggestion(suggestionId) {
        try {
            return await Suggestion_1.Suggestion.findOne({ suggestionId });
        }
        catch (error) {
            this.handleError(error, { operation: 'get_suggestion', suggestionId });
            throw error;
        }
    }
    async canUserDeleteSuggestion(suggestionId, userId, member) {
        try {
            const suggestion = await this.getSuggestion(suggestionId);
            if (!suggestion) {
                return false;
            }
            if (suggestion.authorId === userId) {
                return true;
            }
            if (member.permissions.has('Administrator')) {
                return true;
            }
            return false;
        }
        catch (error) {
            this.handleError(error, { operation: 'can_user_delete_suggestion', suggestionId, userId });
            return false;
        }
    }
    async createSuggestionEmbed(suggestionId, authorId, content, upvoteCount, downvoteCount, client, guildId, isEdited = false) {
        const embedColor = await configurableConstants_1.default.getEmbedColor(guildId);
        const embed = new discord_js_1.EmbedBuilder()
            .setColor(embedColor)
            .setTitle('💡 Suggestion')
            .setDescription(content)
            .addFields({ name: '🔺 Upvotes', value: upvoteCount.toString(), inline: true }, { name: '🔻 Downvotes', value: downvoteCount.toString(), inline: true }, { name: '🆔 ID', value: suggestionId.substring(0, 8), inline: true })
            .setFooter({
            text: `Suggested by ${client.users.cache.get(authorId)?.username || 'Unknown User'}${isEdited ? ' • Edited' : ''}`,
            iconURL: client.users.cache.get(authorId)?.displayAvatarURL() || undefined
        })
            .setTimestamp();
        return embed;
    }
    createSuggestionButtons(suggestionId, authorId) {
        return new discord_js_1.ActionRowBuilder()
            .addComponents(new discord_js_1.ButtonBuilder()
            .setCustomId(`suggestion_upvote_${suggestionId}`)
            .setLabel('Upvote')
            .setEmoji('🔺')
            .setStyle(discord_js_1.ButtonStyle.Primary), new discord_js_1.ButtonBuilder()
            .setCustomId(`suggestion_downvote_${suggestionId}`)
            .setLabel('Downvote')
            .setEmoji('🔻')
            .setStyle(discord_js_1.ButtonStyle.Secondary), new discord_js_1.ButtonBuilder()
            .setCustomId(`suggestion_edit_${suggestionId}_${authorId}`)
            .setLabel('Edit')
            .setEmoji('✏️')
            .setStyle(discord_js_1.ButtonStyle.Success), new discord_js_1.ButtonBuilder()
            .setCustomId(`suggestion_delete_${suggestionId}_${authorId}`)
            .setLabel('Delete')
            .setEmoji('🗑️')
            .setStyle(discord_js_1.ButtonStyle.Danger));
    }
    async updateSuggestionEmbed(suggestion, client) {
        try {
            const channel = await client.channels.fetch(suggestion.channelId);
            const message = await channel.messages.fetch(suggestion.messageId);
            const updatedEmbed = await this.createSuggestionEmbed(suggestion.suggestionId, suggestion.authorId, suggestion.content, suggestion.upvoteCount, suggestion.downvoteCount, client, suggestion.guildId, suggestion.isEdited);
            const actionRow = this.createSuggestionButtons(suggestion.suggestionId, suggestion.authorId);
            await message.edit({ embeds: [updatedEmbed], components: [actionRow] });
        }
        catch (error) {
            this.logger.warn('Failed to update suggestion embed', {
                suggestionId: suggestion.suggestionId,
                messageId: suggestion.messageId,
                error
            });
        }
    }
    async triggerCleanup() {
        try {
            if (!this.cleanupService) {
                throw new Error('Cleanup service not available');
            }
            return await this.cleanupService.triggerManualCleanup();
        }
        catch (error) {
            this.handleError(error, { operation: 'trigger_cleanup' });
            throw error;
        }
    }
    async getCleanupStats() {
        try {
            if (!this.cleanupService) {
                const totalSuggestions = await Suggestion_1.Suggestion.countDocuments();
                const expiredSuggestions = await Suggestion_1.Suggestion.countDocuments({
                    expiresAt: { $lt: new Date() }
                });
                return {
                    totalSuggestions,
                    expiredSuggestions,
                    nextCleanupIn: 0
                };
            }
            return await this.cleanupService.getCleanupStats();
        }
        catch (error) {
            this.handleError(error, { operation: 'get_cleanup_stats' });
            throw error;
        }
    }
    validateSuggestionConfig(config) {
        if (!config.guildId || !/^\d{17,20}$/.test(config.guildId)) {
            throw new errorHandler_1.ValidationError('Invalid guild ID');
        }
        if (config.enabled) {
            if (!config.channelId || !/^\d{17,20}$/.test(config.channelId)) {
                throw new errorHandler_1.ValidationError('Invalid channel ID');
            }
        }
        if (config.allowedRoles && config.allowedRoles.length > 0) {
            for (const roleId of config.allowedRoles) {
                if (!/^\d{17,20}$/.test(roleId)) {
                    throw new errorHandler_1.ValidationError(`Invalid role ID: ${roleId}`);
                }
            }
        }
    }
}
exports.SuggestionService = SuggestionService;
