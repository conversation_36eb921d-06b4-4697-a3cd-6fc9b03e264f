/**
 * Database Index Fix Script
 * Drops old indexes and recreates them to match the current schema
 */

const mongoose = require('mongoose');

async function fixDatabaseIndexes() {
  console.log('🔧 Starting Database Index Fix...\n');

  try {
    // Connect to database
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/banker-test';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to database');

    // Get the collections
    const db = mongoose.connection.db;
    
    // Fix ElectionCandidate collection
    console.log('\n📋 Fixing ElectionCandidate indexes...');
    
    try {
      // Drop all indexes except _id
      const candidateIndexes = await db.collection('electioncandidates').indexes();
      console.log('Current indexes:', candidateIndexes.map(idx => idx.name));
      
      for (const index of candidateIndexes) {
        if (index.name !== '_id_') {
          try {
            await db.collection('electioncandidates').dropIndex(index.name);
            console.log(`✅ Dropped index: ${index.name}`);
          } catch (error) {
            console.log(`⚠️ Could not drop index ${index.name}:`, error.message);
          }
        }
      }
      
      // Recreate the correct indexes
      await db.collection('electioncandidates').createIndex({ electionId: 1, withdrawn: 1, disqualified: 1 });
      console.log('✅ Created index: electionId_1_withdrawn_1_disqualified_1');
      
      await db.collection('electioncandidates').createIndex({ electionId: 1, nominationTime: 1 });
      console.log('✅ Created index: electionId_1_nominationTime_1');
      
      await db.collection('electioncandidates').createIndex({ guildId: 1, userId: 1 });
      console.log('✅ Created index: guildId_1_userId_1');
      
      await db.collection('electioncandidates').createIndex({ electionId: 1, userId: 1 }, { unique: true });
      console.log('✅ Created unique index: electionId_1_userId_1');
      
    } catch (error) {
      console.error('❌ Error fixing ElectionCandidate indexes:', error.message);
    }

    // Fix Election collection
    console.log('\n🗳️ Fixing Election indexes...');
    
    try {
      // Drop all indexes except _id
      const electionIndexes = await db.collection('elections').indexes();
      console.log('Current indexes:', electionIndexes.map(idx => idx.name));
      
      for (const index of electionIndexes) {
        if (index.name !== '_id_') {
          try {
            await db.collection('elections').dropIndex(index.name);
            console.log(`✅ Dropped index: ${index.name}`);
          } catch (error) {
            console.log(`⚠️ Could not drop index ${index.name}:`, error.message);
          }
        }
      }
      
      // Recreate the correct indexes
      await db.collection('elections').createIndex({ electionId: 1 }, { unique: true });
      console.log('✅ Created unique index: electionId_1');
      
      await db.collection('elections').createIndex({ guildId: 1 });
      console.log('✅ Created index: guildId_1');
      
      await db.collection('elections').createIndex({ guildId: 1, status: 1 });
      console.log('✅ Created index: guildId_1_status_1');
      
      await db.collection('elections').createIndex({ guildId: 1, createdAt: -1 });
      console.log('✅ Created index: guildId_1_createdAt_-1');
      
      await db.collection('elections').createIndex({ status: 1, createdAt: -1 });
      console.log('✅ Created index: status_1_createdAt_-1');
      
    } catch (error) {
      console.error('❌ Error fixing Election indexes:', error.message);
    }

    // Fix ElectionVote collection
    console.log('\n🗳️ Fixing ElectionVote indexes...');
    
    try {
      // Drop all indexes except _id
      const voteIndexes = await db.collection('electionvotes').indexes();
      console.log('Current indexes:', voteIndexes.map(idx => idx.name));
      
      for (const index of voteIndexes) {
        if (index.name !== '_id_') {
          try {
            await db.collection('electionvotes').dropIndex(index.name);
            console.log(`✅ Dropped index: ${index.name}`);
          } catch (error) {
            console.log(`⚠️ Could not drop index ${index.name}:`, error.message);
          }
        }
      }
      
      // Recreate the correct indexes
      await db.collection('electionvotes').createIndex({ voteId: 1 }, { unique: true });
      console.log('✅ Created unique index: voteId_1');
      
      await db.collection('electionvotes').createIndex({ electionId: 1 });
      console.log('✅ Created index: electionId_1');
      
      await db.collection('electionvotes').createIndex({ electionId: 1, voterId: 1 });
      console.log('✅ Created index: electionId_1_voterId_1');
      
      await db.collection('electionvotes').createIndex({ electionId: 1, candidateId: 1 });
      console.log('✅ Created index: electionId_1_candidateId_1');
      
      await db.collection('electionvotes').createIndex({ electionId: 1, status: 1 });
      console.log('✅ Created index: electionId_1_status_1');
      
      await db.collection('electionvotes').createIndex({ guildId: 1 });
      console.log('✅ Created index: guildId_1');
      
    } catch (error) {
      console.error('❌ Error fixing ElectionVote indexes:', error.message);
    }

    console.log('\n🎉 Database index fix completed successfully!');

  } catch (error) {
    console.error('\n❌ Database index fix failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from database');
  }
}

// Run the fix
if (require.main === module) {
  fixDatabaseIndexes().catch(console.error);
}

module.exports = { fixDatabaseIndexes };
