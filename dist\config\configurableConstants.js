"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurableConstants = void 0;
const serverConfigurationService_1 = __importDefault(require("../services/serverConfigurationService"));
class ConfigurableConstants {
    static async getEconomyConstants(guildId) {
        const config = await serverConfigurationService_1.default.getServerConfig(guildId);
        return {
            CURRENCY_NAME: config.coinName,
            CURRENCY_SYMBOL: config.coinSymbol,
            CURRENCY_EMOJI: '🪙',
            REACTION_REWARD_AMOUNT: 5,
            REACTION_RATE_LIMIT_SECONDS: 30,
            REACTION_MESSAGE_AGE_LIMIT_HOURS: 24,
            TAX_COLLECTION_INTERVAL_HOURS: 1,
            DEFAULT_STARTER_BALANCE: 0,
        };
    }
    static async getNationName(guildId) {
        const timestamp = new Date().toISOString();
        console.log(`[CONFIG DEBUG ${timestamp}] getNationName() called for guild: ${guildId}`);
        try {
            const result = await serverConfigurationService_1.default.getNationName(guildId);
            console.log(`[CONFIG DEBUG ${timestamp}] getNationName() SUCCESS - Guild: ${guildId}, Result: "${result}"`);
            return result;
        }
        catch (error) {
            console.error(`[CONFIG DEBUG ${timestamp}] getNationName() ERROR - Guild: ${guildId}, Error:`, error);
            throw error;
        }
    }
    static async getCoinName(guildId) {
        const timestamp = new Date().toISOString();
        console.log(`[CONFIG DEBUG ${timestamp}] getCoinName() called for guild: ${guildId}`);
        try {
            const result = await serverConfigurationService_1.default.getCoinName(guildId);
            console.log(`[CONFIG DEBUG ${timestamp}] getCoinName() SUCCESS - Guild: ${guildId}, Result: "${result}"`);
            return result;
        }
        catch (error) {
            console.error(`[CONFIG DEBUG ${timestamp}] getCoinName() ERROR - Guild: ${guildId}, Error:`, error);
            throw error;
        }
    }
    static async getCoinSymbol(guildId) {
        const timestamp = new Date().toISOString();
        console.log(`[CONFIG DEBUG ${timestamp}] getCoinSymbol() called for guild: ${guildId}`);
        try {
            const result = await serverConfigurationService_1.default.getCoinSymbol(guildId);
            console.log(`[CONFIG DEBUG ${timestamp}] getCoinSymbol() SUCCESS - Guild: ${guildId}, Result: "${result}"`);
            return result;
        }
        catch (error) {
            console.error(`[CONFIG DEBUG ${timestamp}] getCoinSymbol() ERROR - Guild: ${guildId}, Error:`, error);
            throw error;
        }
    }
    static async getEmbedColor(guildId) {
        return await serverConfigurationService_1.default.getEmbedColor(guildId);
    }
    static async formatCoins(guildId, amount) {
        const config = await serverConfigurationService_1.default.getServerConfig(guildId);
        return `🪙 **${amount.toLocaleString()}** ${config.coinName}`;
    }
    static async formatCoinsWithSymbol(guildId, amount) {
        const config = await serverConfigurationService_1.default.getServerConfig(guildId);
        return `🪙 **${amount.toLocaleString()} ${config.coinSymbol}**`;
    }
    static async getEmbedFooter(guildId) {
        return await serverConfigurationService_1.default.getEmbedFooter(guildId);
    }
    static async getDynastyConstants(guildId) {
        const coinSymbol = await this.getCoinSymbol(guildId);
        return {
            MIN_PLC_REQUIREMENT: 5000,
            MIN_TENURE_DAYS: 30,
            MILESTONE_BONUS_PERCENTAGE: 10,
            MAX_LEVEL: 10,
            BASE_LEVEL_REQUIREMENT: 1000,
            LEVEL_MULTIPLIER: 1.5,
            COIN_SYMBOL: coinSymbol,
        };
    }
    static async getTradeConstants(guildId) {
        const coinSymbol = await this.getCoinSymbol(guildId);
        return {
            MAX_ACTIVE_TRADES_PER_USER: 5,
            TRADE_TIMEOUT_HOURS: 24,
            ESCROW_TIMEOUT_HOURS: 72,
            DISPUTE_TIMEOUT_DAYS: 7,
            MIN_TRADE_AMOUNT: 1,
            MAX_TRADE_AMOUNT: 1000000,
            TRADE_FEE_PERCENTAGE: 0.02,
            DISPUTE_FEE_AMOUNT: 100,
            COIN_SYMBOL: coinSymbol,
        };
    }
    static getMilestoneConstants() {
        return {
            MAX_WEEKLY_MILESTONE_REWARDS: 50,
            MAX_DAILY_MILESTONE_REWARDS: 10,
            SUSPICIOUS_ACTIVITY_THRESHOLD: 5,
            BLACKLIST_DURATION_HOURS: 24,
            DEFAULT_LOGIN_STREAK_REWARD: 10,
            DEFAULT_MESSAGE_COUNT_REWARD: 15,
            DEFAULT_VOICE_TIME_REWARD: 20,
            DEFAULT_DIMINISHING_FACTOR: 0.9,
        };
    }
    static getValidationConstants() {
        return {
            MAX_REASON_LENGTH: 500,
            MAX_DESCRIPTION_LENGTH: 1000,
            MAX_TITLE_LENGTH: 100,
            MIN_AMOUNT: 1,
            MAX_AMOUNT: 1000000,
            COMMAND_COOLDOWN_SECONDS: 3,
            HEAVY_COMMAND_COOLDOWN_SECONDS: 10,
            DEFAULT_PAGE_SIZE: 10,
            MAX_PAGE_SIZE: 25,
        };
    }
    static async getAllServerConstants(guildId) {
        const config = await serverConfigurationService_1.default.getServerConfig(guildId);
        return {
            nationName: config.nationName,
            coinName: config.coinName,
            coinSymbol: config.coinSymbol,
            embedColor: config.embedColor,
            embedFooter: `${config.nationName} Economy System`,
            formatCoins: (amount) => `🪙 **${amount.toLocaleString()}** ${config.coinName}`,
            formatCoinsWithSymbol: (amount) => `🪙 **${amount.toLocaleString()} ${config.coinSymbol}**`,
        };
    }
}
exports.ConfigurableConstants = ConfigurableConstants;
exports.default = ConfigurableConstants;
