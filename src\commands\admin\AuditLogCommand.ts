/**
 * Audit Log Command
 * Configure audit logging for economy transactions
 */

import { <PERSON><PERSON><PERSON>ommandBuilder, ChannelType } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { createServerSuccessEmbed, createServerErrorEmbed, addUserInfo, EMOJIS } from '../../utils/embedBuilder';
import { ValidationError } from '../../utils/errorHandler';
import AuditChannelService from '../../services/auditChannelService';

/**
 * Audit Log command implementation
 */
export class AuditLogCommand extends BaseCommand {
  constructor() {
    super({
      name: 'auditlog',
      description: 'Configure audit logging for economy transactions (admin only)',
      category: CommandCategory.ADMIN,
      adminOnly: true,
      requiredFeatures: ['ECONOMY_SYSTEM'],
      requiredPermissions: ['Administrator'],
    });
  }

  /**
   * Customize the command builder
   */
  protected customizeCommand(command: SlashCommandBuilder): void {
    command
      .addSubcommand(subcommand =>
        subcommand
          .setName('setup')
          .setDescription('Set the audit logging channel for economy transactions')
          .addChannelOption(option =>
            option.setName('channel')
              .setDescription('The channel where audit logs will be sent')
              .setRequired(true)
              .addChannelTypes(ChannelType.GuildText)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('disable')
          .setDescription('Disable audit logging for this server'))
      .addSubcommand(subcommand =>
        subcommand
          .setName('status')
          .setDescription('View current audit logging configuration'));
  }

  /**
   * Execute the audit log command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const subcommand = interaction.options.getSubcommand();

    switch (subcommand) {
      case 'setup':
        await this.handleSetupAuditChannel(context);
        break;
      case 'disable':
        await this.handleDisableAuditLogging(context);
        break;
      case 'status':
        await this.handleViewAuditStatus(context);
        break;
      default:
        throw new ValidationError('Invalid subcommand');
    }
  }

  /**
   * Handle setting up the audit channel
   */
  private async handleSetupAuditChannel(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const channelOption = interaction.options.getChannel('channel', true);
    const guildId = interaction.guild!.id;

    try {
      // Validate the channel
      const validation = await AuditChannelService.validateAuditChannel(
        interaction.guild!,
        channelOption.id
      );

      if (!validation.valid) {
        const embed = await createServerErrorEmbed(guildId, 'Invalid Audit Channel');
        embed.setDescription(
          `${EMOJIS.ADMIN.WARNING} **Channel Validation Failed**\n\n` +
          `${validation.error}\n\n` +
          `${EMOJIS.ADMIN.INFO} **Required Permissions:**\n` +
          `• View Channel\n` +
          `• Send Messages\n` +
          `• Embed Links`
        );

        addUserInfo(embed, interaction.user);

        await interaction.reply({
          embeds: [embed],
          ephemeral: true
        });
        return;
      }

      // Set the audit channel
      await AuditChannelService.setAuditChannel(guildId, channelOption.id);

      // Create success embed
      const embed = await createServerSuccessEmbed(guildId, 'Audit Logging Configured Successfully!');
      embed.setDescription(
        `${EMOJIS.SUCCESS.CHECK} **Audit Channel Set**\n\n` +
        `Economy transaction audit logs will now be sent to ${channelOption}.\n\n` +
        `${EMOJIS.ECONOMY.COINS} **Logged Transactions:**\n` +
        `• \`/give\` - Administrative coin awards\n` +
        `• \`/fine\` - Administrative fines\n` +
        `• \`/pay\` - User-to-user payments\n\n` +
        `${EMOJIS.ADMIN.INFO} All audit logs include transaction details, timestamps, and user information.`
      )
      .addFields(
        {
          name: `${EMOJIS.ADMIN.HAMMER} Administrator`,
          value: `**${interaction.user.displayName}**`,
          inline: true
        },
        {
          name: `${EMOJIS.MISC.TAG} Audit Channel`,
          value: `${channelOption}`,
          inline: true
        },
        {
          name: `${EMOJIS.SUCCESS.PARTY} Status`,
          value: `**Active**`,
          inline: true
        }
      );

      addUserInfo(embed, interaction.user);

      await interaction.reply({
        embeds: [embed],
        ephemeral: false
      });

      this.logger.info(`Admin ${interaction.user.username} set audit channel to ${channelOption.name}`, {
        adminId: interaction.user.id,
        channelId: channelOption.id,
        channelName: channelOption.name,
        guildId: interaction.guild?.id,
      });

    } catch (error) {
      this.logger.error('Error setting audit channel', {
        error,
        adminId: interaction.user.id,
        channelId: channelOption.id,
        channelName: channelOption.name
      });
      throw error;
    }
  }

  /**
   * Handle disabling audit logging
   */
  private async handleDisableAuditLogging(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const guildId = interaction.guild!.id;

    try {
      // Get current audit channel for display
      const currentChannelId = await AuditChannelService.getAuditChannelId(guildId);
      let currentChannelName = 'None';
      
      if (currentChannelId) {
        const currentChannel = await interaction.guild!.channels.fetch(currentChannelId).catch(() => null);
        currentChannelName = currentChannel?.name || 'Unknown Channel';
      }

      // Disable audit logging
      await AuditChannelService.disableAuditLogging(guildId);

      // Create success embed
      const embed = await createServerSuccessEmbed(guildId, 'Audit Logging Disabled Successfully!');
      embed.setDescription(
        `${EMOJIS.ADMIN.WARNING} **Audit Logging Disabled**\n\n` +
        `Economy transaction audit logs will no longer be sent to any channel.\n\n` +
        `${EMOJIS.ADMIN.INFO} **Note:** You can re-enable audit logging at any time using \`/auditlog setup\`.`
      )
      .addFields(
        {
          name: `${EMOJIS.ADMIN.HAMMER} Administrator`,
          value: `**${interaction.user.displayName}**`,
          inline: true
        },
        {
          name: `${EMOJIS.MISC.TAG} Previous Channel`,
          value: `**${currentChannelName}**`,
          inline: true
        },
        {
          name: `${EMOJIS.ADMIN.SCALES} Status`,
          value: `**Disabled**`,
          inline: true
        }
      );

      addUserInfo(embed, interaction.user);

      await interaction.reply({
        embeds: [embed],
        ephemeral: false
      });

      this.logger.info(`Admin ${interaction.user.username} disabled audit logging`, {
        adminId: interaction.user.id,
        previousChannelId: currentChannelId,
        previousChannelName: currentChannelName,
        guildId: interaction.guild?.id,
      });

    } catch (error) {
      this.logger.error('Error disabling audit logging', { 
        error, 
        adminId: interaction.user.id
      });
      throw error;
    }
  }

  /**
   * Handle viewing audit status
   */
  private async handleViewAuditStatus(context: CommandContext): Promise<void> {
    const { interaction } = context;
    const guildId = interaction.guild!.id;

    try {
      // Get current audit channel
      const currentChannelId = await AuditChannelService.getAuditChannelId(guildId);
      
      let embed;
      
      if (currentChannelId) {
        const currentChannel = await interaction.guild!.channels.fetch(currentChannelId).catch(() => null);

        if (currentChannel) {
          // Validate current channel
          const validation = await AuditChannelService.validateAuditChannel(
            interaction.guild!,
            currentChannelId
          );

          embed = await createServerSuccessEmbed(guildId, 'Audit Logging Status');
          
          if (validation.valid) {
            embed.setDescription(
              `${EMOJIS.SUCCESS.CHECK} **Audit Logging Active**\n\n` +
              `Economy transaction logs are being sent to ${currentChannel}.\n\n` +
              `${EMOJIS.ECONOMY.COINS} **Monitored Transactions:**\n` +
              `• Administrative coin awards (\`/give\`)\n` +
              `• Administrative fines (\`/fine\`)\n` +
              `• User payments (\`/pay\`)`
            );
          } else {
            embed.setDescription(
              `${EMOJIS.ADMIN.WARNING} **Audit Logging Configuration Issue**\n\n` +
              `Audit channel is set to ${currentChannel}, but there's a problem:\n` +
              `**${validation.error}**\n\n` +
              `${EMOJIS.ADMIN.INFO} Please fix the permissions or use \`/auditlog setup\` to choose a different channel.`
            );
          }

          embed.addFields(
            {
              name: `${EMOJIS.MISC.TAG} Audit Channel`,
              value: `${currentChannel}`,
              inline: true
            },
            {
              name: `${EMOJIS.SUCCESS.PARTY} Status`,
              value: validation.valid ? `**Active**` : `**Issue Detected**`,
              inline: true
            },
            {
              name: `${EMOJIS.MISC.ID} Channel ID`,
              value: `\`${currentChannel.id}\``,
              inline: true
            }
          );
        } else {
          // Channel was deleted but still in config
          embed = await createServerErrorEmbed(guildId, 'Audit Channel Configuration Issue');
          embed.setDescription(
            `${EMOJIS.ADMIN.WARNING} **Channel Not Found**\n\n` +
            `An audit channel is configured but the channel no longer exists.\n` +
            `Please use \`/auditlog disable\` to clear the configuration or \`/auditlog setup\` to set a new channel.`
          );
        }
      } else {
        embed = await createServerSuccessEmbed(guildId, 'Audit Logging Status');
        embed.setDescription(
          `${EMOJIS.ADMIN.SCALES} **Audit Logging Disabled**\n\n` +
          `No audit channel is currently configured for this server.\n\n` +
          `${EMOJIS.ADMIN.INFO} Use \`/auditlog setup #channel\` to enable audit logging.`
        );
      }

      addUserInfo(embed, interaction.user);

      await interaction.reply({
        embeds: [embed],
        ephemeral: true
      });

    } catch (error) {
      this.logger.error('Error viewing audit status', { 
        error, 
        adminId: interaction.user.id
      });
      throw error;
    }
  }
}
