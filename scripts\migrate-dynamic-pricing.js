/**
 * Dynamic Pricing Migration Script
 * Updates existing RoleForSale records to include the new pricing fields
 * for backward compatibility with the dynamic pricing system.
 * 
 * Run this script after deploying the dynamic pricing changes.
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { RoleForSale } = require('../dist/models/User');

async function migrateDynamicPricing() {
    try {
        console.log('🔄 Connecting to MongoDB...');
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Connected to MongoDB');

        console.log('🔍 Checking existing roles for dynamic pricing fields...');
        
        // Find all roles that don't have the new pricing fields
        const rolesNeedingMigration = await RoleForSale.find({
            $or: [
                { priceType: { $exists: false } },
                { priceType: null },
                { priceType: undefined }
            ]
        });

        console.log(`📊 Found ${rolesNeedingMigration.length} roles needing migration`);

        if (rolesNeedingMigration.length === 0) {
            console.log('✅ All roles already have dynamic pricing fields');
            return;
        }

        // Separate shop_purchase roles from balance_threshold roles
        const shopRoles = rolesNeedingMigration.filter(role => role.roleType === 'shop_purchase');
        const balanceRoles = rolesNeedingMigration.filter(role => role.roleType === 'balance_threshold');

        console.log(`📋 Migration breakdown:`);
        console.log(`   • Shop purchase roles: ${shopRoles.length}`);
        console.log(`   • Balance threshold roles: ${balanceRoles.length}`);

        let migratedCount = 0;

        // Update all existing roles to use fixed pricing (backward compatibility)
        const updateResult = await RoleForSale.updateMany(
            {
                $or: [
                    { priceType: { $exists: false } },
                    { priceType: null },
                    { priceType: undefined }
                ]
            },
            {
                $set: { 
                    priceType: 'fixed',
                    // Don't set percentageValue for fixed pricing
                }
            }
        );

        migratedCount = updateResult.modifiedCount;
        console.log(`✅ Updated ${migratedCount} roles to use fixed pricing`);

        // Verify the migration
        const verifyFixed = await RoleForSale.countDocuments({ priceType: 'fixed' });
        const verifyPercentage = await RoleForSale.countDocuments({ priceType: 'percentage' });
        const totalRoles = await RoleForSale.countDocuments();
        
        console.log(`📊 Migration Summary:`);
        console.log(`   • Total roles: ${totalRoles}`);
        console.log(`   • Fixed pricing roles: ${verifyFixed}`);
        console.log(`   • Percentage pricing roles: ${verifyPercentage}`);
        console.log(`   • Roles migrated: ${migratedCount}`);

        // Validate that all roles now have pricing types
        const rolesWithoutPricing = await RoleForSale.countDocuments({
            $or: [
                { priceType: { $exists: false } },
                { priceType: null },
                { priceType: undefined }
            ]
        });

        if (rolesWithoutPricing > 0) {
            console.warn(`⚠️  Warning: ${rolesWithoutPricing} roles still missing pricing type`);
        } else {
            console.log('✅ All roles now have valid pricing types');
        }

        // Show some example roles after migration
        const sampleRoles = await RoleForSale.find({}).limit(3);
        console.log(`📋 Sample migrated roles:`);
        sampleRoles.forEach((role, index) => {
            console.log(`   ${index + 1}. ${role.name} (${role.roleType})`);
            console.log(`      • Price: ${role.price}`);
            console.log(`      • Price Type: ${role.priceType}`);
            console.log(`      • Percentage Value: ${role.percentageValue || 'N/A'}`);
        });

        console.log('✅ Dynamic pricing migration completed successfully!');
        
    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
        process.exit(0);
    }
}

// Helper function to validate migration
async function validateMigration() {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        
        const issues = [];
        
        // Check for roles without pricing type
        const noPricingType = await RoleForSale.countDocuments({
            $or: [
                { priceType: { $exists: false } },
                { priceType: null },
                { priceType: undefined }
            ]
        });
        
        if (noPricingType > 0) {
            issues.push(`${noPricingType} roles missing priceType field`);
        }
        
        // Check for percentage roles without percentageValue
        const percentageWithoutValue = await RoleForSale.countDocuments({
            priceType: 'percentage',
            $or: [
                { percentageValue: { $exists: false } },
                { percentageValue: null },
                { percentageValue: undefined }
            ]
        });
        
        if (percentageWithoutValue > 0) {
            issues.push(`${percentageWithoutValue} percentage roles missing percentageValue`);
        }
        
        // Check for invalid percentage values
        const invalidPercentage = await RoleForSale.countDocuments({
            priceType: 'percentage',
            $or: [
                { percentageValue: { $lt: 0.1 } },
                { percentageValue: { $gt: 50 } }
            ]
        });
        
        if (invalidPercentage > 0) {
            issues.push(`${invalidPercentage} roles with invalid percentage values (must be 0.1-50)`);
        }
        
        if (issues.length === 0) {
            console.log('✅ Migration validation passed - no issues found');
        } else {
            console.log('⚠️  Migration validation found issues:');
            issues.forEach(issue => console.log(`   • ${issue}`));
        }
        
        return issues.length === 0;
        
    } catch (error) {
        console.error('❌ Validation failed:', error);
        return false;
    } finally {
        await mongoose.disconnect();
    }
}

// Run the migration
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--validate')) {
        validateMigration();
    } else {
        migrateDynamicPricing();
    }
}

module.exports = { migrateDynamicPricing, validateMigration };
