"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveRole = resolveRole;
exports.validateRolePermissions = validateRolePermissions;
const errorHandler_1 = require("./errorHandler");
async function resolveRole(guild, input) {
    if (!input || input.trim().length === 0) {
        throw new errorHandler_1.ValidationError('Role identifier cannot be empty');
    }
    const trimmedInput = input.trim();
    if (/^\d{17,20}$/.test(trimmedInput)) {
        try {
            const role = await guild.roles.fetch(trimmedInput);
            if (role) {
                return {
                    role,
                    resolvedBy: 'id',
                    confidence: 1.0
                };
            }
        }
        catch (error) {
        }
    }
    const roles = await guild.roles.fetch();
    const roleArray = Array.from(roles.values()).filter(role => role.name !== '@everyone');
    const exactMatch = roleArray.find(role => role.name === trimmedInput);
    if (exactMatch) {
        return {
            role: exactMatch,
            resolvedBy: 'exact_name',
            confidence: 1.0
        };
    }
    const caseInsensitiveMatch = roleArray.find(role => role.name.toLowerCase() === trimmedInput.toLowerCase());
    if (caseInsensitiveMatch) {
        return {
            role: caseInsensitiveMatch,
            resolvedBy: 'exact_name',
            confidence: 0.9
        };
    }
    const fuzzyMatches = roleArray
        .map(role => ({
        role,
        score: calculateFuzzyScore(role.name, trimmedInput)
    }))
        .filter(match => match.score > 0.5)
        .sort((a, b) => b.score - a.score);
    if (fuzzyMatches.length === 0) {
        const suggestions = roleArray
            .slice(0, 5)
            .map(role => `"${role.name}"`)
            .join(', ');
        throw new errorHandler_1.ValidationError(`Role "${trimmedInput}" not found. Available roles include: ${suggestions}${roleArray.length > 5 ? '...' : ''}`);
    }
    if (fuzzyMatches.length === 1) {
        return {
            role: fuzzyMatches[0].role,
            resolvedBy: 'fuzzy_name',
            confidence: fuzzyMatches[0].score
        };
    }
    const topMatch = fuzzyMatches[0];
    const secondMatch = fuzzyMatches[1];
    if (topMatch.score - secondMatch.score > 0.3) {
        return {
            role: topMatch.role,
            resolvedBy: 'fuzzy_name',
            confidence: topMatch.score
        };
    }
    const ambiguousRoles = fuzzyMatches
        .slice(0, 5)
        .map(match => `"${match.role.name}"`)
        .join(', ');
    throw new errorHandler_1.ValidationError(`Multiple roles match "${trimmedInput}": ${ambiguousRoles}. Please be more specific.`);
}
function calculateFuzzyScore(roleName, input) {
    const roleNameLower = roleName.toLowerCase();
    const inputLower = input.toLowerCase();
    if (roleNameLower === inputLower) {
        return 1.0;
    }
    if (roleNameLower.includes(inputLower)) {
        return 0.8 * (inputLower.length / roleNameLower.length);
    }
    if (roleNameLower.startsWith(inputLower)) {
        return 0.7 * (inputLower.length / roleNameLower.length);
    }
    const roleWords = roleNameLower.split(/\s+/);
    const inputWords = inputLower.split(/\s+/);
    let matchingWords = 0;
    for (const inputWord of inputWords) {
        if (roleWords.some(roleWord => roleWord.includes(inputWord) || inputWord.includes(roleWord))) {
            matchingWords++;
        }
    }
    if (matchingWords > 0) {
        return 0.6 * (matchingWords / Math.max(roleWords.length, inputWords.length));
    }
    return 0;
}
async function validateRolePermissions(guild, role) {
    let botMember = guild.members.me;
    if (!botMember) {
        try {
            botMember = await guild.members.fetchMe();
        }
        catch (error) {
            throw new errorHandler_1.ValidationError('Unable to fetch bot member information from guild');
        }
    }
    if (!botMember.permissions.has('ManageRoles')) {
        throw new errorHandler_1.ValidationError('Bot does not have permission to manage roles');
    }
    const botHighestRole = botMember.roles.highest;
    if (role.position >= botHighestRole.position) {
        throw new errorHandler_1.ValidationError(`Cannot manage role "${role.name}" - it is higher than or equal to the bot's highest role`);
    }
    if (role.managed) {
        throw new errorHandler_1.ValidationError(`Cannot manage role "${role.name}" - it is managed by an integration or bot`);
    }
    if (role.name === '@everyone') {
        throw new errorHandler_1.ValidationError('Cannot manage the @everyone role');
    }
}
