/**
 * Audit Channel Service
 * Handles audit logging for economy transactions to designated channels
 */

import { Client, TextChannel, EmbedBuilder, PermissionFlagsBits, Guild, User } from 'discord.js';
import ServerConfigurationService from './serverConfigurationService';
import { createServerBaseEmbed, formatServerCoins, EMOJIS } from '../utils/embedBuilder';
import ConfigurableConstants from '../config/configurableConstants';

/**
 * Transaction types for audit logging
 */
export type AuditTransactionType = 'give' | 'fine' | 'pay';

/**
 * Audit log entry interface
 */
export interface AuditLogEntry {
  type: AuditTransactionType;
  executor: User;
  target: User;
  amount: number;
  reason?: string;
  guildId: string;
  timestamp: Date;
}

/**
 * Service for managing audit channel configuration and logging
 */
export class AuditChannelService {
  
  /**
   * Set the audit channel for a server
   */
  static async setAuditChannel(guildId: string, channelId: string): Promise<void> {
    await ServerConfigurationService.setAuditChannelId(guildId, channelId);
  }

  /**
   * Disable audit logging for a server
   */
  static async disableAuditLogging(guildId: string): Promise<void> {
    await ServerConfigurationService.setAuditChannelId(guildId, null);
  }

  /**
   * Get the current audit channel ID for a server
   */
  static async getAuditChannelId(guildId: string): Promise<string | null> {
    return await ServerConfigurationService.getAuditChannelId(guildId);
  }

  /**
   * Validate that a channel exists and bot has required permissions
   */
  static async validateAuditChannel(guild: Guild, channelId: string): Promise<{ valid: boolean; error?: string }> {
    try {
      const channel = await guild.channels.fetch(channelId);
      
      if (!channel) {
        return { valid: false, error: 'Channel not found in this server.' };
      }

      if (!channel.isTextBased()) {
        return { valid: false, error: 'Channel must be a text channel.' };
      }

      const textChannel = channel as TextChannel;
      const botMember = guild.members.me;
      
      if (!botMember) {
        return { valid: false, error: 'Bot member not found in server.' };
      }

      const permissions = textChannel.permissionsFor(botMember);
      
      if (!permissions) {
        return { valid: false, error: 'Unable to check channel permissions.' };
      }

      const requiredPermissions = [
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.EmbedLinks,
        PermissionFlagsBits.ViewChannel
      ];

      const missingPermissions = requiredPermissions.filter(perm => !permissions.has(perm));
      
      if (missingPermissions.length > 0) {
        const permissionNames = missingPermissions.map(perm => {
          switch (perm) {
            case PermissionFlagsBits.SendMessages: return 'Send Messages';
            case PermissionFlagsBits.EmbedLinks: return 'Embed Links';
            case PermissionFlagsBits.ViewChannel: return 'View Channel';
            default: return 'Unknown Permission';
          }
        });
        
        return { 
          valid: false, 
          error: `Bot is missing required permissions in this channel: ${permissionNames.join(', ')}` 
        };
      }

      return { valid: true };
    } catch (error) {
      console.error('[AuditChannelService] Error validating channel:', error);
      return { valid: false, error: 'Failed to validate channel. Please try again.' };
    }
  }

  /**
   * Send an audit log message for a transaction
   */
  static async logTransaction(client: Client, entry: AuditLogEntry): Promise<void> {
    try {
      const auditChannelId = await this.getAuditChannelId(entry.guildId);
      
      if (!auditChannelId) {
        // Audit logging is not configured for this server
        return;
      }

      const guild = client.guilds.cache.get(entry.guildId);
      if (!guild) {
        console.warn(`[AuditChannelService] Guild not found: ${entry.guildId}`);
        return;
      }

      const channel = await guild.channels.fetch(auditChannelId).catch(() => null);
      if (!channel || !channel.isTextBased()) {
        console.warn(`[AuditChannelService] Audit channel not found or not text-based: ${auditChannelId}`);
        // Optionally disable audit logging if channel is deleted
        await this.disableAuditLogging(entry.guildId);
        return;
      }

      const textChannel = channel as TextChannel;
      
      // Validate permissions before sending
      const validation = await this.validateAuditChannel(guild, auditChannelId);
      if (!validation.valid) {
        console.warn(`[AuditChannelService] Invalid audit channel permissions: ${validation.error}`);
        return;
      }

      // Create audit log embed
      const embed = await this.createAuditEmbed(entry);
      
      await textChannel.send({ embeds: [embed] });
      
      console.log(`[AuditChannelService] Audit log sent for ${entry.type} transaction: ${entry.executor.username} -> ${entry.target.username} (${entry.amount})`);
      
    } catch (error) {
      console.error('[AuditChannelService] Failed to send audit log:', error);
      // Don't throw error to avoid breaking the original transaction
    }
  }

  /**
   * Create an audit log embed for a transaction
   */
  private static async createAuditEmbed(entry: AuditLogEntry): Promise<EmbedBuilder> {
    const { type, executor, target, amount, reason, guildId, timestamp } = entry;
    
    // Get server-specific values
    const formattedAmount = await formatServerCoins(guildId, Math.abs(amount));
    const constants = await ConfigurableConstants.getAllServerConstants(guildId);
    
    // Determine embed details based on transaction type
    let title: string;
    let emoji: string;
    let description: string;
    
    switch (type) {
      case 'give':
        title = 'Administrative Coin Award';
        emoji = EMOJIS.ADMIN.HAMMER;
        description = `${emoji} **Administrator awarded coins to user**\n\n` +
                     `${formattedAmount} has been awarded to **${target.displayName}**`;
        break;
      case 'fine':
        title = 'Administrative Fine Issued';
        emoji = EMOJIS.ADMIN.SCALES;
        description = `${emoji} **Administrator fined user**\n\n` +
                     `${formattedAmount} has been deducted from **${target.displayName}**`;
        break;
      case 'pay':
        title = 'User Payment Transaction';
        emoji = EMOJIS.ECONOMY.COINS;
        description = `${emoji} **User-to-user payment completed**\n\n` +
                     `**${executor.displayName}** paid ${formattedAmount} to **${target.displayName}**`;
        break;
      default:
        title = 'Economy Transaction';
        emoji = EMOJIS.ECONOMY.COINS;
        description = `${emoji} **Transaction completed**`;
    }

    // Create embed with server-specific styling
    const embed = await createServerBaseEmbed(guildId, title, description);
    
    // Add transaction details
    embed.addFields(
      {
        name: `${EMOJIS.ACTIONS.SENDER} ${type === 'pay' ? 'Sender' : 'Administrator'}`,
        value: `${executor} (${executor.id})`,
        inline: true
      },
      {
        name: `${EMOJIS.ACTIONS.TARGET} ${type === 'pay' ? 'Recipient' : 'Target User'}`,
        value: `${target} (${target.id})`,
        inline: true
      },
      {
        name: `${EMOJIS.ECONOMY.COINS} Amount`,
        value: formattedAmount,
        inline: true
      }
    );

    // Add reason if provided
    if (reason) {
      embed.addFields({
        name: `${EMOJIS.MISC.SCROLL} Reason`,
        value: reason,
        inline: false
      });
    }

    // Add timestamp
    embed.addFields({
      name: `${EMOJIS.MISC.CLOCK} Transaction Time`,
      value: `<t:${Math.floor(timestamp.getTime() / 1000)}:F>`,
      inline: false
    });

    // Set footer with server branding
    embed.setFooter({ text: `${constants.nationName} Economy Audit Log` });
    
    return embed;
  }
}

export default AuditChannelService;
