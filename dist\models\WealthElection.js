"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const wealthElectionSchema = new mongoose_1.Schema({
    electionId: {
        type: String,
        required: [true, 'Election ID is required'],
        unique: true,
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    channelId: {
        type: String,
        required: [true, 'Channel ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Channel ID must be a valid Discord snowflake'
        }
    },
    messageId: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Message ID must be a valid Discord snowflake'
        },
        index: true
    },
    title: {
        type: String,
        required: [true, 'Election title is required'],
        maxlength: [100, 'Title cannot exceed 100 characters'],
        trim: true
    },
    description: {
        type: String,
        required: [true, 'Election description is required'],
        maxlength: [2000, 'Election description cannot exceed 2000 characters'],
        minlength: [1, 'Election description cannot be empty']
    },
    seats: {
        type: Number,
        required: [true, 'Number of seats is required'],
        min: [1, 'Must have at least 1 seat'],
        max: [25, 'Cannot exceed 25 seats'],
        default: 5
    },
    candidateCap: {
        type: Number,
        min: [1, 'Candidate cap must be at least 1'],
        max: [100, 'Candidate cap cannot exceed 100'],
        required: false
    },
    eligibleVoterRoles: [{
            type: String,
            required: true,
            validate: {
                validator: function (v) {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }],
    eligibleCandidateRoles: [{
            type: String,
            required: true,
            validate: {
                validator: function (v) {
                    return /^\d{17,20}$/.test(v);
                },
                message: 'Role ID must be a valid Discord snowflake'
            }
        }],
    nominationStart: {
        type: Date,
        required: [true, 'Nomination start time is required'],
        index: true
    },
    nominationEnd: {
        type: Date,
        required: [true, 'Nomination end time is required'],
        index: true,
        validate: {
            validator: function (v) {
                return v > this.nominationStart;
            },
            message: 'Nomination end must be after nomination start'
        }
    },
    votingStart: {
        type: Date,
        required: [true, 'Voting start time is required'],
        index: true,
        validate: {
            validator: function (v) {
                return v >= this.nominationEnd;
            },
            message: 'Voting start must be at or after nomination end'
        }
    },
    votingEnd: {
        type: Date,
        required: [true, 'Voting end time is required'],
        index: true,
        validate: {
            validator: function (v) {
                return v > this.votingStart;
            },
            message: 'Voting end must be after voting start'
        }
    },
    snapshotTime: {
        type: Date,
        index: true
    },
    status: {
        type: String,
        enum: ['SETUP', 'NOMINATING', 'SNAPSHOT_PENDING', 'VOTING', 'ENDED', 'CANCELLED'],
        required: [true, 'Election status is required'],
        default: 'SETUP',
        index: true
    },
    locked: {
        type: Boolean,
        default: false,
        index: true
    },
    snapshotHash: {
        type: String,
        default: '',
        validate: {
            validator: function (v) {
                return !v || /^[a-f0-9]{64}$/.test(v);
            },
            message: 'Snapshot hash must be a valid SHA-256 hash'
        }
    },
    createdBy: {
        type: String,
        required: [true, 'Creator ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Creator ID must be a valid Discord snowflake'
        },
        index: true
    },
    endedBy: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || /^\d{17,20}$/.test(v);
            },
            message: 'Ended by ID must be a valid Discord snowflake'
        }
    },
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    endedAt: {
        type: Date,
        index: true
    }
}, {
    timestamps: false
});
wealthElectionSchema.index({ guildId: 1, status: 1 });
wealthElectionSchema.index({ guildId: 1, createdAt: -1 });
wealthElectionSchema.index({ status: 1, nominationEnd: 1 });
wealthElectionSchema.index({ status: 1, votingEnd: 1 });
wealthElectionSchema.pre('validate', function () {
    if (this.eligibleVoterRoles && this.eligibleVoterRoles.length === 0) {
        this.invalidate('eligibleVoterRoles', 'At least one eligible voter role is required');
    }
    if (this.eligibleCandidateRoles && this.eligibleCandidateRoles.length === 0) {
        this.invalidate('eligibleCandidateRoles', 'At least one eligible candidate role is required');
    }
    if (this.votingStart < this.nominationEnd) {
        this.invalidate('votingStart', 'Voting must start at or after nominations end');
    }
});
wealthElectionSchema.methods.isNominationOpen = function () {
    const now = new Date();
    return this.status === 'NOMINATING' &&
        now >= this.nominationStart &&
        now < this.nominationEnd;
};
wealthElectionSchema.methods.isVotingOpen = function () {
    const now = new Date();
    return this.status === 'VOTING' &&
        now >= this.votingStart &&
        now < this.votingEnd;
};
wealthElectionSchema.methods.canTakeSnapshot = function () {
    return this.status === 'NOMINATING' || this.status === 'SNAPSHOT_PENDING';
};
wealthElectionSchema.methods.getTotalCandidates = async function () {
    const Candidate = (0, mongoose_1.model)('ElectionCandidate');
    return await Candidate.countDocuments({
        electionId: this.electionId,
        withdrawn: false,
        disqualified: false
    });
};
wealthElectionSchema.methods.getTotalVotes = async function () {
    const Ballot = (0, mongoose_1.model)('ElectionBallot');
    return await Ballot.countDocuments({
        electionId: this.electionId,
        replaced: false
    });
};
const WealthElection = (0, mongoose_1.model)('WealthElection', wealthElectionSchema);
exports.default = WealthElection;
