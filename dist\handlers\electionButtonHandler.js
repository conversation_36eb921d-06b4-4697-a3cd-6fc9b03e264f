"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElectionButtonHandler = void 0;
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
class ElectionButtonHandler {
    constructor(app) {
        this.app = app;
        this.logger = app.logger;
        try {
            this.electionService = app.getService('ElectionService');
        }
        catch (error) {
            this.logger.error('[ElectionButtonHandler] ElectionService not available', { error });
            throw new Error('ElectionService not available for ElectionButtonHandler');
        }
        this.logger.info('[ElectionButtonHandler] Election button handler initialized');
    }
    async handleElectionButton(interaction, customId) {
        try {
            const parts = customId.split('_');
            if (parts.length < 3) {
                throw new errorHandler_1.ValidationError('Invalid election button format');
            }
            const action = parts[1];
            let electionId;
            let candidateId;
            if (action === 'vote' && parts.length >= 4) {
                candidateId = parts[parts.length - 1];
                electionId = parts.slice(2, -1).join('_');
            }
            else {
                electionId = parts.slice(2).join('_');
                candidateId = undefined;
            }
            this.logger.info(`Handling election button: ${action}`, {
                customId,
                electionId,
                userId: interaction.user.id,
                candidateId,
                parsedParts: parts.length
            });
            switch (action) {
                case 'vote':
                    if (!candidateId) {
                        throw new errorHandler_1.ValidationError('Candidate ID missing for vote action');
                    }
                    await this.handleVote(interaction, electionId, candidateId);
                    break;
                case 'resign':
                    await this.handleResignCandidate(interaction, electionId);
                    break;
                case 'refresh':
                    await this.handleRefresh(interaction, electionId);
                    break;
                case 'end':
                    await this.handleEndElection(interaction, electionId);
                    break;
                default:
                    throw new errorHandler_1.ValidationError(`Unknown election action: ${action}`);
            }
        }
        catch (error) {
            this.logger.error('Error handling election button', {
                error,
                customId,
                userId: interaction.user.id
            });
            const errorEmbed = this.createElectionErrorEmbed('Election Error', error instanceof errorHandler_1.ValidationError ? error.message : 'An unexpected error occurred');
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            }
            else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
    async handleVote(interaction, electionId, candidateId) {
        if (!interaction.guild || !interaction.member) {
            throw new errorHandler_1.ValidationError('This action can only be performed in a server');
        }
        if (!electionId || !candidateId) {
            throw new errorHandler_1.ValidationError('Invalid election or candidate information');
        }
        const member = interaction.member;
        try {
            await interaction.guild.members.fetch(interaction.user.id);
        }
        catch (error) {
            throw new errorHandler_1.ValidationError('You are no longer a member of this server');
        }
        const election = await this.electionService.getElection(electionId);
        if (!election) {
            throw new errorHandler_1.ValidationError('Election not found');
        }
        if (election.status !== 'ACTIVE') {
            throw new errorHandler_1.ValidationError('Voting is not currently open for this election');
        }
        const canVote = await this.electionService.canUserVote(electionId, interaction.user.id, member);
        if (!canVote) {
            throw new errorHandler_1.ValidationError('You do not have the required role to vote in this election');
        }
        const candidates = await this.electionService.getElectionCandidates(electionId);
        const candidate = candidates.find(c => c.discordId === candidateId);
        if (!candidate || candidate.status !== 'ACTIVE') {
            throw new errorHandler_1.ValidationError('Candidate not found or no longer active');
        }
        if (candidate.discordId === interaction.user.id) {
            throw new errorHandler_1.ValidationError('You cannot vote for yourself');
        }
        const existingVote = await this.electionService.getUserVote(electionId, interaction.user.id);
        const isUpdate = !!existingVote;
        await interaction.deferReply({ ephemeral: true });
        const vote = await this.electionService.castVote(electionId, interaction.user.id, candidateId, member.displayName, member.user.username, candidate.displayName, candidate.username, interaction.guild.id);
        await this.updateElectionMessage(interaction, electionId);
        const confirmationEmbed = this.createVoteConfirmationEmbed(election, candidate.displayName, vote.voteWeight, isUpdate);
        await interaction.editReply({ embeds: [confirmationEmbed] });
    }
    async handleResignCandidate(interaction, electionId) {
        const election = await this.electionService.getElection(electionId);
        if (!election) {
            throw new errorHandler_1.ValidationError('Election not found');
        }
        const isCandidate = await this.electionService.isUserCandidate(electionId, interaction.user.id);
        if (!isCandidate) {
            throw new errorHandler_1.ValidationError('You are not an active candidate in this election');
        }
        await this.electionService.removeCandidate(electionId, interaction.user.id);
        const confirmationEmbed = new discord_js_1.EmbedBuilder()
            .setColor(0xf39c12)
            .setTitle('Candidacy Withdrawn')
            .setDescription(`You have withdrawn your candidacy from the election: **${election.title}**\n\n` +
            'All votes for you have been invalidated.')
            .setTimestamp();
        await interaction.reply({ embeds: [confirmationEmbed], ephemeral: true });
        await this.updateElectionMessage(interaction, electionId);
    }
    async handleRefresh(interaction, electionId) {
        await interaction.deferUpdate();
        await this.updateElectionMessage(interaction, electionId);
    }
    async handleEndElection(interaction, electionId) {
        try {
            this.logger.info('[ElectionButtonHandler] Processing end election request', {
                electionId,
                userId: interaction.user.id,
                guildId: interaction.guild?.id
            });
            if (!interaction.guild || !interaction.member) {
                throw new errorHandler_1.ValidationError('This action can only be performed in a server');
            }
            const member = interaction.member;
            if (!member.permissions) {
                this.logger.error('[ElectionButtonHandler] Member permissions not available for end election', {
                    userId: interaction.user.id,
                    electionId,
                    memberType: typeof member
                });
                throw new errorHandler_1.ValidationError('Unable to verify your permissions. Please try again.');
            }
            await interaction.deferReply({ ephemeral: true });
            const endedElection = await this.electionService.endElection(electionId, interaction.user.id);
            const results = await this.electionService.calculateFinalResults(electionId, interaction.guild.memberCount);
            const resultsEmbed = this.createElectionResultsEmbed(endedElection, results.candidates, results.stats);
            if (interaction.message) {
                await interaction.message.edit({
                    embeds: [resultsEmbed],
                    components: []
                });
            }
            await interaction.editReply({
                content: `✅ Election "${endedElection.title}" ended successfully. Final results are now displayed.`
            });
            this.logger.info('[ElectionButtonHandler] Election ended successfully', {
                electionId,
                endedBy: interaction.user.id,
                totalVotes: endedElection.totalVotes,
                totalVoteWeight: endedElection.totalVoteWeight
            });
        }
        catch (error) {
            this.logger.error('[ElectionButtonHandler] Error processing end election', {
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
                electionId,
                userId: interaction.user.id,
                errorRef: 'ERR-MEH3Z-GIKXQ'
            });
            let errorMessage;
            if (error instanceof errorHandler_1.ValidationError) {
                errorMessage = error.message;
            }
            else if (error instanceof Error) {
                this.logger.error('[ElectionButtonHandler] Unexpected error details', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack,
                    errorRef: 'ERR-MEH3Z-GIKXQ'
                });
                errorMessage = 'Failed to end election. Please try again.';
            }
            else {
                errorMessage = 'An unexpected error occurred. Please try again.';
            }
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `❌ **Error:** ${errorMessage} (Error Reference: ERR-MEH3Z-GIKXQ)`
                });
            }
            else {
                await interaction.reply({
                    content: `❌ **Error:** ${errorMessage} (Error Reference: ERR-MEH3Z-GIKXQ)`,
                    ephemeral: true
                });
            }
        }
    }
    async updateElectionMessage(interaction, electionId) {
        try {
            const election = await this.electionService.getElection(electionId);
            if (!election) {
                this.logger.warn('Election not found for message update', { electionId });
                return;
            }
            const candidates = await this.electionService.getElectionCandidates(electionId);
            const userVote = await this.electionService.getUserVote(electionId, interaction.user.id);
            const isUserCandidate = await this.electionService.isUserCandidate(electionId, interaction.user.id);
            const member = interaction.member;
            const canEndElection = member ? await this.electionService.canUserEndElection(electionId, interaction.user.id, member) : false;
            const { createElectionEmbed, createElectionButtons } = await Promise.resolve().then(() => __importStar(require('../utils/electionEmbedBuilder')));
            const stats = {
                totalVotes: election.totalVotes,
                totalVoteWeight: election.totalVoteWeight,
                totalCandidates: election.totalCandidates,
                activeCandidates: candidates.filter(c => c.status === 'ACTIVE').length
            };
            const embed = await createElectionEmbed(election, candidates, stats);
            const components = createElectionButtons(electionId, candidates, interaction.user.id, userVote?.candidateId, isUserCandidate, canEndElection);
            if (interaction.message) {
                await interaction.message.edit({
                    embeds: [embed],
                    components: components
                });
            }
        }
        catch (error) {
            this.logger.error('Failed to update election message', {
                error,
                electionId,
                userId: interaction.user.id
            });
        }
    }
    createElectionResultsEmbed(election, candidates, stats) {
        const embed = new discord_js_1.EmbedBuilder()
            .setColor(0x00ff00)
            .setTitle(`🏆 Election Results: ${election.title}`)
            .setDescription(election.description || 'Election completed')
            .setTimestamp();
        if (candidates.length === 0) {
            embed.addFields({
                name: 'Results',
                value: 'No candidates participated in this election.',
                inline: false
            });
        }
        else {
            const resultText = candidates
                .map((candidate, index) => {
                const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📍';
                return `${medal} **${candidate.displayName}** - ${candidate.voteWeight.toLocaleString()} coins (${candidate.voteCount} votes)`;
            })
                .join('\n');
            embed.addFields({
                name: 'Final Results',
                value: resultText,
                inline: false
            });
            embed.addFields({
                name: 'Election Statistics',
                value: `Total Votes: ${stats.totalVotes}\nTotal Vote Weight: ${stats.totalVoteWeight.toLocaleString()} coins`,
                inline: false
            });
        }
        return embed;
    }
    createWinnerAnnouncementEmbed(election, winner) {
        return new discord_js_1.EmbedBuilder()
            .setColor(0xffd700)
            .setTitle('🎉 Election Winner Announced!')
            .setDescription(`Congratulations to **${winner.displayName}** for winning the election: **${election.title}**!\n\n` +
            `**Final Score:** ${winner.voteWeight.toLocaleString()} coins from ${winner.voteCount} votes`)
            .setTimestamp();
    }
    createElectionErrorEmbed(title, description) {
        return new discord_js_1.EmbedBuilder()
            .setColor(0xff0000)
            .setTitle(title)
            .setDescription(description)
            .setTimestamp();
    }
    createVoteConfirmationEmbed(election, candidateName, voteWeight, isUpdate) {
        return new discord_js_1.EmbedBuilder()
            .setColor(0x00ff00)
            .setTitle(isUpdate ? '✅ Vote Updated' : '✅ Vote Cast')
            .setDescription(`You have ${isUpdate ? 'updated your vote' : 'voted'} for **${candidateName}** in the election: **${election.title}**\n\n` +
            `**Your vote weight:** ${voteWeight.toLocaleString()} coins`)
            .setTimestamp();
    }
}
exports.ElectionButtonHandler = ElectionButtonHandler;
