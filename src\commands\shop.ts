import { SlashCommandBuilder, ChatInputCommandInteraction, ActionRowBuilder, ButtonBuilder, ButtonStyle, EmbedBuilder } from 'discord.js';
import { RoleForSale } from '../models/User';
import { withErrorHandler, ValidationError, DatabaseError } from '../utils/errorHandler';
import { createServerEconomyEmbed, addUserInfo, formatServerCoins, createShopNavigationButtons, EMOJIS, COLORS } from '../utils/embedBuilder';
import { ensureUser } from '../services/economyService';
import User from '../models/User';
import ConfigurableConstants from '../config/configurableConstants';
import DynamicPricingService from '../services/dynamicPricingService';

module.exports = {
    data: new SlashCommandBuilder()
        .setName('shop')
        .setDescription('Browse and purchase available roles with your coins'),
    execute: withErrorHandler(async (interaction: ChatInputCommandInteraction) => {
        const guildId = interaction.guild?.id;
        const discordId = interaction.user.id;

        // Basic validation
        if (!guildId) {
            throw new ValidationError('This command can only be used in a server.');
        }

        if (!interaction.guild) {
            throw new ValidationError('Guild information not available.');
        }

        try {
            // Ensure user exists and get their balance
            const user = await ensureUser(discordId, guildId);
            const currentBalance = user.balance;

            // Get all purchasable roles for this guild (only shop_purchase type)
            const availableRoles = await RoleForSale.find({
                guildId,
                roleType: 'shop_purchase'
            }).sort({ price: 1 });

            if (availableRoles.length === 0) {
                const embed = await createServerEconomyEmbed(guildId, 'Role Shop');
                embed.setDescription(
                    `${EMOJIS.MISC.EMPTY} **No roles available for purchase**\n\n` +
                    `The shop is currently empty. Contact an administrator to add purchasable roles.`
                );

                // Add navigation buttons
                const navigationRow = createShopNavigationButtons();

                await interaction.reply({
                    embeds: [embed],
                    components: [navigationRow],
                    ephemeral: false
                });
                return;
            }

            // Get user's current roles to check ownership
            const member = await interaction.guild.members.fetch(discordId);
            const userRoleIds = member.roles.cache.map(role => role.id);

            // Calculate dynamic prices for all roles
            const priceCalculations = await DynamicPricingService.calculatePricesForRoles(availableRoles, guildId);

            // Create shop embed
            const embed = await createServerEconomyEmbed(guildId, 'Role Shop');
            const coinName = await ConfigurableConstants.getCoinName(guildId);
            const formattedBalance = await formatServerCoins(guildId, currentBalance);

            embed.setDescription(
                `${EMOJIS.ECONOMY.SHOP} **Welcome to the Role Shop!**\n\n` +
                `Purchase roles using your ${coinName}. Click the "Buy" button next to any role you want to purchase.\n\n` +
                `${EMOJIS.ECONOMY.COINS} **Your Balance:** ${formattedBalance}`
            );

            // Process roles and create fields
            const roleFields: string[] = [];
            const components: ActionRowBuilder<ButtonBuilder>[] = [];
            let buttonCount = 0;
            let currentRow: ActionRowBuilder<ButtonBuilder> | null = null;

            for (const roleData of availableRoles) {
                const priceInfo = priceCalculations.get(roleData.roleId);
                if (!priceInfo) continue;

                const formattedPrice = await formatServerCoins(guildId, priceInfo.displayPrice);
                const hasRole = userRoleIds.includes(roleData.roleId);
                const canAfford = currentBalance >= priceInfo.actualPrice;

                let statusEmoji: string;
                let statusText: string;
                let buttonDisabled = false;

                if (hasRole) {
                    statusEmoji = EMOJIS.SUCCESS.CHECK;
                    statusText = '**Already Owned**';
                    buttonDisabled = true;
                } else if (canAfford) {
                    statusEmoji = EMOJIS.ECONOMY.COINS;
                    statusText = '**Available**';
                } else {
                    statusEmoji = EMOJIS.MISC.LOCK;
                    statusText = '**Insufficient Funds**';
                    buttonDisabled = true;
                }

                // Add role field with dynamic pricing info
                let priceDisplay = formattedPrice;
                if (priceInfo.isPercentageBased) {
                    priceDisplay += ` (${priceInfo.percentageValue}% of economy)`;
                }

                let fieldValue = `${statusEmoji} ${statusText} - ${priceDisplay}`;
                if (roleData.description) {
                    fieldValue += `\n*${roleData.description}*`;
                }

                roleFields.push(`**${roleData.name}**\n${fieldValue}`);

                // Create buy button (max 5 buttons per row, max 5 rows)
                if (buttonCount < 25) { // Discord limit: 5 rows × 5 buttons = 25 buttons max
                    if (buttonCount % 5 === 0) {
                        if (currentRow) components.push(currentRow);
                        currentRow = new ActionRowBuilder<ButtonBuilder>();
                    }

                    const button = new ButtonBuilder()
                        .setCustomId(`shop_buy_${roleData.roleId}`)
                        .setLabel(hasRole ? 'Owned' : 'Buy')
                        .setEmoji(hasRole ? EMOJIS.SUCCESS.CHECK : EMOJIS.ECONOMY.COINS)
                        .setStyle(hasRole ? ButtonStyle.Success : (canAfford ? ButtonStyle.Primary : ButtonStyle.Secondary))
                        .setDisabled(buttonDisabled);

                    currentRow!.addComponents(button);
                    buttonCount++;
                }
            }

            // Add the last row if it has buttons
            if (currentRow && currentRow.components.length > 0) {
                components.push(currentRow);
            }

            // Add role fields to embed (Discord limit: 25 fields max)
            const maxFields = Math.min(roleFields.length, 25);
            for (let i = 0; i < maxFields; i++) {
                embed.addFields({
                    name: `${i + 1}. Role`,
                    value: roleFields[i],
                    inline: true
                });
            }

            if (roleFields.length > 25) {
                embed.addFields({
                    name: '⚠️ Display Limit Reached',
                    value: `Showing first 25 of ${roleFields.length} available roles.`,
                    inline: false
                });
            }

            // Add navigation buttons
            const navigationRow = createShopNavigationButtons();
            components.push(navigationRow);

            // Add user info to embed
            addUserInfo(embed, interaction.user);

            await interaction.reply({
                embeds: [embed],
                components: components,
                ephemeral: false
            });

        } catch (error: unknown) {
            if (error instanceof ValidationError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new DatabaseError(`Failed to load shop: ${error.message}`);
            } else {
                throw new DatabaseError('Failed to load shop.');
            }
        }
    })
};


