"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const User_1 = require("../models/User");
const errorHandler_1 = require("../utils/errorHandler");
const roleResolver_1 = require("../utils/roleResolver");
const embedBuilder_1 = require("../utils/embedBuilder");
const BankerPermissions_1 = require("../utils/permissions/BankerPermissions");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
const dynamicPricingService_1 = __importDefault(require("../services/dynamicPricingService"));
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('setroleforsale')
        .setDescription('Configure a role as purchasable for the specified coin amount (Admin/Banker only)')
        .addRoleOption(option => option
        .setName('role')
        .setDescription('The role to make purchasable')
        .setRequired(true))
        .addStringOption(option => option
        .setName('price')
        .setDescription('Price: fixed amount (e.g., "1000") or percentage (e.g., "5%")')
        .setRequired(true))
        .addStringOption(option => option
        .setName('description')
        .setDescription('Optional description for the role')
        .setMaxLength(200)
        .setRequired(false)),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        const role = interaction.options.getRole('role', true);
        const priceInput = interaction.options.getString('price', true);
        const description = interaction.options.getString('description');
        const guildId = interaction.guild?.id;
        if (!guildId) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!interaction.guild) {
            throw new errorHandler_1.ValidationError('Guild information not available.');
        }
        if (!interaction.member || !(interaction.member instanceof discord_js_1.GuildMember)) {
            throw new errorHandler_1.ValidationError('Member information not available.');
        }
        const hasPermissions = await (0, BankerPermissions_1.hasBankerPermissions)(interaction.member);
        if (!hasPermissions) {
            throw new errorHandler_1.PermissionError('This command requires administrator permissions or the designated banker role.');
        }
        try {
            let parsedPrice;
            try {
                parsedPrice = dynamicPricingService_1.default.parsePriceInput(priceInput);
            }
            catch (error) {
                throw new errorHandler_1.ValidationError(`Invalid price format: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
            const fullRole = interaction.guild.roles.cache.get(role.id) || await interaction.guild.roles.fetch(role.id);
            if (!fullRole) {
                throw new errorHandler_1.ValidationError('Role not found in guild');
            }
            await (0, roleResolver_1.validateRolePermissions)(interaction.guild, fullRole);
            const existingRole = await User_1.RoleForSale.findOne({
                roleId: fullRole.id,
                guildId: guildId,
                roleType: 'shop_purchase'
            });
            if (existingRole) {
                existingRole.price = parsedPrice.value;
                existingRole.name = fullRole.name;
                existingRole.roleType = 'shop_purchase';
                existingRole.priceType = parsedPrice.priceType;
                existingRole.percentageValue = parsedPrice.percentageValue;
                if (description !== null) {
                    existingRole.description = description;
                }
                await existingRole.save();
                const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Role Updated in Shop!');
                const coinName = await configurableConstants_1.default.getCoinName(guildId);
                const calculatedPrice = await dynamicPricingService_1.default.calculatePrice(existingRole, guildId);
                const formattedPrice = await (0, embedBuilder_1.formatServerCoins)(guildId, calculatedPrice.displayPrice);
                let priceDisplay = formattedPrice;
                if (calculatedPrice.isPercentageBased) {
                    priceDisplay += ` (${calculatedPrice.percentageValue}% of economy)`;
                }
                embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **${fullRole.name}** has been updated in the role shop!\n\n` +
                    `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **New Price:** ${priceDisplay}\n` +
                    `${embedBuilder_1.EMOJIS.MISC.SCROLL} **Description:** ${description || 'None'}`);
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} What's Next?`,
                    value: [
                        `• Members can now purchase this role using \`/shop\``,
                        `• Use \`/removerolefromsale\` to remove it from the shop`,
                        `• Use \`/shop\` to view the updated role shop`
                    ].join('\n'),
                    inline: false
                });
                (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: false
                });
            }
            else {
                const newRole = await User_1.RoleForSale.create({
                    guildId: guildId,
                    roleId: fullRole.id,
                    name: fullRole.name,
                    price: parsedPrice.value,
                    description: description,
                    roleType: 'shop_purchase',
                    priceType: parsedPrice.priceType,
                    percentageValue: parsedPrice.percentageValue
                });
                const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(guildId, 'Role Added to Shop!');
                const coinName = await configurableConstants_1.default.getCoinName(guildId);
                const calculatedPrice = await dynamicPricingService_1.default.calculatePrice(newRole, guildId);
                const formattedPrice = await (0, embedBuilder_1.formatServerCoins)(guildId, calculatedPrice.displayPrice);
                let priceDisplay = formattedPrice;
                if (calculatedPrice.isPercentageBased) {
                    priceDisplay += ` (${calculatedPrice.percentageValue}% of economy)`;
                }
                embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **${fullRole.name}** has been added to the role shop!\n\n` +
                    `${embedBuilder_1.EMOJIS.ECONOMY.COINS} **Price:** ${priceDisplay}\n` +
                    `${embedBuilder_1.EMOJIS.MISC.SCROLL} **Description:** ${description || 'None'}`);
                embed.addFields({
                    name: `${embedBuilder_1.EMOJIS.MISC.LIGHTBULB} What's Next?`,
                    value: [
                        `• Members can now purchase this role using \`/shop\``,
                        `• Use \`/setroleforsale\` again to update the price or description`,
                        `• Use \`/removerolefromsale\` to remove it from the shop`,
                        `• Use \`/shop\` to view the role shop`
                    ].join('\n'),
                    inline: false
                });
                (0, embedBuilder_1.addUserInfo)(embed, interaction.user);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: false
                });
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError || error instanceof errorHandler_1.PermissionError) {
                throw error;
            }
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(`Failed to configure role for sale: ${error.message}`);
            }
            else {
                throw new errorHandler_1.DatabaseError('Failed to configure role for sale.');
            }
        }
    })
};
