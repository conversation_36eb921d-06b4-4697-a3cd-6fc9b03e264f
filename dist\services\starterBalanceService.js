"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processStarterBalance = processStarterBalance;
exports.getStarterBalanceRules = getStarterBalanceRules;
exports.createStarterBalanceRule = createStarterBalanceRule;
exports.updateStarterBalanceRule = updateStarterBalanceRule;
exports.removeStarterBalanceRule = removeStarterBalanceRule;
exports.hasStarterBalanceRule = hasStarterBalanceRule;
const StarterBalance_1 = require("../models/StarterBalance");
const economyService_1 = require("./economyService");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
async function processStarterBalance(member, role) {
    try {
        const starterBalanceRule = await StarterBalance_1.StarterBalance.findOne({
            guildId: member.guild.id,
            roleId: role.id
        });
        if (!starterBalanceRule) {
            return false;
        }
        await (0, economyService_1.adjustBalance)(member.id, member.guild.id, starterBalanceRule.amount, 'starter_balance', `Starter balance for role: ${role.name}`, member.client);
        const coinSymbol = await configurableConstants_1.default.getCoinSymbol(member.guild.id);
        console.log(`[Starter Balance] Granted ${starterBalanceRule.amount} ${coinSymbol} to ${member.displayName} for receiving role ${role.name}`);
        try {
            const formattedAmount = await (0, embedBuilder_1.formatServerCoinsWithSymbol)(member.guild.id, starterBalanceRule.amount);
            const embed = await (0, embedBuilder_1.createServerSuccessEmbed)(member.guild.id, 'Starter Balance Awarded!')
                .then(embed => embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Welcome Bonus!**\n\n` +
                `You have been awarded **${formattedAmount}** ` +
                `for receiving the **${role.name}** role!\n\n` +
                `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Use \`/balance\` to check your current balance and ` +
                `\`/roles\` to see what you can purchase with your coins.`));
            await member.send({ embeds: [embed] });
        }
        catch (dmError) {
            console.error(`[Starter Balance] Failed to send DM to ${member.displayName}:`, dmError);
        }
        return true;
    }
    catch (error) {
        console.error(`[Starter Balance] Failed to process starter balance for ${member.displayName} and role ${role.name}:`, error);
        throw new errorHandler_1.DatabaseError(`Failed to process starter balance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function getStarterBalanceRules(guildId) {
    try {
        return await StarterBalance_1.StarterBalance.find({ guildId }).sort({ roleName: 1 });
    }
    catch (error) {
        console.error(`[Starter Balance] Failed to get starter balance rules for guild ${guildId}:`, error);
        throw new errorHandler_1.DatabaseError(`Failed to retrieve starter balance rules: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function createStarterBalanceRule(guildId, roleId, roleName, amount) {
    try {
        const existingRule = await StarterBalance_1.StarterBalance.findOne({ guildId, roleId });
        if (existingRule) {
            throw new errorHandler_1.DatabaseError(`Starter balance rule already exists for role ${roleName}`);
        }
        const newRule = new StarterBalance_1.StarterBalance({
            guildId,
            roleId,
            roleName,
            amount
        });
        await newRule.save();
        console.log(`[Starter Balance] Created new rule: ${amount} PLC for role ${roleName} in guild ${guildId}`);
        return newRule;
    }
    catch (error) {
        if (error instanceof errorHandler_1.DatabaseError) {
            throw error;
        }
        console.error(`[Starter Balance] Failed to create starter balance rule:`, error);
        throw new errorHandler_1.DatabaseError(`Failed to create starter balance rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function updateStarterBalanceRule(guildId, roleId, newAmount) {
    try {
        const updatedRule = await StarterBalance_1.StarterBalance.findOneAndUpdate({ guildId, roleId }, { amount: newAmount }, { new: true, runValidators: true });
        if (updatedRule) {
            console.log(`[Starter Balance] Updated rule for role ${updatedRule.roleName}: ${newAmount} PLC`);
        }
        return updatedRule;
    }
    catch (error) {
        console.error(`[Starter Balance] Failed to update starter balance rule:`, error);
        throw new errorHandler_1.DatabaseError(`Failed to update starter balance rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function removeStarterBalanceRule(guildId, roleId) {
    try {
        const deletedRule = await StarterBalance_1.StarterBalance.findOneAndDelete({ guildId, roleId });
        if (deletedRule) {
            console.log(`[Starter Balance] Removed rule for role ${deletedRule.roleName}`);
            return true;
        }
        return false;
    }
    catch (error) {
        console.error(`[Starter Balance] Failed to remove starter balance rule:`, error);
        throw new errorHandler_1.DatabaseError(`Failed to remove starter balance rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function hasStarterBalanceRule(guildId, roleId) {
    try {
        const rule = await StarterBalance_1.StarterBalance.findOne({ guildId, roleId });
        return !!rule;
    }
    catch (error) {
        console.error(`[Starter Balance] Failed to check starter balance rule existence:`, error);
        return false;
    }
}
