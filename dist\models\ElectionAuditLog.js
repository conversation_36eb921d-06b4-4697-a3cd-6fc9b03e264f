"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const electionAuditLogSchema = new mongoose_1.Schema({
    logId: {
        type: String,
        required: [true, 'Log ID is required'],
        unique: true,
        index: true
    },
    electionId: {
        type: String,
        required: [true, 'Election ID is required'],
        validate: {
            validator: function (v) {
                return v && v.trim().length > 0;
            },
            message: 'Election ID cannot be empty'
        },
        index: true
    },
    actionType: {
        type: String,
        enum: ['CREATE', 'NOMINATE', 'WITHDRAW', 'SNAPSHOT', 'VOTE', 'END', 'DISQUALIFY', 'PURGE', 'CANCEL'],
        required: [true, 'Action type is required'],
        index: true
    },
    actorId: {
        type: String,
        required: [true, 'Actor ID is required'],
        validate: {
            validator: function (v) {
                return v === 'SYSTEM' || /^\d{17,20}$/.test(v);
            },
            message: 'Actor ID must be "SYSTEM" or a valid Discord snowflake'
        },
        index: true
    },
    targetId: {
        type: String,
        validate: {
            validator: function (v) {
                return !v || v === 'SYSTEM' || /^\d{17,20}$/.test(v) || (v && v.trim().length > 0);
            },
            message: 'Target ID must be empty, "SYSTEM", a valid Discord snowflake, or a valid ID'
        },
        index: true
    },
    details: {
        type: mongoose_1.Schema.Types.Mixed,
        required: [true, 'Details are required'],
        validate: {
            validator: function (v) {
                return v !== null && v !== undefined;
            },
            message: 'Details cannot be null or undefined'
        }
    },
    timestamp: {
        type: Date,
        required: [true, 'Timestamp is required'],
        default: Date.now,
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    }
}, {
    timestamps: false
});
electionAuditLogSchema.index({ electionId: 1, timestamp: -1 });
electionAuditLogSchema.index({ electionId: 1, actionType: 1, timestamp: -1 });
electionAuditLogSchema.index({ guildId: 1, timestamp: -1 });
electionAuditLogSchema.index({ actorId: 1, timestamp: -1 });
electionAuditLogSchema.index({ actionType: 1, timestamp: -1 });
electionAuditLogSchema.methods.isSystemAction = function () {
    return this.actorId === 'SYSTEM';
};
electionAuditLogSchema.methods.getFormattedDetails = function () {
    if (!this.details)
        return 'No details';
    try {
        if (typeof this.details === 'string') {
            return this.details;
        }
        switch (this.actionType) {
            case 'CREATE':
                return `Election created: "${this.details.title}" with ${this.details.seats} seats`;
            case 'NOMINATE':
                return `User nominated: ${this.details.displayName} (${this.details.username})`;
            case 'WITHDRAW':
                return `Candidate withdrew: ${this.details.candidateName || 'Unknown'}`;
            case 'SNAPSHOT':
                return `Balance snapshot taken: ${this.details.totalUsers} users, ${this.details.totalWeight} total weight`;
            case 'VOTE':
                const weight = this.details.weight || 0;
                const previous = this.details.previousVote ? ' (replaced previous vote)' : '';
                return `Vote cast with weight ${weight.toLocaleString()} PLC${previous}`;
            case 'END':
                return `Election ended: ${this.details.totalVotes || 0} votes, ${this.details.totalWeight || 0} total weight`;
            case 'DISQUALIFY':
                return `Candidate disqualified: ${this.details.reason || 'No reason provided'}`;
            case 'PURGE':
                return `Election purged by admin`;
            case 'CANCEL':
                return `Election cancelled: ${this.details.reason || 'No reason provided'}`;
            default:
                return JSON.stringify(this.details);
        }
    }
    catch (error) {
        return 'Error formatting details';
    }
};
electionAuditLogSchema.statics.logAction = async function (logData) {
    const { randomUUID } = require('crypto');
    return await this.create({
        logId: randomUUID(),
        electionId: logData.electionId,
        actionType: logData.actionType,
        actorId: logData.actorId,
        targetId: logData.targetId,
        details: logData.details,
        guildId: logData.guildId,
        timestamp: new Date()
    });
};
electionAuditLogSchema.pre('save', function () {
    if (!this.timestamp) {
        this.timestamp = new Date();
    }
    if (!this.details || (typeof this.details === 'object' && Object.keys(this.details).length === 0)) {
        this.invalidate('details', 'Details cannot be empty');
    }
});
const ElectionAuditLog = (0, mongoose_1.model)('ElectionAuditLog', electionAuditLogSchema);
exports.default = ElectionAuditLog;
