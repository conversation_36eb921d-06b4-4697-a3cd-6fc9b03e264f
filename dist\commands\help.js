"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMainHelpEmbed = createMainHelpEmbed;
exports.createCategoryHelpEmbed = createCategoryHelpEmbed;
exports.createCommandDetailEmbed = createCommandDetailEmbed;
exports.createGettingStartedEmbed = createGettingStartedEmbed;
exports.createMainHelpButtons = createMainHelpButtons;
exports.createCategoryButtons = createCategoryButtons;
exports.createCommandButtons = createCommandButtons;
exports.getCommandData = getCommandData;
exports.isUserAdmin = isUserAdmin;
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const incomeGuideService_1 = require("../services/incomeGuideService");
const embedBuilder_1 = require("../utils/embedBuilder");
const configurableConstants_1 = __importDefault(require("../config/configurableConstants"));
const helpStateManager_1 = require("../utils/helpStateManager");
async function getCommandData(guildId) {
    const timestamp = new Date().toISOString();
    try {
        console.log(`[HELP DEBUG ${timestamp}] ===== getCommandData() START =====`);
        console.log(`[HELP DEBUG ${timestamp}] Getting command data for guild: ${guildId}`);
        console.log(`[HELP DEBUG ${timestamp}] Calling ConfigurableConstants.getCoinName()...`);
        const coinName = await configurableConstants_1.default.getCoinName(guildId);
        console.log(`[HELP DEBUG ${timestamp}] ✅ getCoinName() returned: "${coinName}" for guild: ${guildId}`);
        console.log(`[HELP DEBUG ${timestamp}] Calling ConfigurableConstants.getCoinSymbol()...`);
        const coinSymbol = await configurableConstants_1.default.getCoinSymbol(guildId);
        console.log(`[HELP DEBUG ${timestamp}] ✅ getCoinSymbol() returned: "${coinSymbol}" for guild: ${guildId}`);
        return [
            {
                name: 'Economy',
                description: 'Manage your coins and economy interactions',
                emoji: '💰',
                commands: [
                    {
                        name: 'balance',
                        description: `Check your current ${coinName} balance`,
                        category: 'economy',
                        adminOnly: false,
                        usage: '/balance',
                        examples: ['/balance'],
                        cooldown: 3
                    },
                    {
                        name: 'pay',
                        description: `Transfer ${coinName} to another user`,
                        category: 'economy',
                        adminOnly: false,
                        usage: '/pay <user> <amount> [reason]',
                        examples: [
                            '/pay @user 100',
                            '/pay @user 50 "Thanks for helping!"'
                        ],
                        parameters: [
                            { name: 'user', description: 'The user to pay', required: true, type: 'User' },
                            { name: 'amount', description: 'Amount to pay', required: true, type: 'Integer' },
                            { name: 'reason', description: 'Optional reason for payment', required: false, type: 'String' }
                        ],
                        cooldown: 5
                    },
                    {
                        name: 'history',
                        description: 'View your recent transaction history',
                        category: 'economy',
                        adminOnly: false,
                        usage: '/history [page]',
                        examples: ['/history', '/history 2'],
                        parameters: [
                            { name: 'page', description: 'Page number to view', required: false, type: 'Integer' }
                        ]
                    },
                    {
                        name: 'leaderboard',
                        description: 'View the server wealth leaderboard',
                        category: 'economy',
                        adminOnly: false,
                        usage: '/leaderboard [page]',
                        examples: ['/leaderboard', '/leaderboard 2'],
                        parameters: [
                            { name: 'page', description: 'Page number to view', required: false, type: 'Integer' }
                        ]
                    },
                    {
                        name: 'trade',
                        description: 'Secure trading system for coins and items',
                        category: 'economy',
                        adminOnly: false,
                        usage: '/trade <subcommand>',
                        examples: [
                            '/trade sell @buyer 100 "My Item" "Great condition"',
                            '/trade buy @seller 100 "Their Item"',
                            '/trade list',
                            '/trade cancel 123'
                        ],
                        parameters: [
                            { name: 'subcommand', description: 'sell, buy, list, cancel, or accept', required: true, type: 'String' }
                        ]
                    }
                ]
            },
            {
                name: 'Roles & Achievements',
                description: 'Role-based achievements and progression system',
                emoji: '🏆',
                commands: [
                    {
                        name: 'roles',
                        description: 'View all role achievements and your progress',
                        category: 'roles',
                        adminOnly: false,
                        usage: '/roles',
                        examples: ['/roles']
                    },
                    {
                        name: 'buyrole',
                        description: 'Purchase an available role achievement',
                        category: 'roles',
                        adminOnly: false,
                        usage: '/buyrole <role>',
                        examples: ['/buyrole @VIP'],
                        parameters: [
                            { name: 'role', description: 'The role to purchase', required: true, type: 'Role' }
                        ]
                    }
                ]
            },
            {
                name: 'Utility & Features',
                description: 'Server utilities and special features',
                emoji: '🛠️',
                commands: [
                    {
                        name: 'poll',
                        description: 'Create wealth-based voting polls',
                        category: 'utility',
                        adminOnly: false,
                        usage: '/poll <roles> <title> <options>',
                        examples: ['/poll "@Members @VIP" "Server Event?" "Option 1|Option 2|Option 3"'],
                        parameters: [
                            { name: 'roles', description: 'Roles that can vote', required: true, type: 'String' },
                            { name: 'title', description: 'Poll question/title', required: true, type: 'String' },
                            { name: 'options', description: 'Poll options separated by |', required: true, type: 'String' }
                        ],
                        requiredPermissions: ['ManageMessages']
                    },
                    {
                        name: 'elections',
                        description: 'Create wealth-based elections',
                        category: 'utility',
                        adminOnly: false,
                        usage: '/elections <title> <roles_to_ping> <candidates>',
                        examples: ['/elections "Server Leader Election" "@Members" "Candidate1|Candidate2"'],
                        parameters: [
                            { name: 'title', description: 'Election title', required: true, type: 'String' },
                            { name: 'roles_to_ping', description: 'Roles to notify', required: true, type: 'String' },
                            { name: 'candidates', description: 'Candidates separated by |', required: true, type: 'String' }
                        ],
                        requiredPermissions: ['ManageMessages']
                    },
                    {
                        name: 'placeholders',
                        description: 'View available placeholders for messages',
                        category: 'utility',
                        adminOnly: false,
                        usage: '/placeholders',
                        examples: ['/placeholders']
                    }
                ]
            },
            {
                name: 'Milestones',
                description: 'Automated milestone and achievement system',
                emoji: '🎯',
                commands: [
                    {
                        name: 'milestone',
                        description: 'Manage automated milestone reward system',
                        category: 'milestone',
                        adminOnly: true,
                        usage: '/milestone <subcommand>',
                        examples: [
                            '/milestone status',
                            '/milestone enable time_based',
                            '/milestone disable participation_diversity'
                        ],
                        parameters: [
                            { name: 'subcommand', description: 'status, enable, disable, configure, or leaderboard', required: true, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'milestones',
                        description: 'View your milestone progress',
                        category: 'milestone',
                        adminOnly: false,
                        usage: '/milestones [user]',
                        examples: ['/milestones', '/milestones @user'],
                        parameters: [
                            { name: 'user', description: 'User to check milestones for', required: false, type: 'User' }
                        ]
                    },
                    {
                        name: 'milestonestatus',
                        description: 'Check milestone system status',
                        category: 'milestone',
                        adminOnly: false,
                        usage: '/milestonestatus',
                        examples: ['/milestonestatus']
                    }
                ]
            },
            {
                name: 'Administration',
                description: 'Server administration and management commands',
                emoji: '⚙️',
                commands: [
                    {
                        name: 'setup',
                        description: 'Configure server economy settings',
                        category: 'admin',
                        adminOnly: true,
                        usage: '/setup <nationname> <coinname> [coinsymbol] [embedcolor]',
                        examples: [
                            '/setup "Dragon Empire" "Dragon Coins" "DC" "#FF0000"',
                            '/setup "Star Alliance" "Alliance Credits" "AC"',
                            '/setup "Phoenix Guild" "Phoenix Tokens"'
                        ],
                        parameters: [
                            { name: 'nationname', description: 'Name of your nation/organization', required: true, type: 'String' },
                            { name: 'coinname', description: 'Name of your currency', required: true, type: 'String' },
                            { name: 'coinsymbol', description: 'Currency symbol/abbreviation', required: false, type: 'String' },
                            { name: 'embedcolor', description: 'Embed color for server messages', required: false, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'give',
                        description: 'Give coins to a user',
                        category: 'admin',
                        adminOnly: true,
                        usage: '/give <user> <amount> [reason]',
                        examples: [
                            '/give @user 1000',
                            '/give @user 500 "Event reward"'
                        ],
                        parameters: [
                            { name: 'user', description: 'User to give coins to', required: true, type: 'User' },
                            { name: 'amount', description: 'Amount of coins to give', required: true, type: 'Integer' },
                            { name: 'reason', description: 'Reason for giving coins', required: false, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'fine',
                        description: 'Remove coins from a user (fine them)',
                        category: 'admin',
                        adminOnly: true,
                        usage: '/fine <user> <amount> [reason]',
                        examples: [
                            '/fine @user 100',
                            '/fine @user 250 "Rule violation"'
                        ],
                        parameters: [
                            { name: 'user', description: 'User to fine', required: true, type: 'User' },
                            { name: 'amount', description: 'Amount of coins to remove', required: true, type: 'Integer' },
                            { name: 'reason', description: 'Reason for the fine', required: false, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'auditlog',
                        description: 'Configure audit logging for economy transactions',
                        category: 'admin',
                        adminOnly: true,
                        usage: '/auditlog <subcommand>',
                        examples: [
                            '/auditlog setup #audit-channel',
                            '/auditlog disable',
                            '/auditlog status'
                        ],
                        parameters: [
                            { name: 'subcommand', description: 'setup, disable, or status', required: true, type: 'String' },
                            { name: 'channel', description: 'Channel for audit logs (setup only)', required: false, type: 'Channel' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'tax',
                        description: 'Configure automatic taxation system',
                        category: 'admin',
                        adminOnly: true,
                        usage: '/tax <subcommand>',
                        examples: [
                            '/tax enable 5',
                            '/tax disable',
                            '/tax status'
                        ],
                        parameters: [
                            { name: 'subcommand', description: 'enable, disable, or status', required: true, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'addrole',
                        description: 'Add a new role achievement',
                        category: 'admin',
                        adminOnly: true,
                        usage: '/addrole <role> <cost> [description]',
                        examples: [
                            '/addrole @VIP 1000',
                            '/addrole @Premium 5000 "Premium member benefits"'
                        ],
                        parameters: [
                            { name: 'role', description: 'Role to add as achievement', required: true, type: 'Role' },
                            { name: 'cost', description: 'Cost in coins to purchase', required: true, type: 'Integer' },
                            { name: 'description', description: 'Role description', required: false, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'editrole',
                        description: 'Edit an existing role achievement',
                        category: 'admin',
                        adminOnly: true,
                        usage: '/editrole <role> [cost] [description]',
                        examples: ['/editrole @VIP 1500 "Updated VIP benefits"'],
                        parameters: [
                            { name: 'role', description: 'Role to edit', required: true, type: 'Role' },
                            { name: 'cost', description: 'New cost in coins', required: false, type: 'Integer' },
                            { name: 'description', description: 'New description', required: false, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'removerole',
                        description: 'Remove a role achievement',
                        category: 'admin',
                        adminOnly: true,
                        usage: '/removerole <role>',
                        examples: ['/removerole @OldRole'],
                        parameters: [
                            { name: 'role', description: 'Role to remove', required: true, type: 'Role' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'richestrole',
                        description: `Assign role to user with highest ${coinName} balance`,
                        category: 'admin',
                        adminOnly: true,
                        usage: '/richestrole <role>',
                        examples: ['/richestrole @Richest'],
                        parameters: [
                            { name: 'role', description: 'Role to assign to richest user', required: true, type: 'Role' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'starterbalance',
                        description: 'Manage starter balance rules for roles',
                        category: 'admin',
                        adminOnly: true,
                        usage: '/starterbalance <subcommand>',
                        examples: [
                            '/starterbalance add @NewMember 100',
                            '/starterbalance remove @OldRole',
                            '/starterbalance list'
                        ],
                        parameters: [
                            { name: 'subcommand', description: 'add, remove, or list', required: true, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    }
                ]
            },
            {
                name: 'Automation',
                description: 'Automated messages and server features',
                emoji: '🤖',
                commands: [
                    {
                        name: 'automessage',
                        description: 'Configure automated messages for server events',
                        category: 'automation',
                        adminOnly: true,
                        usage: '/automessage <subcommand>',
                        examples: [
                            '/automessage add welcome "Welcome {user}!"',
                            '/automessage list',
                            '/automessage remove welcome'
                        ],
                        parameters: [
                            { name: 'subcommand', description: 'add, remove, list, or test', required: true, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'editautomessage',
                        description: 'Edit existing automated messages',
                        category: 'automation',
                        adminOnly: true,
                        usage: '/editautomessage <event> <message>',
                        examples: ['/editautomessage welcome "Welcome to our server, {user}!"'],
                        parameters: [
                            { name: 'event', description: 'Event type to edit', required: true, type: 'String' },
                            { name: 'message', description: 'New message content', required: true, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'monetizechannel',
                        description: 'Configure channel monetization settings',
                        category: 'automation',
                        adminOnly: true,
                        usage: '/monetizechannel <channel> <amount>',
                        examples: ['/monetizechannel #general 5'],
                        parameters: [
                            { name: 'channel', description: 'Channel to monetize', required: true, type: 'Channel' },
                            { name: 'amount', description: 'Coins earned per message', required: true, type: 'Integer' }
                        ],
                        requiredPermissions: ['Administrator']
                    },
                    {
                        name: 'incomecredentials',
                        description: 'Customize income earning guide text',
                        category: 'automation',
                        adminOnly: true,
                        usage: '/incomecredentials <text>',
                        examples: ['/incomecredentials "Earn coins by being active!"'],
                        parameters: [
                            { name: 'text', description: 'Custom income guide text', required: true, type: 'String' }
                        ],
                        requiredPermissions: ['Administrator']
                    }
                ]
            }
        ];
        console.log(`[HELP DEBUG ${timestamp}] ===== getCommandData() SUCCESS =====`);
        console.log(`[HELP DEBUG ${timestamp}] Command data generated with coinName: "${coinName}", coinSymbol: "${coinSymbol}"`);
    }
    catch (error) {
        console.error(`[HELP DEBUG ${timestamp}] ❌ ERROR in getCommandData() for guild ${guildId}:`, error);
        console.log(`[HELP DEBUG ${timestamp}] ⚠️ FALLBACK: Using default command data for guild: ${guildId}`);
        return [
            {
                name: 'Economy',
                description: 'Manage your coins and economy interactions',
                emoji: '💰',
                commands: [
                    {
                        name: 'balance',
                        description: 'Check your current coin balance',
                        category: 'economy',
                        adminOnly: false,
                        usage: '/balance',
                        examples: ['/balance'],
                        cooldown: 3
                    },
                    {
                        name: 'pay',
                        description: 'Transfer coins to another user',
                        category: 'economy',
                        adminOnly: false,
                        usage: '/pay <user> <amount> [reason]',
                        examples: ['/pay @user 100', '/pay @user 50 "Thanks for helping!"'],
                        parameters: [
                            { name: 'user', description: 'The user to pay', required: true, type: 'User' },
                            { name: 'amount', description: 'Amount to pay', required: true, type: 'Integer' },
                            { name: 'reason', description: 'Optional reason for payment', required: false, type: 'String' }
                        ],
                        cooldown: 5
                    }
                ]
            }
        ];
    }
}
async function createMainHelpEmbed(guildId) {
    const timestamp = new Date().toISOString();
    try {
        console.log(`[HELP DEBUG ${timestamp}] ===== createMainHelpEmbed() START =====`);
        console.log(`[HELP DEBUG ${timestamp}] Creating main help embed for guild: ${guildId}`);
        console.log(`[HELP DEBUG ${timestamp}] Calling getCommandData()...`);
        const categories = await getCommandData(guildId);
        console.log(`[HELP DEBUG ${timestamp}] ✅ getCommandData() returned ${categories.length} categories for guild: ${guildId}`);
        console.log(`[HELP DEBUG ${timestamp}] Calling ConfigurableConstants.getNationName()...`);
        const nationName = await configurableConstants_1.default.getNationName(guildId);
        console.log(`[HELP DEBUG ${timestamp}] ✅ getNationName() returned: "${nationName}" for guild: ${guildId}`);
        console.log(`[HELP DEBUG ${timestamp}] Calling ConfigurableConstants.getCoinName()...`);
        const coinName = await configurableConstants_1.default.getCoinName(guildId);
        console.log(`[HELP DEBUG ${timestamp}] ✅ getCoinName() returned: "${coinName}" for guild: ${guildId}`);
        console.log(`[HELP DEBUG ${timestamp}] Creating embed with title: "${nationName} - Command Help"`);
        const embed = await (0, embedBuilder_1.createServerBaseEmbed)(guildId, `${nationName} - Command Help`);
        console.log(`[HELP DEBUG ${timestamp}] ✅ createServerBaseEmbed() completed for guild: ${guildId}`);
        const description = `Welcome to the **${nationName}** economy bot! 🎉\n\nSelect a category below to explore available commands.`;
        console.log(`[HELP DEBUG ${timestamp}] Setting embed description: "${description}"`);
        embed.setDescription(description);
        console.log(`[HELP DEBUG ${timestamp}] ✅ Embed description set with nationName: "${nationName}" for guild: ${guildId}`);
        categories.forEach((category, index) => {
            const commandCount = category.commands.length;
            embed.addFields({
                name: `${category.emoji} ${category.name}`,
                value: `${category.description}\n*${commandCount} command${commandCount !== 1 ? 's' : ''}*`,
                inline: true
            });
        });
        const gettingStartedValue = `New to ${nationName}? Start with \`/balance\` to check your ${coinName}!`;
        console.log(`[HELP DEBUG ${timestamp}] Adding getting started field with value: "${gettingStartedValue}"`);
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.ECONOMY.SPARKLES} Getting Started`,
            value: gettingStartedValue,
            inline: false
        });
        console.log(`[HELP DEBUG ${timestamp}] ✅ Getting started field added with nationName: "${nationName}" and coinName: "${coinName}" for guild: ${guildId}`);
        embed.setFooter({ text: 'Use the buttons below to navigate • Help system v2.0' });
        console.log(`[HELP DEBUG ${timestamp}] ===== createMainHelpEmbed() SUCCESS =====`);
        console.log(`[HELP DEBUG ${timestamp}] Final embed title: "${embed.data.title}"`);
        console.log(`[HELP DEBUG ${timestamp}] Final embed description preview: "${embed.data.description?.substring(0, 100)}..."`);
        return embed;
    }
    catch (error) {
        console.error(`[HELP DEBUG ${timestamp}] ❌ ERROR in createMainHelpEmbed() for guild ${guildId}:`, error);
        const fallbackEmbed = new discord_js_1.EmbedBuilder()
            .setTitle('Help System')
            .setDescription('Welcome to the help system! There was an issue loading server-specific information.')
            .setColor('#0099ff')
            .setFooter({ text: 'Help system v2.0' });
        return fallbackEmbed;
    }
}
async function createCategoryHelpEmbed(guildId, categoryIndex, userIsAdmin) {
    const categories = await getCommandData(guildId);
    const category = categories[categoryIndex];
    if (!category) {
        throw new Error('Invalid category index');
    }
    const embed = await (0, embedBuilder_1.createServerBaseEmbed)(guildId, `${category.emoji} ${category.name} Commands`);
    embed.setDescription(category.description);
    const visibleCommands = category.commands.filter(cmd => !cmd.adminOnly || userIsAdmin);
    if (visibleCommands.length === 0) {
        embed.addFields({
            name: 'No Available Commands',
            value: 'You don\'t have permission to use any commands in this category.',
            inline: false
        });
    }
    else {
        visibleCommands.forEach((command, index) => {
            const permissionText = command.adminOnly ? ' 🔒' : '';
            const cooldownText = command.cooldown ? ` • ${command.cooldown}s cooldown` : '';
            embed.addFields({
                name: `/${command.name}${permissionText}`,
                value: `${command.description}\n\`${command.usage}\`${cooldownText}`,
                inline: false
            });
        });
    }
    embed.setFooter({ text: `Page ${categoryIndex + 1}/${categories.length} • Click a command button for details` });
    return embed;
}
async function createCommandDetailEmbed(guildId, categoryIndex, commandIndex) {
    const categories = await getCommandData(guildId);
    const category = categories[categoryIndex];
    const command = category?.commands[commandIndex];
    if (!command) {
        throw new Error('Invalid command index');
    }
    const embed = await (0, embedBuilder_1.createServerBaseEmbed)(guildId, `/${command.name}`);
    embed.setDescription(command.description);
    embed.addFields({
        name: '📝 Usage',
        value: `\`${command.usage}\``,
        inline: false
    });
    if (command.parameters && command.parameters.length > 0) {
        const paramText = command.parameters.map(param => {
            const required = param.required ? '**Required**' : '*Optional*';
            return `• **${param.name}** (${param.type}) - ${required}\n  ${param.description}`;
        }).join('\n');
        embed.addFields({
            name: '⚙️ Parameters',
            value: paramText,
            inline: false
        });
    }
    if (command.examples && command.examples.length > 0) {
        embed.addFields({
            name: '💡 Examples',
            value: command.examples.map(example => `\`${example}\``).join('\n'),
            inline: false
        });
    }
    const additionalInfo = [];
    if (command.cooldown) {
        additionalInfo.push(`⏱️ Cooldown: ${command.cooldown} seconds`);
    }
    if (command.adminOnly) {
        additionalInfo.push('🔒 Administrator only');
    }
    if (command.requiredPermissions && command.requiredPermissions.length > 0) {
        additionalInfo.push(`🛡️ Required: ${command.requiredPermissions.join(', ')}`);
    }
    if (additionalInfo.length > 0) {
        embed.addFields({
            name: 'ℹ️ Additional Information',
            value: additionalInfo.join('\n'),
            inline: false
        });
    }
    embed.setFooter({ text: `${category.emoji} ${category.name} • Use buttons to navigate` });
    return embed;
}
async function createMainHelpButtons(guildId, userIsAdmin) {
    console.log(`[HELP BUTTON DEBUG] Creating main help buttons for guild: ${guildId}, isAdmin: ${userIsAdmin}`);
    const categories = await getCommandData(guildId);
    const rows = [];
    console.log(`[HELP BUTTON DEBUG] Retrieved ${categories.length} categories for button generation`);
    for (let i = 0; i < categories.length; i += 5) {
        const row = new discord_js_1.ActionRowBuilder();
        const slice = categories.slice(i, i + 5);
        slice.forEach((category, index) => {
            const actualIndex = i + index;
            const hasVisibleCommands = category.commands.some(cmd => !cmd.adminOnly || userIsAdmin);
            if (hasVisibleCommands) {
                const customId = `help_category_${actualIndex}`;
                console.log(`[HELP BUTTON DEBUG] Creating category button: "${customId}" for "${category.name}"`);
                row.addComponents(new discord_js_1.ButtonBuilder()
                    .setCustomId(customId)
                    .setLabel(category.name)
                    .setEmoji(category.emoji)
                    .setStyle(discord_js_1.ButtonStyle.Primary));
            }
        });
        if (row.components.length > 0) {
            rows.push(row);
        }
    }
    const utilityRow = new discord_js_1.ActionRowBuilder()
        .addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId('help_getting_started')
        .setLabel('Getting Started Guide')
        .setEmoji('🚀')
        .setStyle(discord_js_1.ButtonStyle.Success));
    rows.push(utilityRow);
    return rows;
}
async function createCategoryButtons(guildId, categoryIndex, userIsAdmin) {
    const categories = await getCommandData(guildId);
    const category = categories[categoryIndex];
    const rows = [];
    if (!category)
        return rows;
    const visibleCommands = category.commands.filter(cmd => !cmd.adminOnly || userIsAdmin);
    for (let i = 0; i < visibleCommands.length; i += 5) {
        const row = new discord_js_1.ActionRowBuilder();
        const slice = visibleCommands.slice(i, i + 5);
        slice.forEach((command, index) => {
            const actualIndex = category.commands.findIndex(cmd => cmd.name === command.name);
            row.addComponents(new discord_js_1.ButtonBuilder()
                .setCustomId(`help_command_${categoryIndex}_${actualIndex}`)
                .setLabel(`/${command.name}`)
                .setStyle(command.adminOnly ? discord_js_1.ButtonStyle.Danger : discord_js_1.ButtonStyle.Secondary));
        });
        rows.push(row);
    }
    const navRow = new discord_js_1.ActionRowBuilder()
        .addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId('help_back_main')
        .setLabel('← Back to Main')
        .setStyle(discord_js_1.ButtonStyle.Primary));
    if (categoryIndex > 0) {
        navRow.addComponents(new discord_js_1.ButtonBuilder()
            .setCustomId(`help_category_${categoryIndex - 1}`)
            .setLabel('← Previous')
            .setStyle(discord_js_1.ButtonStyle.Secondary));
    }
    if (categoryIndex < categories.length - 1) {
        navRow.addComponents(new discord_js_1.ButtonBuilder()
            .setCustomId(`help_category_${categoryIndex + 1}`)
            .setLabel('Next →')
            .setStyle(discord_js_1.ButtonStyle.Secondary));
    }
    rows.push(navRow);
    return rows;
}
async function createCommandButtons(guildId, categoryIndex, commandIndex) {
    const categories = await getCommandData(guildId);
    const category = categories[categoryIndex];
    const rows = [];
    if (!category)
        return rows;
    const navRow = new discord_js_1.ActionRowBuilder()
        .addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId(`help_category_${categoryIndex}`)
        .setLabel(`← Back to ${category.name}`)
        .setStyle(discord_js_1.ButtonStyle.Primary), new discord_js_1.ButtonBuilder()
        .setCustomId('help_back_main')
        .setLabel('🏠 Main Menu')
        .setStyle(discord_js_1.ButtonStyle.Secondary));
    if (commandIndex > 0) {
        navRow.addComponents(new discord_js_1.ButtonBuilder()
            .setCustomId(`help_command_${categoryIndex}_${commandIndex - 1}`)
            .setLabel('← Previous')
            .setStyle(discord_js_1.ButtonStyle.Secondary));
    }
    if (commandIndex < category.commands.length - 1) {
        navRow.addComponents(new discord_js_1.ButtonBuilder()
            .setCustomId(`help_command_${categoryIndex}_${commandIndex + 1}`)
            .setLabel('Next →')
            .setStyle(discord_js_1.ButtonStyle.Secondary));
    }
    rows.push(navRow);
    return rows;
}
function isUserAdmin(interaction) {
    if (!interaction.memberPermissions)
        return false;
    return interaction.memberPermissions.has(discord_js_1.PermissionFlagsBits.Administrator);
}
async function createGettingStartedEmbed(guildId) {
    const nationName = await configurableConstants_1.default.getNationName(guildId);
    const coinName = await configurableConstants_1.default.getCoinName(guildId);
    const incomeGuideText = await (0, incomeGuideService_1.getIncomeGuideText)(guildId);
    const embed = await (0, embedBuilder_1.createServerBaseEmbed)(guildId, `🚀 Getting Started with ${nationName}`);
    embed.setDescription(`Welcome to **${nationName}**! Here's everything you need to know to get started with our economy system.`);
    embed.addFields({
        name: '💰 Your First Steps',
        value: [
            `• Use \`/balance\` to check your current ${coinName}`,
            `• Use \`/history\` to see your transaction history`,
            `• Use \`/leaderboard\` to see the richest members`,
            `• Use \`/roles\` to see available role achievements`
        ].join('\n'),
        inline: false
    }, {
        name: '🏆 Earning Coins',
        value: incomeGuideText || `Earn ${coinName} by participating in server activities!`,
        inline: false
    }, {
        name: '🎯 Role Achievements',
        value: [
            `• Purchase special roles with your ${coinName}`,
            `• Use \`/buyrole @RoleName\` to purchase a role`,
            `• Each role has different costs and benefits`
        ].join('\n'),
        inline: false
    }, {
        name: '🤝 Trading & Payments',
        value: [
            `• Send ${coinName} to other users with \`/pay @user amount\``,
            `• Use the secure \`/trade\` system for item exchanges`,
            `• All transactions are logged in your history`
        ].join('\n'),
        inline: false
    }, {
        name: '🛠️ Advanced Features',
        value: [
            '• Participate in wealth-based polls and elections',
            '• Track your milestone progress',
            '• Explore automation features (admin only)'
        ].join('\n'),
        inline: false
    });
    embed.setFooter({ text: 'Need help with a specific command? Use the category buttons!' });
    return embed;
}
const helpCommand = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('help')
        .setDescription('Comprehensive help system with detailed command information'),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        try {
            console.log(`[HELP DEBUG] Help command executed by user: ${interaction.user.id}`);
            if (!interaction.guild) {
                throw new errorHandler_1.CommandError('This command can only be used in a server.');
            }
            const guildId = interaction.guild.id;
            const userId = interaction.user.id;
            const userIsAdmin = isUserAdmin(interaction);
            console.log(`[HELP DEBUG] Guild: ${guildId}, User: ${userId}, IsAdmin: ${userIsAdmin}`);
            console.log(`[HELP DEBUG] Creating help state for user: ${userId}, guild: ${guildId}`);
            (0, helpStateManager_1.setHelpState)(userId, guildId, {
                currentPage: 'main',
                userId,
                guildId
            });
            console.log(`[HELP DEBUG] Help state created successfully`);
            console.log(`[HELP DEBUG] Creating main help embed...`);
            const embed = await createMainHelpEmbed(guildId);
            console.log(`[HELP DEBUG] Main help embed created successfully`);
            console.log(`[HELP DEBUG] Creating main help buttons...`);
            const buttons = await createMainHelpButtons(guildId, userIsAdmin);
            console.log(`[HELP DEBUG] Main help buttons created successfully`);
            console.log(`[HELP DEBUG] Sending help response...`);
            await interaction.reply({
                embeds: [embed],
                components: buttons,
                ephemeral: false
            });
            console.log(`[HELP DEBUG] Help response sent successfully`);
        }
        catch (error) {
            if (error instanceof Error) {
                throw new errorHandler_1.CommandError(error.message);
            }
            throw new errorHandler_1.CommandError('An error occurred while showing help.');
        }
    })
};
module.exports = {
    ...helpCommand,
    createMainHelpEmbed,
    createCategoryHelpEmbed,
    createCommandDetailEmbed,
    createGettingStartedEmbed,
    createMainHelpButtons,
    createCategoryButtons,
    createCommandButtons,
    getCommandData,
    isUserAdmin
};
