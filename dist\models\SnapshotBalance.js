"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const snapshotBalanceSchema = new mongoose_1.Schema({
    snapshotId: {
        type: String,
        required: [true, 'Snapshot ID is required'],
        unique: true,
        index: true
    },
    electionId: {
        type: String,
        required: [true, 'Election ID is required'],
        validate: {
            validator: function (v) {
                return v && v.trim().length > 0;
            },
            message: 'Election ID cannot be empty'
        },
        index: true
    },
    userId: {
        type: String,
        required: [true, 'User ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'User ID must be a valid Discord snowflake'
        },
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    balance: {
        type: Number,
        required: [true, 'Balance is required'],
        min: [0, 'Balance cannot be negative'],
        validate: {
            validator: function (v) {
                return Number.isInteger(v);
            },
            message: 'Balance must be an integer'
        }
    },
    recordedAt: {
        type: Date,
        required: [true, 'Recorded at timestamp is required'],
        index: true,
        immutable: true
    },
    proofHash: {
        type: String,
        required: [true, 'Proof hash is required'],
        validate: {
            validator: function (v) {
                return /^[a-f0-9]{64}$/.test(v);
            },
            message: 'Proof hash must be a valid SHA-256 hash'
        },
        immutable: true
    }
}, {
    timestamps: false,
    collection: 'snapshotbalances',
    versionKey: false
});
snapshotBalanceSchema.index({ electionId: 1, userId: 1 }, { unique: true });
snapshotBalanceSchema.index({ electionId: 1, balance: -1 });
snapshotBalanceSchema.index({ guildId: 1, electionId: 1 });
snapshotBalanceSchema.index({ recordedAt: 1 });
snapshotBalanceSchema.statics.generateProofHash = function (userId, balance, recordedAt, electionId) {
    const crypto = require('crypto');
    const proofData = `${userId}:${balance}:${recordedAt.toISOString()}:${electionId}`;
    return crypto.createHash('sha256').update(proofData).digest('hex');
};
snapshotBalanceSchema.methods.generateProofHash = function () {
    const crypto = require('crypto');
    const proofData = `${this.userId}:${this.balance}:${this.recordedAt.toISOString()}:${this.electionId}`;
    return crypto.createHash('sha256').update(proofData).digest('hex');
};
snapshotBalanceSchema.methods.validateProofHash = function () {
    const expectedHash = this.generateProofHash();
    return this.proofHash === expectedHash;
};
snapshotBalanceSchema.pre('save', function () {
    if (this.isNew) {
        const expectedHash = this.generateProofHash();
        if (!this.proofHash) {
            this.proofHash = expectedHash;
        }
        else if (this.proofHash !== expectedHash) {
            this.invalidate('proofHash', 'Proof hash does not match the expected hash');
        }
        if (!this.recordedAt) {
            this.recordedAt = new Date();
        }
    }
});
snapshotBalanceSchema.pre(['updateOne', 'updateMany', 'findOneAndUpdate'], function () {
    throw new Error('SnapshotBalance records are immutable and cannot be updated');
});
snapshotBalanceSchema.pre('deleteOne', function () {
    throw new Error('SnapshotBalance records are immutable and cannot be deleted');
});
snapshotBalanceSchema.pre('deleteMany', function () {
    throw new Error('SnapshotBalance records are immutable and cannot be deleted');
});
const SnapshotBalance = (0, mongoose_1.model)('SnapshotBalance', snapshotBalanceSchema);
exports.default = SnapshotBalance;
