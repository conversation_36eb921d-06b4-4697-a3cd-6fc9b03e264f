"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElectionsCommand = void 0;
const discord_js_1 = require("discord.js");
const BaseCommand_1 = require("../base/BaseCommand");
const errorHandler_1 = require("../../utils/errorHandler");
const electionEmbedBuilder_1 = require("../../utils/electionEmbedBuilder");
class ElectionsCommand extends BaseCommand_1.BaseCommand {
    constructor() {
        super({
            name: 'elections',
            description: 'Create a wealth-based voting election in your server',
            category: BaseCommand_1.CommandCategory.UTILITY,
            requiredFeatures: ['ECONOMY_SYSTEM'],
            guildOnly: true,
            requiredPermissions: ['ManageMessages']
        });
    }
    customizeCommand(command) {
        command
            .setDefaultMemberPermissions(discord_js_1.PermissionFlagsBits.ManageMessages)
            .addStringOption(option => option
            .setName('title')
            .setDescription('Title of the election')
            .setRequired(true)
            .setMaxLength(100))
            .addStringOption(option => option
            .setName('roles_to_ping')
            .setDescription('Roles to notify about the election (space-separated role IDs or mentions)')
            .setRequired(true))
            .addStringOption(option => option
            .setName('eligible_voter_roles')
            .setDescription('Roles that can vote (space-separated role IDs or mentions)')
            .setRequired(true))
            .addStringOption(option => option
            .setName('eligible_candidate_roles')
            .setDescription('Roles that can become candidates (space-separated role IDs or mentions)')
            .setRequired(true))
            .addStringOption(option => option
            .setName('description')
            .setDescription('Optional description of the election')
            .setRequired(false)
            .setMaxLength(500))
            .addBooleanOption(option => option
            .setName('allow_vote_changes')
            .setDescription('Allow users to change their votes (default: true)')
            .setRequired(false))
            .addBooleanOption(option => option
            .setName('show_vote_weights')
            .setDescription('Show vote weights publicly (default: true)')
            .setRequired(false))
            .addStringOption(option => option
            .setName('candidates')
            .setDescription('Specify candidates by mentioning users (e.g., @user1 @user2 @user3)')
            .setRequired(false)
            .setMaxLength(1000));
    }
    async initialize(app) {
        try {
            this.electionService = app.getService('ElectionService');
            if (!this.electionService) {
                throw new Error('ElectionService not available in application context');
            }
            console.log('✅ ElectionsCommand initialized with ElectionService');
        }
        catch (error) {
            console.error('❌ Failed to initialize ElectionsCommand:', error);
            throw error;
        }
    }
    setElectionService(electionService) {
        this.electionService = electionService;
        console.log('✅ ElectionService set via setter method');
    }
    async executeCommand(context) {
        const { interaction, guild } = context;
        if (!guild) {
            throw new errorHandler_1.ValidationError('This command can only be used in a server.');
        }
        if (!this.electionService) {
            console.error('❌ ElectionService not available in ElectionsCommand - ERR-MEH22HZJ-IXZW2');
            this.logger.error('[ElectionsCommand] ElectionService not available', {
                userId: interaction.user.id,
                guildId: guild.id,
                hasService: !!this.electionService,
                errorRef: 'ERR-MEH22HZJ-IXZW2'
            });
            throw new errorHandler_1.ValidationError('Election service is not available. Please try the command again in a moment or contact an administrator. (Error Reference: ERR-MEH22HZJ-IXZW2)');
        }
        const title = interaction.options.getString('title', true);
        const rolesStringToPing = interaction.options.getString('roles_to_ping', true);
        const eligibleVoterRolesString = interaction.options.getString('eligible_voter_roles', true);
        const eligibleCandidateRolesString = interaction.options.getString('eligible_candidate_roles', true);
        const description = interaction.options.getString('description');
        const allowVoteChanges = interaction.options.getBoolean('allow_vote_changes') ?? true;
        const showVoteWeights = interaction.options.getBoolean('show_vote_weights') ?? true;
        const candidatesString = interaction.options.getString('candidates');
        try {
            const rolesToPing = this.parseRoles(rolesStringToPing, guild);
            const eligibleVoterRoles = this.parseRoles(eligibleVoterRolesString, guild);
            const eligibleCandidateRoles = this.parseRoles(eligibleCandidateRolesString, guild);
            await this.validateRoles(rolesToPing, guild, 'roles to ping');
            await this.validateRoles(eligibleVoterRoles, guild, 'eligible voter roles');
            await this.validateRoles(eligibleCandidateRoles, guild, 'eligible candidate roles');
            await interaction.deferReply();
            const initialEmbed = {
                title: '🗳️ Creating Election...',
                description: 'Please wait while your election is being created.',
                color: 0xffff00
            };
            const reply = await interaction.editReply({ embeds: [initialEmbed] });
            const election = await this.electionService.createElection(guild.id, interaction.channel.id, interaction.user.id, {
                title,
                description: description || undefined,
                rolesToPing,
                eligibleVoterRoles,
                eligibleCandidateRoles,
                allowMultipleVotes: allowVoteChanges,
                showVoteWeights
            });
            let candidates = [];
            if (candidatesString) {
                candidates = await this.processCandidates(candidatesString, election.electionId, guild);
            }
            const embed = await (0, electionEmbedBuilder_1.createElectionEmbed)(election, candidates, {
                totalVotes: 0,
                totalVoteWeight: 0,
                totalCandidates: candidates.length,
                activeCandidates: candidates.length
            });
            const memberPermissions = interaction.member?.permissions;
            const isAdmin = memberPermissions && typeof memberPermissions !== 'string' && memberPermissions.has(discord_js_1.PermissionFlagsBits.Administrator) || false;
            const isUserCandidate = candidates.some(c => c.discordId === interaction.user.id);
            const buttons = (0, electionEmbedBuilder_1.createElectionButtons)(election.electionId, candidates, interaction.user.id, undefined, isUserCandidate, isAdmin);
            await interaction.editReply({
                content: this.createPingContent(rolesToPing, guild),
                embeds: [embed],
                components: buttons,
                allowedMentions: {
                    roles: rolesToPing
                }
            });
            election.messageId = reply.id;
            await election.save();
            this.logger.info(`Election created successfully: ${election.electionId}`, {
                guildId: guild.id,
                createdBy: interaction.user.id,
                title
            });
        }
        catch (error) {
            this.logger.error('[ElectionsCommand] Failed to create election', {
                error: error instanceof Error ? error.message : 'Unknown error',
                guildId: guild?.id,
                userId: interaction.user.id
            });
            const errorMessage = error instanceof errorHandler_1.ValidationError
                ? error.message
                : 'An error occurred while creating the election. Please try again.';
            if (interaction.deferred) {
                await interaction.editReply({
                    content: `❌ **Error:** ${errorMessage}`,
                    embeds: [],
                    components: []
                });
            }
            else {
                await interaction.reply({
                    content: `❌ **Error:** ${errorMessage}`,
                    ephemeral: true
                });
            }
        }
    }
    parseRoles(rolesString, guild) {
        const rolePattern = /<@&(\d+)>|(\d{17,20})/g;
        const roles = [];
        let match;
        this.logger.debug('[ElectionsCommand] Parsing roles from string', { rolesString });
        while ((match = rolePattern.exec(rolesString)) !== null) {
            const roleId = match[1] || match[2];
            if (roleId && !roles.includes(roleId)) {
                const roleIdString = String(roleId);
                roles.push(roleIdString);
                this.logger.debug('[ElectionsCommand] Found role ID', { roleId: roleIdString, type: typeof roleIdString });
            }
        }
        if (roles.length === 0) {
            throw new errorHandler_1.ValidationError(`No valid roles found in: ${rolesString}`);
        }
        this.logger.debug('[ElectionsCommand] Parsed roles', { roles, count: roles.length });
        return roles;
    }
    async validateRoles(roleIds, guild, roleType) {
        const invalidRoles = [];
        for (const roleId of roleIds) {
            const role = guild.roles.cache.get(roleId);
            if (!role) {
                invalidRoles.push(roleId);
            }
        }
        if (invalidRoles.length > 0) {
            throw new errorHandler_1.ValidationError(`Invalid ${roleType}: ${invalidRoles.map(id => `<@&${id}>`).join(', ')}. ` +
                'Make sure the roles exist and the bot has permission to see them.');
        }
    }
    createPingContent(roleIds, guild) {
        const roleMentions = roleIds.map(id => `<@&${id}>`).join(' ');
        return `🗳️ **New Election Started!** ${roleMentions}\n\nA new election has been created. Check the details below and participate!`;
    }
    async processCandidates(candidatesString, electionId, guild) {
        if (!this.electionService) {
            throw new errorHandler_1.ValidationError('Election service not available');
        }
        const userMentionRegex = /<@!?(\d+)>/g;
        const userIds = [];
        let match;
        while ((match = userMentionRegex.exec(candidatesString)) !== null) {
            userIds.push(match[1]);
        }
        if (userIds.length === 0) {
            throw new errorHandler_1.ValidationError('No valid user mentions found in candidates parameter. Use format: @user1 @user2 @user3');
        }
        if (userIds.length > 20) {
            throw new errorHandler_1.ValidationError('Maximum 20 candidates allowed per election');
        }
        const candidates = [];
        const errors = [];
        for (const userId of userIds) {
            try {
                const member = await guild.members.fetch(userId);
                if (!member) {
                    errors.push(`User <@${userId}> not found in server`);
                    continue;
                }
                const canBeCandidate = await this.electionService.canUserBeCandidate(electionId, userId, member);
                if (!canBeCandidate) {
                    errors.push(`<@${userId}> does not have the required role to be a candidate`);
                    continue;
                }
                const candidate = await this.electionService.addCandidate(electionId, userId, member.displayName, member.user.username, undefined);
                candidates.push(candidate);
            }
            catch (error) {
                this.logger.error('[ElectionsCommand] Error processing candidate', {
                    userId,
                    electionId,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
                errors.push(`Failed to add <@${userId}> as candidate: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }
        if (errors.length > 0) {
            this.logger.warn('[ElectionsCommand] Some candidates could not be added', {
                electionId,
                errors,
                successfulCandidates: candidates.length,
                totalAttempted: userIds.length
            });
            if (candidates.length === 0) {
                throw new errorHandler_1.ValidationError(`Failed to add any candidates:\n${errors.join('\n')}`);
            }
        }
        this.logger.info('[ElectionsCommand] Candidates processed successfully', {
            electionId,
            candidatesAdded: candidates.length,
            errors: errors.length
        });
        return candidates;
    }
}
exports.ElectionsCommand = ElectionsCommand;
